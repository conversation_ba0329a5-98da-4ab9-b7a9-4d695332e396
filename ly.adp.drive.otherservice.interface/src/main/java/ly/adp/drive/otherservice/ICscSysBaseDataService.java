package ly.adp.drive.otherservice;

import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

/**
 * 基础服务接口
 * <AUTHOR>
 *
 */
public interface ICscSysBaseDataService {

	/**
	 *  单号生成服务
	 * @param dlrId
	 * @param billTypeId
	 * @param token
	 * @return
	 */
	 ListResult<Map<String, Object>> generateOrderCode(String dlrId, String billTypeId, String token) ;
	/**
	 * 单号生成服务(屏蔽认证)
	 * @param dlrId
	 * @param billTypeId
	 * @return
	 */
	 ListResult<Map<String, Object>> generateOrderCodeNoToken(String dlrId, String billTypeId);

	 ListResult<Map<String, Object>> mdslookupvaluefindbypage(String lookupTypeCode, String token) ;
	/**
	 * 试驾车保存
	 * @param authentication
	 * @param param
	 * @return
	 */

	
	
}
