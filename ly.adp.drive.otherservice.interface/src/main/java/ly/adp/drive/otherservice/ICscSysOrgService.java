package ly.adp.drive.otherservice;

import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.ListResult;

public interface ICscSysOrgService {

	/**
	 * 根据系统岗位获取员工列表
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String,Object>> getEmpListByPosition(String token,ParamPage<Map<String,Object>> mapParam);

	/**
	 * 根据多个用户ID或员工ID，获取员工列表
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String,Object>> getEmpListByIdList(String token,ParamPage<Map<String,Object>> mapParam);

	/**
	 * 构造用户token信息(通过用户id)
	 * @param userId
	 * @return
	 */
	public String generateTokenByUserId(String userId);
	
	/**
	 * 构造用户token信息(通过用户id)
	 * @param userId
	 * @return
	 */
	public String generateTokenByUserIdWithoutSuffix(String userId);

	/**
	 * 根据dlrCode获取专营店信息
	 * @param token
	 * @param dlrCode
	 * @return
	 */
	public Map<String, Object> getOrgDlrInfo(String token, String dlrCode) ;

	public ListResult<Map<String, Object>> getDlrInfoNoToken(ParamPage<Map<String,Object>> mapPage) ;
}
