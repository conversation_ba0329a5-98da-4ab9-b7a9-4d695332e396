package ly.adp.drive.otherservice;

import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

public interface ICscSysDemoCarService {
	public ListResult<Map<String, Object>> DemoCarQueryList(String authentication,ParamPage<Map<String, Object>> mapParam);
	public OptResult democarSave(String authentication,ParamBase<Map<String, Object>> mapParam);
}
