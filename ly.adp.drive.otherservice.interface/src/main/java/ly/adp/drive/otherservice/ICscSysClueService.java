package ly.adp.drive.otherservice;

import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

public interface ICscSysClueService {

	/**
	 * 线索查重
	 * @param mapParam
	 * @return
	 */
	public EntityResult<Map<String, Object>> clueDlrCheckRepeat(String authentication,ParamBase<Map<String, Object>> queryCondition);
	/**
	 * 店端线索保存
	 * @param clueParam
	 * @return
	 */
	public EntityResult<Map<String, Object>> clueDlrSave(String authentication,ParamBase<Map<String, Object>> queryCondition);
	/**
	 * 内置配置查询
	 * @param map
	 * @param token
	 * @return
	 */
	public ListResult<Map<String,Object>> queryConfigList(String authentication,ParamPage<Map<String, Object>> dataInfo);
	public ListResult<Map<String,Object>> queryListSysteConfigValueByCode(String token,ParamPage<Map<String,Object>> dataInfo);
	
	
}
