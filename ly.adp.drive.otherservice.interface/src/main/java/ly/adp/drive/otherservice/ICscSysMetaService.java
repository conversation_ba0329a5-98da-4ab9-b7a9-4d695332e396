package ly.adp.drive.otherservice;

import java.util.HashMap;
import java.util.Map;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

public interface ICscSysMetaService {

	/**
	 * 页面查询
	 * @param authentication
	 * @param pageCode
	 * @return
	 */
	public ListResult<Map<String, Object>> proConfigPageviewQueryByPage(String authentication, String pageCode) ;

	/**
	 * 查询网格列
	 * @param pageIndex
	 * @param pageSize
	 * @param dataInfo
	 * @return
	 */
	public ListResult<Map<String, Object>> proConfigColumnQueryByPage(String authentication,
			HashMap<String, Object> queryCodition);

	/**
	 * 全局标签查询
	 * @param authentication
	 * @param dataInfo
	 * @return
	 */
	ListResult<Map<String, Object>> proGlobalLabelQueryByPage(String authentication,ParamPage<Map<String, Object>> dataInfo);

	public ListResult<Map<String, Object>> proConfigMappingQueryByPage(String authentication, String pageCode) ;
	public EntityResult<Map<String, Object>> proConfigMappingTranField(String authentication, String pageCode, Map<String, Object> inputInfoMap, String inputJsonStr) ;

}
