FROM mp/alpine-oraclejdk8:v1
ADD *.jar /home/<USER>/app.jar
COPY ./skywalking-agent/ /opt/skywalking-agent/

ENV env dev
ENV dev_meta http://172.26.223.200:8848
ENV TZ=Asia/Shanghai
ENV app_name=ly-mp-module-cloud
ENV JAVA_OPTS="	-XX:+UseContainerSupport"

# ENV  SW_AGENT_RUN=0
# ENV  SW_AGENT_COLLECTOR_BACKEND_SERVICES="skywalking-oap-server:11800"
# ENV  SW_AGENT_NAME="app_name"
# ENV  SW_AGENT_NAMESPACE="app_name"
# ENV  SW_AGENT_SAMPLE="-1"
# ENV  SW_LOGGING_DIR="/swlogs/"
# ENV  SW_LOGGING_FILE_NAME="sw-app_name.log"
# ENV  SW_LOGGING_LEVEL="INFO"
# ENV  SW_LOGGING_MAX_HISTORY_FILES="1"
# ENV  JAVA_TOOL_OPTIONS="-javaagent:/opt/skywalking-agent/skywalking-agent.jar"
USER root
RUN mkdir ${SW_LOGGING_DIR}
RUN chmod -R 775 ${SW_LOGGING_DIR};
RUN chmod -R 775 "/opt/skywalking-agent/";

USER mpjava

WORKDIR /home/<USER>
ENTRYPOINT  java $JAVA_OPTS  -Duser.timezone=GMT+8 -Dspring.cloud.nacos.discovery.namespace=$env -Dspring.cloud.nacos.config.namespace=$env -Dspring.cloud.nacos.discovery.server-addr=$dev_meta -Dspring.cloud.nacos.config.server-addr=$dev_meta -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
