/*
 * main项目的build脚本
 */

println "lympmain projectDir = $projectDir"
apply from: "$rootDir/gradle/dependencies.gradle"
//apply from: "$rootDir/gradle/prodependencies.gradle"

allprojects {

    //提出配置文件
	task createBizDocs {
	    def conf = file("$buildDir/conf")
	    outputs.dir conf
	    doLast {
	        conf.mkdirs()
			copy {
	            from "$rootDir/$commonProName/src/main/resources"
	            into "$buildDir/conf"
	            exclude 'META-INF'
	        }
	        copy {
	            from "src/main/resources"
	            into "$buildDir/conf"
	            exclude '*.xml'
	            //exclude 'META-INF'
       	 	}
	    }
	}
	task createDocs {
		def conf = file("$buildDir/conf")
	    outputs.dir conf
	    doLast {
	    	conf.mkdirs()
	        copy {
	            from "src/main/resources"
	            into "$buildDir/conf"
	            exclude '*.xml'
       	 	}
       	 	copy {
	            from "$rootDir/$commonProName/src/main/resources"
	            into "$buildDir/conf"
	            include 'ly-mp-cache.properties'
	            include 'ly-mp-other.properties'
	            include 'ly-mp-cache-appcontext.xml'
	            include 'logback-spring.xml'
	            include 'ly-mp-mobsdk.xml'
	            include 'mp-config-env.properties'
	        }
	    }
	}
}


subprojects {
	buildscript {
		ext {
			mybatisPlusVersion ='3.1.2'
			seataVersion='1.7.0'
		}
		repositories {
			 maven {
				  url 'https://maven.aliyun.com/repository/public/'
				}
			maven {
				  url 'https://maven.aliyun.com/repository/spring/'
				}
			maven{
				url 'https://devrepo.devcloud.cn-east-3.huaweicloud.com/04/nexus/content/repositories/0aeb4a9baf00f3e70f6bc018ff034740_1_0/'
				//authentication(userName: 'lanyou_hcf', password: 'lanyou_hcf#szlanyou.COM')
				credentials {
						username "${repositoryUsername}"
						password "${repositoryPassword}"
					}
			}
            maven {url "https://plugins.gradle.org/m2/"}
			//maven{ url "${repositoryUrl}/repository/maven-public/"}
			flatDir {
				dirs '../extjar'
			}
		}
		dependencies {
			classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
			//classpath("org.springframework.boot:spring-boot-gradle-plugin:1.5.7.RELEASE")
			//classpath('se.transmode.gradle:gradle-docker:1.2')
		}
	}

	apply plugin: 'java'
   	//apply plugin: 'war'
	apply plugin: 'eclipse'
	apply plugin: 'jacoco'


    repositories {
		//mavenCentral()
		maven {
			  url 'https://maven.aliyun.com/repository/public/'
			}
		maven {
			  url 'https://maven.aliyun.com/repository/spring/'
			}
		maven{
			url 'https://devrepo.devcloud.cn-east-3.huaweicloud.com/04/nexus/content/repositories/0aeb4a9baf00f3e70f6bc018ff034740_1_0/'
			//authentication(userName: 'lanyou_hcf', password: 'lanyou_hcf#szlanyou.COM')
				credentials {
						username "${repositoryUsername}"
						password "${repositoryPassword}"
					}
		}
		//maven{ url "${repositoryUrl}/repository/maven-public/"}
    }

	sourceCompatibility = 1.8
	targetCompatibility = 1.8
	compileJava.options.encoding = 'UTF-8'

	processResources {
		println(" *** processResources $project.name")

		if(project.name == "$commonProName" ){
			copy {
				from "src/main/resources"
				into "$buildDir/resources/main"
			}
		}

		if (project.hasProperty('docker')){
			include '**/*'
		}else{
			//println "no docker"
//			include '**/*.xml'
			//include 'META-INF/*'
			include '**/*'
		}
	}

	// 单元测试
	tasks.withType(Test) { ignoreFailures = true }
	compileTestJava.options.encoding = 'UTF-8'
	// 复制资源文件到单元测试的classpath目录, 保证单元测试能进行
    processTestResources {
		from 'src/main/resources'
		from 'src/test/resources'
		// mp的资源文件，请根据项目实际保存的目录更改
		from "$rootDir/$commonProName/src/main/resources"
	}

	// 代码覆盖率分析
	test { finalizedBy jacocoTestReport }
	jacoco {
    	toolVersion = "0.8.0"
    }
	jacocoTestReport {
        reports {
	        xml.enabled true
        }
    }

//	configurations { providedCompile }

	configurations.all {
		// 每次构建检查依赖
		resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
		
	    resolutionStrategy.force 'commons-lang:commons-lang:2.4'
	    resolutionStrategy.force 'org.apache.commons:commons-pool2:2.4.1'
		//CVE-2021-42550 安全漏洞 影响版本  logback < 1.2.9
		resolutionStrategy.force "ch.qos.logback:logback-classic:1.2.10"
		resolutionStrategy.force "ch.qos.logback:logback-core:1.2.10"
		resolutionStrategy.force "org.codehaus.groovy:groovy:2.4.21"
	}

	configurations {
	    compile.exclude group:'org.slf4j', module: "slf4j-log4j12"
	    compile.exclude group:'log4j'
	    compile.exclude group:'org.springframework', module: 'spring'
	    compile.exclude group:'com.google.collections', module: 'google-collections'
	    
	    compile.exclude group:'accessors', module: 'adp'
	    compile.exclude group:'org.apache.ant', module: 'ant'
	    compile.exclude group:'org.apache.ant', module: 'ant-launcher'
	    //compile.exclude group:'org.ow2.asm', module: 'asm'
	    compile.exclude group:'org.bouncycastle', module: 'bcmail-jdk14'
	    compile.exclude group:'bouncycastle', module: 'bcmail-jdk14'
	    compile.exclude group:'org.bouncycastle', module: 'bcprov-jdk14'
	    compile.exclude group:'bouncycastle', module: 'bcprov-jdk14'
	    compile.exclude group:'bouncycastle', module: 'bctsp-jdk14'
	    compile.exclude group:'jline', module: 'jline'
	    compile.exclude group:'org.mapstruct', module: 'mapstruct'
	    compile.exclude group:'io.netty', module: 'netty'
	    compile.exclude group:'stax', module: 'stax-api'
	    compile.exclude group:'xml-apis', module: 'xml-apis'
	    compile.exclude group:'javax.transaction', module: 'javax.transaction-api'
	    compile.exclude group:'io.lettuce', module: 'lettuce-core'
		compile.exclude group:'org.apache.logging.log4j', module: "log4j-core"
        compile.exclude group:'org.apache.logging.log4j', module: "log4j-api"
        compile.exclude group:'org.apache.logging.log4j', module: "log4j-to-slf4j"
	}

	dependencies {

		//compile("org.springframework.boot:spring-boot-starter-activemq:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}")
        compile("org.springframework.boot:spring-boot-starter-aop:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-cache:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-data-redis:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-mail:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-quartz:${springBootVersion}")
		compile("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
		//compile("org.springframework.boot:spring-boot-starter-websocket:${springBootVersion}")
		//compile("org.springframework.boot:spring-boot-loader-tools:${springBootVersion}")
		//compile("org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2")
		compile("com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"){
			exclude group:'com.baomidou', module: 'mybatis-plus-generator'
			exclude group:'org.springframework.boot', module: 'spring-boot-autoconfigure'
		}
		compile("com.github.jsqlparser:jsqlparser:4.0")

        compile("org.springframework.boot:spring-boot:${springBootVersion}")
//		compile group: 'com.alibaba', name: 'dubbo', version: '2.6.2'
		//runtime("com.microsoft.sqlserver:mssql-jdbc")
		//runtime("mysql:mysql-connector-java")
		//compileOnly("org.springframework.boot:spring-boot-configuration-processor:${springBootVersion}")
		testCompile("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")

		
//		compile("io.seata:seata-all:${seataVersion}")
		compile("io.seata:seata-spring-boot-starter:${seataVersion}") {
			exclude group: 'org.springframework', module: 'spring-context'
			exclude group: 'org.springframework', module: 'spring-beans'
			exclude group: 'org.springframework', module: 'spring-core'
			exclude group: 'org.springframework', module: 'spring-aop'
			exclude group: 'org.springframework', module: 'spring-webmvc'
		}
		implementation files('../extjar/ly.mp.cloud.common-v2.jar')
		//compile group: 'com.ctrip.framework.apollo', name: 'apollo-client', version: '1.4.0'
		
		
		// https://mvnrepository.com/artifact/org.apache.curator/curator-recipes
//		compile("org.apache.curator:curator-recipes:4.0.0"){
//			exclude module: "guava"
//			exclude module: "zookeeper"
//			exclude module: "slf4j-api"
//		}

		compile libs.commons
		compile libs.cloudconfig
		compile libs.activemq

//		compile("com.szlanyou.mp:ly.mp.cloud.common:$mpversionId"){transitive=false}
		compile("com.szlanyou.mp:ly.mp.cache:$mpversionId"){transitive=false}
		compile("com.szlanyou.mp:ly.mp.dal.comm:$mpversionId"){transitive=false}
		
		//compile("com.szlanyou.mp:ly.mp.log.entities:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.ibiz:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.biz:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.idal:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.dal.oracle:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.dal.mysql:$mpversionId"){transitive=false}
		//compile("com.szlanyou.mp:ly.mp.log.dal.sqlserver:$mpversionId"){transitive=false}
		compile("org.springframework:spring-beans:5.2.20.RELEASE")
		compile("io.netty:netty-all:4.1.51.Final")
		compile("commons-beanutils:commons-beanutils:1.9.4")
		compile("org.yaml:snakeyaml:1.27")
		compile("org.apache.commons:commons-compress:1.21")
    }



	group = ''
	jar {
	    manifest {
	      attributes(
	          'Implementation-Title': "${project.name}",
	          'Implementation-Version': "MP${versionId}",
	          'Created-By': System.getProperty('java.version') + ' (' + System.getProperty('java.vendor') + ')',
	          'Built-With': "gradle-${project.getGradle().getGradleVersion()}, groovy-${GroovySystem.getVersion()}",
	          'Build-Time': "${new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")}",
	          'Built-By': "szlanyou",
	          'Built-On': "${InetAddress.localHost.hostName}/${InetAddress.localHost.hostAddress}"
	      )
	    }
	  }



	apply plugin: 'maven'

	uploadArchives {
		repositories {
			mavenDeployer {
				pom.groupId = groupId
				pom.version = versionId
				pom.artifactId = project.name
				repository(url: "${repositoryUrlRelease}"){
					authentication(userName: "${repositoryUsername}", password: "${repositoryPassword}")
					//credentials {
						//username 'lanyou_hcf'
						//password 'lanyou_hcf#szlanyou.COM'
					//}
				}
				//repository(url: "${repositoryUrl}/repository/maven-releases/") {
				//	authentication(userName: repositoryUsername, password: repositoryPassword)
				//}
				
				snapshotRepository(url: "${repositoryUrlSnapshot}"){
					authentication(userName: "${repositoryUsername}", password: "${repositoryPassword}")
					//credentials {
						//username 'lanyou_hcf'
						//password 'lanyou_hcf#szlanyou.COM'
					//}
				}
				//snapshotRepository(url: "${repositoryUrl}/repository/maven-snapshots/") {
				//	authentication(userName: repositoryUsername, password: repositoryPassword)
				//}
			}
		}
	}

}

