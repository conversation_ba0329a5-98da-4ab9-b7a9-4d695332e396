package com.annotationSelf;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SmartADPCache {
    int expireTime() default 5; // 默认过期时间为5分钟

    TimeUnit timeUnit() default TimeUnit.MINUTES;// 单位默认为分钟
}