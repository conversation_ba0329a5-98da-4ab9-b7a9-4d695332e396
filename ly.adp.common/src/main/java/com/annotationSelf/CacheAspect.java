package com.annotationSelf;

import com.alibaba.fastjson.JSONObject;
import com.ly.mp.busicen.common.helper.CacheHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class CacheAspect {

    @Value("#{ ${SmartADPCache.enable:false} }")
    Boolean smartADPCacheEnable;

    @Around("@annotation(smartADPCache)")
    public Object around(ProceedingJoinPoint joinPoint, SmartADPCache smartADPCache) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        // 生成缓存Key
        String key = generateKey(methodName, args);

        if (smartADPCacheEnable) {
            // 从Redis中获取缓存
            Object cachedResult = CacheHelper.get(methodName, key);

            if (cachedResult != null) {
                return cachedResult;
            }
        }
        // 如果没有缓存，执行方法  
        Object result = joinPoint.proceed();

        if (smartADPCacheEnable) {
            // 将结果缓存到Redis中
            CacheHelper.put(methodName, key, result, smartADPCache.expireTime(), smartADPCache.timeUnit());
        }
        return result;
    }

    private String generateKey(String methodName, Object[] args) {
        StringBuilder key = new StringBuilder(methodName);
        key.append("(");
        for (Object arg : args) {
            //key.append(arg.toString()).append(",");
            key.append(JSONObject.toJSONString(arg)).append(",");
        }
        key.deleteCharAt(key.length() - 1);
        key.append(")");
        return key.toString();
    }
}