package com.ly.mp.busicen.common.context;

import java.io.*;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.List;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ly.mp.busicen.common.util.OptResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;


public class BusicenInvoker<T> implements IBusicenInvoke<T> {

    public static final Logger log = LoggerFactory.getLogger(BusicenInvoker.class);

    static boolean printStackTrace = false;

    T result;

    Throwable expt;

    public static BusicenInvoker<OptResult> doOpt(Supplier<OptResult> supplier) {
        BusicenInvoker<OptResult> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        if (busicenInvoker.expt != null) {
            OptResultStack resultTemp = new OptResultStack();
            resultTemp.setResult("0");
            resultTemp.setMsg("操作失败");
            if (busicenInvoker.result == null) {
                busicenInvoker.result = resultTemp;
            }
            if (busicenInvoker.expt instanceof BusicenException) {
                busicenInvoker.result.setMsg(busicenInvoker.expt.getMessage());
            }
            if (printStackTrace) {
                String trace = throwToStr(busicenInvoker.expt);
                resultTemp.setStackTrace(trace);
            }
        }
        return busicenInvoker;
    }

    public static <M> BusicenInvoker<ListResult<M>> doList(Supplier<ListResult<M>> supplier) {
        BusicenInvoker<ListResult<M>> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        if (busicenInvoker.expt != null) {
            ListResultStack<M> resultTemp = new ListResultStack<>();
            resultTemp.setMsg("查询失败");
            resultTemp.setResult("0");
            if (busicenInvoker.result == null) {
                busicenInvoker.result = resultTemp;
            }
            if (busicenInvoker.expt instanceof BusicenException) {
                busicenInvoker.result.setMsg(busicenInvoker.expt.getMessage());
            }
            if (printStackTrace) {
                String trace = throwToStr(busicenInvoker.expt);
                resultTemp.setStackTrace(trace);
            }
        }
        return busicenInvoker;
    }

    public static <M> BusicenInvoker<EntityResult<M>> doEntity(Supplier<EntityResult<M>> supplier) {
        BusicenInvoker<EntityResult<M>> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        if (busicenInvoker.expt != null) {
            EntityResultStack<M> resultTemp = new EntityResultStack<>();
            resultTemp.setMsg("操作失败");
            resultTemp.setResult("0");
            if (busicenInvoker.result == null) {
                busicenInvoker.result = resultTemp;
            }
            if (busicenInvoker.expt instanceof BusicenException) {
                busicenInvoker.result.setMsg(busicenInvoker.expt.getMessage());
            }
            if (printStackTrace) {
                String trace = throwToStr(busicenInvoker.expt);
                resultTemp.setStackTrace(trace);
            }
        }
        return busicenInvoker;
    }

    public static <T> BusicenInvoker<T> doo(Supplier<T> supplier) {
        BusicenInvoker<T> busicenInvoker = new BusicenInvoker<>();
        busicenInvoker.invoke(supplier);
        return busicenInvoker;
    }

    @Override
    public IBusicenInvoke<T> defErr(T t) {
        if (result == null) {
            result = t;
        }
        return this;
    }

    @Override
    public IBusicenInvoke<T> invoke(Supplier<T> supplier) {
        try {
            result = supplier.get();
        } catch (Throwable e) {
            log.error("调用异常", e);
            expt = e;

        }
        return this;
    }

    @Override
    public IBusicenInvoke<T> optResult(BusicenInvokeConsumer<T> consumer) {
        consumer.accept(result, expt);
        return this;
    }

    @Override
    public T result() {
        return result;
    }

    public static String throwToStr(Throwable t) {
        if (t == null) {
            return null;
        }
        try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw, true)) {
            t.printStackTrace(pw);
            pw.flush();
            sw.flush();
            return sw.getBuffer().toString();
        } catch (IOException e) {
            return "";
        }
    }

    public interface ResultExceptionStack {

        String getStackTrace();

        void setStackTrace(String stackTrace);
    }

    public static class ListResultStack<T> extends ListResult<T> implements ResultExceptionStack {
        private String stackTrace;

        @Override
        public String getStackTrace() {
            return stackTrace;
        }

        @Override
        public void setStackTrace(String stackTrace) {
            this.stackTrace = stackTrace;
        }
    }

    public static class EntityResultStack<T> extends EntityResult<T> implements ResultExceptionStack {
        private String stackTrace;

        @Override
        public String getStackTrace() {
            return stackTrace;
        }

        @Override
        public void setStackTrace(String stackTrace) {
            this.stackTrace = stackTrace;
        }
    }

    public static class OptResultStack extends OptResult implements ResultExceptionStack {
        private String stackTrace;

        @Override
        public String getStackTrace() {
            return stackTrace;
        }

        @Override
        public void setStackTrace(String stackTrace) {
            this.stackTrace = stackTrace;
        }
    }
}
