package com.ly.mp.busicen.common.helper;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.exception.AppException;
import com.ly.mp.busicen.common.handler.AppExceptionHandler;


/**
 * <AUTHOR>
 * @version 创建时间 ：2018年8月30日 下午2:58:17
 */
public class DateHelper {
    private static final Logger logger = LoggerFactory.getLogger(AppExceptionHandler.class);
    public static final String yyyy_MM_dd = "yyyy-MM-dd";
    public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String yyMMdd = "yyMMdd";
    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";
    public static final String yy = "yy";
    public static final String yyyyMMdd = "yyyyMMdd";
    public static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    //每天生成的随机数，最保险的做法是将数据存储到硬盘
    private static Map<String, String> serialNo = new HashMap<>();

    /**
     * 将驼峰式命名的字符串转换为下划线大写方式。如果转换前的驼峰式命名的字符串为空，则返回空字符串。</br>
     * 例如：HelloWorld->HELLO_WORLD
     *
     * @param name 转换前的驼峰式命名的字符串
     * @return 转换后下划线大写方式命名的字符串
     */
    public static String underscoreName(String name) {
        StringBuilder result = new StringBuilder();
        if (name != null && name.length() > 0) {
            // 将第一个字符处理成大写
            result.append(name.substring(0, 1).toUpperCase(Locale.getDefault()));
            // 循环处理其余字符
            for (int i = 1; i < name.length(); i++) {
                String s = name.substring(i, i + 1);
                // 在大写字母前添加下划线
                if (s.equals(s.toUpperCase(Locale.getDefault())) && !Character.isDigit(s.charAt(0))) {
                    result.append("_");
                }
                // 其他字符直接转成大写
                result.append(s.toUpperCase(Locale.getDefault()));
            }
        }
        return result.toString();
    }

    /**
     * 将date按yyyy-MM-dd格式化
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年9月12日 上午10:23:52
     */
    public static String formatDate(Date date) {
        return formatDate(yyyy_MM_dd, date);
    }

    /**
     * 将当前日期按format格式化
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年9月12日 上午10:15:23
     */
    public static String formatDate(String format) {
        return formatDate(format, new Date());
    }

    /**
     * 将date按format格式化
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年9月12日 上午10:17:11
     */
    public static String formatDate(String format, Date date) {
        if (null == date) {
            return "";
        }
        format = StringUtils.isEmpty(format) ? yyyy_MM_dd : format;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 按yyMMdd格式化 date
     *
     * <AUTHOR>
     * @version 创建时间：2019年1月4日上午11:08:52
     */
    public static Date parseDate(String date) {
        if (StringUtils.isEmpty(date)) {
            throw new RuntimeException("日期不能为空");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(yyMMdd);
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 格式化 date
     *
     * <AUTHOR>
     * @version 创建时间：2019年1月31日上午11:08:52
     */
    public static Date parseDate(String date, String format) {
        if (StringUtils.isEmpty(date)) {
            throw new RuntimeException("日期不能为空");
        }
        if (StringUtils.isEmpty(format)) {
            throw new RuntimeException("参数格式不能为空");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 生成14位日期+5位随机数流水，输出如：2018033115231200110
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年9月11日 上午11:12:24
     */
	/*public static String getSerialNo() {
		SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMddHHmmss);
		return sdf.format(new Date()) + getRandom(5);
	}*/

    /**
     * 生成6位日期+5位随机数流水，输出如：18033100110
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年8月31日 下午6:21:40
     */
/*	public static String gainSerialNo() {
		return formatDate(yyMMdd, new Date()) + getRandom(5);
	}*/

    /**
     * 生成6位日期+4位流水，输出如：prefix1803310001
     * 每天每类单据都从0001开始递增
     *
     * @param prefix 单据前缀，如：YBBX
     * @return
     * @deprecated 存在流水号被重置风险
     */
    public static String gainSerialNo(String prefix) {
        return gainSerialNo(prefix, 4, true);
    }

    /***
     * 生成项目号(3位流水)
     *
     * @deprecated 存在流水号被重置风险
     *
     * @param prefix 项目号前缀
     * @return
     */
    public static String gainProjectSerialNo(String prefix) {
        return gainSerialNo(prefix, 3, false);
    }

    /**
     * @param prefix        单据前缀
     * @param serialNoLen   单据流水长度
     * @param controlByDate true-每天从1开始递增，false-每年从1开始递增
     */
    public static String gainSerialNo(String prefix, int serialNoLen, boolean controlByDate) {
        if (StringUtils.isEmpty(prefix)) {
            throw new AppException("单据前缀不能为空");
        }
        String currentDate = "";
        String key = prefix;
        if (controlByDate) {
            currentDate = formatDate(yyMMdd, new Date());
            key = prefix + currentDate;
        }
        String cacheName = "billSerialNo";
        Integer currentSerialNo = (Integer) CacheHelper.get(cacheName, key);
        if (null == currentSerialNo) {
            currentSerialNo = 1;
        } else {
            currentSerialNo = currentSerialNo + 1;
        }
        CacheHelper.put(cacheName, key, currentSerialNo);
        return key + fillFourLength(currentSerialNo, serialNoLen);
    }

    /**
     * 将currentSerialNo补齐成serialNoLen，不足前面加0
     *
     * @param currentSerialNo
     * @param serialNoLen
     * @return
     */
    public static String fillFourLength(Integer serialNo, int serialNoLen) {
        int len = String.valueOf(serialNo).length();
        int addZero = serialNoLen > len ? serialNoLen - len : 0;
        StringBuilder fillStr = new StringBuilder("");
        for (int i = 0; i < addZero; i++) {
            fillStr.append("0");
        }
        return fillStr.toString() + serialNo;
    }

    /**
     * 生成指定位数的随机数
     *
     * @param length 位数
     * <AUTHOR>
     * @version 创建时间 ：2018年9月11日 下午2:09:27
     */
    public static String getRandom(int length) {
        String current = formatDate(yyMMdd, new Date());
        String randomNo;
        //重试次数
        int reTry = 0;
        while (true) {
            String key = current + "_";
            randomNo = "";
            //最多重试3次还是重复直接截取当前系统毫秒数【其他方案：可以考虑将随机数长度递增】
            if (reTry >= 3) {
                //截取当前系统毫秒数(长度为13位)
                String millis = String.valueOf(System.currentTimeMillis());
                int beginIndex = millis.length() < length ? 0 : millis.length() - length;
                randomNo = String.valueOf(System.currentTimeMillis()).substring(beginIndex);
                logger.error("生成不重复的随机数失败,截取当前系统毫秒：" + randomNo);
                break;
            }
//			Random random = new Random();
//			for (int i = 0; i < length; i++) {
//				randomNo += String.valueOf(random.nextInt(10));
//
//			}
//			key = key + randomNo;
//			if(serialNo.containsKey(key)) {
//				reTry++;
//				continue;
//			}
            serialNo.put(key, "1");
            break;
        }
        return randomNo;
    }

    /**
     * 输入：2018-08-30
     * 输出：2018-08-30 00:00:00
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年8月30日 下午3:17:50
     */
    public static Date getDateTimenSecondBegin(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyy_MM_dd_HH_mm_ss);
        String formatDate = formatDate(date) + " 00:00:00";
        try {
            return sdf.parse(formatDate);
        } catch (ParseException e) {
            logger.error("亲，日期转换出错啦。。" + e.getMessage(), e);
        }
        return date;
    }

    /**
     * 输入：2018-08-30
     * 输出：2018-08-30 23:59:59
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年8月30日 下午3:17:55
     */
    public static Date getDateTimeSecondEnd(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyy_MM_dd_HH_mm_ss);
        String formatDate = formatDate(date) + " 23:59:59";
        try {
            return sdf.parse(formatDate);
        } catch (ParseException e) {
            logger.error("亲，日期转换出错啦。。" + e.getMessage(), e);
        }
        return date;
    }

    /**
     * 输入：2018-08-30
     * 输出：2018-08-30 23:59:59
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年8月30日 下午3:17:55
     */
    public static Date getFormatDateTime(String format, Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String formatDate = sdf.format(date);
        try {
            return sdf.parse(formatDate);
        } catch (ParseException e) {
            logger.error("亲，日期转换出错啦。。" + e.getMessage(), e);
        }
        return date;
    }

    /**
     * 将单据日期转换成汇率日期（年-月-1），日固定为1
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年10月10日 下午4:28:01
     */
    public static Date getExRateDate(Date billDate) {
        Calendar c = Calendar.getInstance();
        c.setTime(billDate);
        int month = c.get(Calendar.MONTH) + 1;
        int year = c.get(Calendar.YEAR);
        String exRateDate = year + "-" + month + "-" + "1";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(exRateDate);
        } catch (ParseException e) {
        }
        return null;
    }

    @Deprecated
    public static String serialNo() throws Exception {
        String filePath = DateHelper.class.getClassLoader().getResource("serialno.properties").getPath();
        filePath = URLDecoder.decode(filePath, "utf-8");
        File file = new File(filePath);
        Properties prop = new Properties();
        try (InputStream inputFile = new FileInputStream(file);
             OutputStream outputFile = new FileOutputStream(file)) {
            prop.load(inputFile);
            String key = formatDate("yyyyMMdd", new Date());
            String value = prop.getProperty(key);
            Integer serialNo = Integer.valueOf(value);
            prop.setProperty(key, serialNo + 1 + "");

            //在属性文件对应记录增加注释
            prop.store(outputFile, "Update '" + key + "'+ '" + serialNo);
        } catch (Exception e) {
            logger.error("\n\n\n occured exception===" + e.getMessage(), e);
        }
        return "";
    }

    /**
     * 时间戳转换成日期格式字符串
     *
     * @param seconds   精确到秒的字符串
     * @param formatStr
     * @return
     */
    public static String timeStamp2Date(String seconds, String format) {
        if (seconds == null || seconds.isEmpty() || seconds.equals("null")) {
            return "";
        }
        if (format == null || format.isEmpty()) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(Long.valueOf(seconds + "000")));
    }

    public static String getFirstDate() {
        // 获取当前年月
        YearMonth yearMonth = YearMonth.now();
        // 获取当月第一天的日期
        LocalDate startDate = yearMonth.atDay(1);
        // 设置时间为00:00:00
        LocalTime startTime = LocalTime.MIDNIGHT;
        // 创建起始时间和结束时间的LocalDateTime对象
        LocalDateTime startDateTime = LocalDateTime.of(startDate, startTime);
        // 格式化输出
        return startDateTime.format(formatter);
    }

    public static String getEndDate() {
        // 获取当前年月
        YearMonth yearMonth = YearMonth.now();
        // 获取当月最后一天的日期
        LocalDate endDate = yearMonth.atEndOfMonth();
        // 设置时间为23:59:59
        LocalTime endTime = LocalTime.MAX;
        // 创建起始时间和结束时间的LocalDateTime对象
        LocalDateTime endDateTime = LocalDateTime.of(endDate, endTime);
        // 格式化输出
        return endDateTime.format(formatter);
    }
}
