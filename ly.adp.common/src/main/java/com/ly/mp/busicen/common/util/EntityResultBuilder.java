package com.ly.mp.busicen.common.util;

import com.ly.mp.component.entities.EntityResult;

public class EntityResultBuilder<T> {

	private EntityResult<T> parent = new EntityResult<>();

	public static <T> EntityResultBuilder<T> create() {		
		return new EntityResultBuilder<T>();
	}
	
	public static <T> EntityResultBuilder<T> creatOk() {
		EntityResultBuilder<T> result = create();
		result.parent.setMsg("保存成功");
		result.parent.setResult("1");
		return result;
	}

	public EntityResult<T> build() {
		return  parent;
	}

	public EntityResultBuilder<T> result(String result) {
		parent.setResult(result);
		return this;
	}	

	public EntityResultBuilder<T> msg(String msg) {
		parent.setMsg(msg);
		return this;
	}

	public  EntityResultBuilder<T> rows(T rows) {
		parent.setRows(rows);
		return this;
	}

	public  EntityResultBuilder<T> extInfo(String extInfo) {
		parent.setExtInfo(extInfo);
		return this;
	}
	
	public static void main(String[] args) {
		EntityResult<?> tt = EntityResultBuilder.create().result("-1").rows(11).build();
		EntityResult<String> tts = EntityResultBuilder.<String>creatOk().result("1").msg("aa").rows("rrrrj").build();
		System.out.println(tt.getRows());
		System.out.println(tts.getRows());
	}

}
