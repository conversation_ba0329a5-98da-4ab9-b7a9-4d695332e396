package com.ly.adp.common.configuration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.common.entity.ParamQry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
@FeignClient(name = "dwsFeign", url = "${refer.dws.baseurl:}")
public interface DwsFeign {
    /**
     * 离职战败线索移交查询
     *
     * @param qry
     * @return
     */
    @PostMapping("/api/testDriveCar/selectTestDriveSheet")
    Page<Map<String, Object>> selectTestDriveSheet(@RequestBody ParamQry qry);
}
