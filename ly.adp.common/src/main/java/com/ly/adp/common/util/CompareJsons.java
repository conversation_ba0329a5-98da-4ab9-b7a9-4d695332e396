package com.ly.adp.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class CompareJsons {
	/**
	 * caiyunxiang
	 * CompareJsons 
	 * json比对工具类 提供返回类型暂时 只有字符类型
	 * @param json1 旧的
	 * @param json2 新的
	 * @param key null
	 * @return
	 */
	public static List<Map<String, Object>> compareJsons(JSONObject json1, JSONObject json2,String key){
		List<Map<String, Object>> jsonListMap=new ArrayList<Map<String,Object>>();
        commonCompare(json1,json2,key);
        Iterator<String> json1Keys = json1.keySet().iterator();
        while (json1Keys.hasNext()){
            key = json1Keys.next();
            Map<String, Object> jsonMap =compareJsons(json1.get(key), json2.get(key),key);
            if(!StringUtils.isEmpty(jsonMap.get("column"))) {
            	jsonListMap.add(jsonMap);
            	System.err.println(jsonMap);
            }
        }
        return jsonListMap;
    }

	public static Map<String, Object> compareJsons(Object json1, Object json2, String key) {
		Map<String, Object> jsonMap = new HashMap<String, Object>();
		commonCompare(json1, json2, key);
		if (json1 instanceof JSONObject) {
			// 如果是JSONObject则继续递归比较。
			compareJsons((JSONObject) json1, (JSONObject) json2, key);
		} else if (json1 instanceof JSONArray) {
			// 如果是JSONArray，则进行数组类比较。
			compareJsons((JSONArray) json1, (JSONArray) json2, key);
		} else {
			// 其余全部为字符串比较，非字符串的也转换为字符串比较。
			jsonMap = compareJsons(json1.toString(), json2.toString(), key);
		}
		return jsonMap;
	}

	public static void compareJsons(JSONArray jsonArray1, JSONArray jsonArray2, String key) {
		commonCompare(jsonArray1, jsonArray2, key);
		// 数组存在无序的情况，所以需要将1中的每一个元素，跟2中的所有元素进行比较。
		// 两种方案：1.先对两个jsonArray进行排序，然后再依次比较。 2.对1中的每一个元素，判断是否在2中存在。(有重复元素的可能会有问题。)
		// 方案2的实现：
		Iterator<Object> iterator1 = jsonArray1.iterator();
		while (iterator1.hasNext()) {
			Object o1 = iterator1.next();
			if (jsonArray2.indexOf(o1) == -1) {
				System.err.println("compareJsons1不一致：key  " + key + " json1中的 jsonArray其中的value ： "
						+ JSONObject.toJSONString(o1) + "  仅在json1中存在，不在json2中存在");
			} else {
				System.out.println(
						"compareJsons1一致：key " + key + " json1中的 jsonArray其中的value ：" + JSONObject.toJSONString(o1));
			}
		}
		Iterator<Object> iterator2 = jsonArray2.iterator();
		while (iterator2.hasNext()) {
			Object o2 = iterator2.next();
			if (jsonArray1.indexOf(o2) == -1) {
				System.err.println("compareJsons1不一致：key " + key + " json2中的 jsonArray其中的value ： "
						+ JSONObject.toJSONString(o2) + "  仅在json2中存在，不在json1中存在");
			}
		}
	}

	public static Map<String, Object> compareJsons(String json1, String json2, String key) {
		Map<String, Object> jsonMap = new HashMap<String, Object>();
		commonCompare(json1, json2, key);
		if (json1.equals(json2)) {
			System.out.println("compareJsons2一致：key " + key + " ， json1 value = " + json1 + " json2 value = " + json2);
		} else {
			System.err
					.println("compareJsons2不一致： key " + key + " ， json1 value = " + json1 + " json2 value = " + json2);
			jsonMap.put("column", key);
			jsonMap.put("paramOld", json1);
			jsonMap.put("paramNew", json2);
		}
		return jsonMap;
	}

	public static Map<String, Object> commonCompare(Object json1, Object json2, String key) {
		Map<String, Object> jsonMap = new HashMap<String, Object>();
		if (json1 == null && json2 == null) {
			System.err.println("commonCompare3不一致： key " + key + " 在两者中均不存在");
		}
		if (json1 == null) {
			System.err.println(
					"commonCompare3不一致： key " + key + " 在json1中不存在，在json2中为 " + JSONObject.toJSONString(json2));
		}
		if (json2 == null) {
			System.err.println(
					"commonCompare3不一致： key " + key + " 在json1中为 " + JSONObject.toJSONString(json2) + " 在json2中不存在");
		}
		return jsonMap;
	}
}
