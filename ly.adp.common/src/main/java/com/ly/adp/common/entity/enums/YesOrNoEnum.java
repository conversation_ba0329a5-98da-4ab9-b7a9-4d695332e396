package com.ly.adp.common.entity.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 是非枚举
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
public enum YesOrNoEnum {

    YES("1", 1, "是"),
    NO("0", 0, "否"),

    ;

    /**
     * 是非编码
     */
    private final String code;

    /**
     * 是非类型
     */
    private final Integer type;

    /**
     * 是否描述
     */
    private final String desc;

    YesOrNoEnum(String code, Integer type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static YesOrNoEnum getYesOrNoEnumByCode(String code) {
        return Arrays.stream(YesOrNoEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getYesOrNoDescByCode(String code) {
        YesOrNoEnum yesOrNoEnum = getYesOrNoEnumByCode(code);
        return Optional.ofNullable(yesOrNoEnum).map(YesOrNoEnum::getDesc).orElse(null);
    }

    public static YesOrNoEnum getYesOrNoEnumByType(Integer type) {
        return Arrays.stream(YesOrNoEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElse(null);
    }

    public static String getYesOrNoDescByType(Integer type) {
        YesOrNoEnum yesOrNoEnum = getYesOrNoEnumByType(type);
        return Optional.ofNullable(yesOrNoEnum).map(YesOrNoEnum::getDesc).orElse(null);
    }

}
