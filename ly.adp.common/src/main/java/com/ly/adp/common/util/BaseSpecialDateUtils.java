package com.ly.adp.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

import com.alibaba.excel.util.StringUtils;
import com.ly.mp.component.entities.OptResult;

public class BaseSpecialDateUtils {

    public static String[] chars = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
            "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8",
            "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"};

    //获取当前时间
    public static String sysDate() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    //生产8位UUID
    public static String getUUID() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }

    public static String sysDateTZ() {
        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(new Date());
    }

    //校验车牌
    public static boolean checkPlateNumberFormat(String content) {
        String pattern = "([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}(([A-HJ-Z]{1}[A-HJ-NP-Z0-9]{5})|([A-HJ-Z]{1}(([DF]{1}[A-HJ-NP-Z0-9]{1}[0-9]{4})|([0-9]{5}[DF]{1})))|([A-HJ-Z]{1}[A-D0-9]{1}[0-9]{3}警)))|([0-9]{6}使)|((([沪粤川云桂鄂陕蒙藏黑辽渝]{1}A)|鲁B|闽D|蒙E|蒙H)[0-9]{4}领)|(WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·•]{1}[0-9]{4}[TDSHBXJ0-9]{1})|([VKHBSLJNGCE]{1}[A-DJ-PR-TVY]{1}[0-9]{5})";
        return Pattern.matches(pattern, content);
    }

    //字段排序校验
    public static OptResult checkColumnOrSorting(Map<String, Object> paramMap, String column) {
        OptResult result = new OptResult();
        String sorting = "DESC,ASC";
        if (!StringUtils.isEmpty(paramMap.get("column")) && !StringUtils.isEmpty(paramMap.get("sorting"))) {
            if (!paramMap.get("column").toString().contains(column) || !sorting.contains(paramMap.get("sorting").toString())) {
                StringBuffer sBuffer = new StringBuffer();
                sBuffer.append("字段排序校验不通过 ");
                sBuffer.append("传值：").append(column).append("不存在排序列").append(paramMap.get("column"));
                sBuffer.append("或者");
                sBuffer.append("传值：").append(paramMap.get("sorting")).append("不存在排序方式").append(sorting);
                result.setResult("0");
                result.setMsg(sBuffer.toString());
            } else {
                result.setResult("1");
                result.setMsg("操作成功");
            }
        }
        return result;
    }
}