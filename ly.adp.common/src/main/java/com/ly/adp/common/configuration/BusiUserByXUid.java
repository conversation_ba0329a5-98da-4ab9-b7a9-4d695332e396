package com.ly.adp.common.configuration;

import com.ly.mp.cloud.api.service.IHandleUser;
import com.ly.mp.component.entities.UserEntity;
import com.ly.mp.component.helper.SessionHelper;
import com.ly.mp.component.helper.SpringContextHolder;

import java.util.Map;

public class BusiUserByXUid implements IHandleUser<UserEntity> {


//    @Autowired
//    AdpBaseServiceRef adpBaseServiceRef;

    @Override
    public UserEntity handleUser(UserEntity userEntity) {
        if (userEntity.getUserID() == null || userEntity.getUserID().isEmpty()) {
            throw new IllegalArgumentException("用户不存在");
        }
        String token = "XUID_" + userEntity.getUserID();
        SessionHelper.put(token, userEntity);
        Map<String, Object> map = SpringContextHolder.getBean(AdpBaseServiceRef.class).getUserLogInfo(token);
        return userEntity;
    }
}
