package com.ly.adp.common.entity;

import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/28
 */
public class ParamQry {

    /**
     * 查询参数
     */
    private Map<String, Object> param;

    public Map<String, Object> getParam() {
        return param;
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ParamQry paramQry = (ParamQry) o;
        return Objects.equals(param, paramQry.param);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(param);
    }

    @Override
    public String toString() {
        return "ParamQry{" +
                "param=" + param +
                '}';
    }

    public static ParamQry buildDwsQry(int pageIndex, int pageSize, Map<String, Object> param) {
        // 构建分页参数
        int pageOffset = 0;
        if (pageIndex > 0) {
            pageOffset = (pageIndex - 1) * pageSize;
        }
        if (pageSize > 0) {
            param.put("pageOffset", pageOffset);
            param.put("pageSize", pageSize);
        }

        // 构建查询
        ParamQry qry = new ParamQry();
        qry.setParam(param);
        return qry;
    }
    public static ParamQry buildDwsQry(Map<String, Object> param) {
        // 构建查询
        ParamQry qry = new ParamQry();
        qry.setParam(param);
        return qry;
    }
}
