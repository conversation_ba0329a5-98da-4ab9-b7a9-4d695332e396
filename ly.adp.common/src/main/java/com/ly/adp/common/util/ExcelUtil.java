package com.ly.adp.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.ly.mp.busicen.common.context.BusicenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * excel 工具类
 * <AUTHOR>
 * @Version 1.0.0
 **/
public class ExcelUtil {

    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * 校验文件类型是否为excel
     * @param file
     * @return
     */
    public static boolean checkIsExcel(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String subffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        return subffix.equalsIgnoreCase("xls") || subffix.equalsIgnoreCase("xlsx");
    }

    /**
     * 读取excel 信息
     * @param file excel文件
     * @param clazz 对应数据实体
     * @return
     * @param <T>
     * @throws IOException
     */
    public static <T> List<T> readExcel(MultipartFile file, Class<T> clazz) throws IOException {
        return EasyExcel.read(file.getInputStream())
                .head(clazz)
                .sheet()
                .doReadSync();
    }

    /**
     * 导出 Excel 文件
     *
     * @param response  HttpServletResponse 对象
     * @param fileName  导出文件名
     * @param dataList  要导出的数据列表
     * @param dataClass 表头
     * @param <T>       数据类型
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, List<T> dataList, Class<T> dataClass) {
        // 设置响应头信息
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        //将文件名称转码再使用
        String newFileName = null;
        try {
            newFileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw BusicenException.create("文件名转码异常");
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + newFileName + ".xlsx");
        try {
            // 创建 ExcelWriterBuilder 对象并注册列宽自适应策略
            EasyExcel.write(response.getOutputStream(), dataClass)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet()
                    .doWrite(dataList);
        } catch (Exception e) {
            log.error("{}导出异常", fileName, e);
        }
    }
}
