package com.ly.adp.common.util;

import com.ly.adp.common.entity.enums.ResultCodeEnum;
import com.ly.mp.busicen.common.util.EntityResultBuilder;
import com.ly.mp.busicen.common.util.ListResultBuilder;
import com.ly.mp.busicen.common.util.OptResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.apache.commons.compress.utils.Lists;

/**
 * 返回结果工具类
 * <AUTHOR>
 * @Version 1.0.0
 **/
public class CommonResultUitl {
    public static final String SUCCESS_CODE = "1";

    /**
     * 判断是否成功
     * @param result
     * @return
     */
    public static boolean isSuccess(String result) {
        return SUCCESS_CODE.equals(result);
    }


    /**
     * 返回结果提示: 调用系统-调用方法-提示信息
     * @param target 调用系统
     * @param method 调用方法
     * @param msg 提示信息
     * @return
     */
    public static String tip(String target, String method, String msg) {
        return "【" + target + "】" + method + "-" + msg;
    }

    /**
     * 返回结果为空时提示
     * @param target 调用系统
     * @param method 调用方法
     * @return
     */
    public static String nullTip(String target, String method) {
        return tip(target, method, "返回结果为空");
    }

    public static OptResult bizOpt(ResultCodeEnum resultCodeEnum) {
        return OptResultBuilder
                .create()
                .result(resultCodeEnum.getCode())
                .Msg(resultCodeEnum.getErrMsg())
                .build();
    }

    public static OptResult bizSuccessOpt(String msg) {
        return OptResultBuilder
                .createOk()
                .Msg(msg)
                .build();
    }

    public static ListResult bizList(ResultCodeEnum resultCodeEnum) {
        return ListResultBuilder
                .create()
                .result(resultCodeEnum.getCode())
                .msg(resultCodeEnum.getErrMsg())
                .rows(Lists.newArrayList())
                .build();
    }

    public static <T> EntityResult<T> bizEntity(ResultCodeEnum resultCodeEnum) {
        return bizEntity(resultCodeEnum, null);
    }

    public static <T> EntityResult<T> bizEntity(ResultCodeEnum resultCodeEnum, T t){
        return bizEntity(resultCodeEnum, t, "");
    }

    public static <T> EntityResult<T> bizEntity(ResultCodeEnum resultCodeEnum, T t, String extInfo){
        return EntityResultBuilder
                .<T>create()
                .result(resultCodeEnum.getCode())
                .msg(resultCodeEnum.getErrMsg())
                .rows(t)
                .extInfo(extInfo)
                .build();
    }
}
