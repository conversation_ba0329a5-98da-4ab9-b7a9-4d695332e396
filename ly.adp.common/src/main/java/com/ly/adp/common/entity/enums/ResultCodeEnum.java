package com.ly.adp.common.entity.enums;

/**
 * drive 返回值枚举
 * <AUTHOR>
 * @Version 1.0.0
 **/
public enum ResultCodeEnum {
    // 通用返回枚举
    FAIL("0", "操作失败"),
    SUCCESS("1", "操作成功"),
    BIZ_ERR("1600040001", "业务异常，异常信息自定义"),
    // 试驾签到相关
    TEST_DRIVE_SIGN_FAILED("1600040003", "试驾签到失败"),
    ;

    private final String code;
    private final String errMsg;

    ResultCodeEnum(String code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public static ResultCodeEnum fromCode(String code) {
        for (ResultCodeEnum resultCodeEnum : ResultCodeEnum.values()) {
            if (resultCodeEnum.getCode().equals(code)) {
                return resultCodeEnum;
            }
        }
        throw new IllegalArgumentException("Invalid ResultCodeEnum code: " + code);
    }

    @Override
    public String toString() {
        return this.errMsg;
    }
}
