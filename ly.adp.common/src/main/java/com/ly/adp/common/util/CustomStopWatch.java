package com.ly.adp.common.util;

import org.springframework.util.StopWatch;

import java.text.NumberFormat;

public class CustomStopWatch extends StopWatch {
    
    public CustomStopWatch(String id) {
        super(id);
    }
    
    @Override
    public String shortSummary() {
        return "CustomStopWatch '" + getId() + "': running time = " +
               formatTime(getTotalTimeNanos());
    }
    
    @Override
    public String prettyPrint() {
        StringBuilder sb = new StringBuilder(shortSummary());
        sb.append('\n');
        
        if (null == getTaskInfo()) {
            sb.append("No task info kept");
            return sb.toString();
        }
        
        TaskInfo[] tasks = getTaskInfo();
        if (tasks.length == 0) {
            return sb.toString();
        }
        
        sb.append("---------------------------------------------\n");
        sb.append("ms         %     Task name\n");
        sb.append("---------------------------------------------\n");
        
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumIntegerDigits(3);
        nf.setGroupingUsed(false);
        
        NumberFormat pf = NumberFormat.getPercentInstance();
        pf.setMinimumIntegerDigits(3);
        pf.setGroupingUsed(false);
        
        for (TaskInfo task : tasks) {
            if (task != null) {
                sb.append(nf.format(task.getTimeNanos() / 1_000_000.0)).append("      ");
                sb.append(pf.format((double) task.getTimeNanos() / getTotalTimeNanos())).append("      ");
                sb.append(task.getTaskName()).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    private String formatTime(long nanos) {
        return String.format("%.2f ms", nanos / 1_000_000.0);
    }
}