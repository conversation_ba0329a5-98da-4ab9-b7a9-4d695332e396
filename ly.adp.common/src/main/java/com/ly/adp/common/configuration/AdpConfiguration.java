package com.ly.adp.common.configuration;

import com.ly.adp.common.ms.AdpMsRabbit;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.bucn.component.cdata.CdataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.annotation.PostConstruct;
import java.io.IOException;

@Configuration
@Import({AdpFeignConfiguration.class, AdpMybatisConfiguration.class, RabbitMQPool.class})
public class AdpConfiguration {

    @Autowired
    CdataManager cdataManager;

    @PostConstruct
    public void init() throws IOException {
        cdataManager.load(null);
    }

    @Bean
    public AdpMsRabbit adpMsRabbit(){
        return new AdpMsRabbit();
    }

}
