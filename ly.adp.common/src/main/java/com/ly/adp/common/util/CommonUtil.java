package com.ly.adp.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.component.helper.StringHelper;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Version 1.0.0
 **/
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    public static final String XTADMIN = "xtadmin";

    /**
     *  邮箱正则校验
     */
    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";

    /**
     *  手机号正则校验
     */
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    /**
     *  身份证号正则校验
     */
    private static final String ID_CARD_REGEX = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private CommonUtil() {
    }

    // ====================== JSON工具 ======================

    /**
     * 实体list信息转为 json 字符串
     * @param list 实体列表
     * @return JSON字符串
     */
    public static <E> String listToJsonString(List<E> list) {
        try {
            // 将 List 转换为 JSON 字符串
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "[]";
        }
    }

    /**
     * 实体信息转为 json 字符串
     * @param obj 实体对象
     * @return JSON字符串
     */
    public static <T> String objToJsonString(T obj) {
        try {
            // 将对象转换为 JSON 字符串
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("实体转换json失败", e);
            return "{}";
        }
    }
    /**
     * JSON字符串转换为实体对象
     * @param <T> 实体对象类型
     * @param json JSON字符串
     * @param clazz 实体对象的Class
     * @return 实体对象
     */
    public static <T> T jsonStringToObj(String json, Class<T> clazz) {
        try {
            // 将 JSON 字符串转换为对象
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JSON转换实体失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转换为实体列表
     * @param <E> 实体列表元素类型
     * @param json JSON字符串
     * @param clazz 实体列表元素的Class
     * @return 实体列表
     */
    public static <E> List<E> jsonStringToList(String json, Class<E> clazz) {
        if (StringHelper.IsEmptyOrNull(json)) {
            return Lists.newArrayList();
        }
        try {
            // 将 JSON 字符串转换为对象列表
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("JSON转换实体列表失败", e);
            return null;
        }
    }

    /**
     * 将13位时间戳转换为年月日时分秒格式的字符串
     * @param timestamp 13位时间戳（毫秒级）
     * @return 格式化的日期时间字符串
     */
    public static String timestampToDateTimeString(long timestamp) {
        // 创建日期对象
        Date date = new Date(timestamp);
        // 定义日期时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将日期对象格式化为字符串
        return dateFormat.format(date);
    }

    // ====================== 集合处理 ======================

    /**
     * 判断集合是否为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 判断集合是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 判断Map是否为空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断Map是否不为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }


    // ====================== 验证工具 ======================

    /**
     * 验证邮箱
     */
    public static boolean isEmail(String email) {
        return Pattern.matches(EMAIL_REGEX, email);
    }

    /**
     * 验证手机号
     */
    public static boolean isPhone(String phone) {
        return Pattern.matches(PHONE_REGEX, phone);
    }

    /**
     * 验证身份证
     */
    public static boolean isIdCard(String idCard) {
        return Pattern.matches(ID_CARD_REGEX, idCard);
    }

    // ====================== 字符串处理 ======================

    /**
     * 判断字符串是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().length() == 0;
    }

    /**
     * 判断字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 判断多个字符串是否都为空
     */
    public static boolean isAllEmpty(String... strings) {
        if (strings == null) {
            return true;
        }
        for (String str : strings) {
            if (isNotEmpty(str)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断多个字符串是否都不为空
     */
    public static boolean isAllNotEmpty(String... strings) {
        if (strings == null) {
            return false;
        }
        for (String str : strings) {
            if (isEmpty(str)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 驼峰转下划线
     */
    public static String camelToUnderline(String str) {
        if (isEmpty(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                sb.append("_").append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 下划线转驼峰
     */
    public static String underlineToCamel(String str) {
        if (isEmpty(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_') {
                upperCase = true;
            } else {
                sb.append(upperCase ? Character.toUpperCase(c) : c);
                upperCase = false;
            }
        }
        return sb.toString();
    }

    // ====================== 文件处理 ======================

    /**
     * 读取文件内容
     */
    public static String readFile(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 写入文件
     */
    public static void writeFile(String content, String filePath) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content);
        }
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (isEmpty(fileName)) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    // ====================== 加密解密 ======================

    /**
     * MD5加密
     */
    public static String md5(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(text.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(bytes);
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }

    /**
     * SHA256加密
     */
    public static String sha256(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] bytes = md.digest(text.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(bytes);
        } catch (Exception e) {
            throw new RuntimeException("SHA256加密失败", e);
        }
    }

    /**
     * AES加密
     */
    public static String aesEncrypt(String content, String password) {
        try {
            SecretKeySpec key = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] result = cipher.doFinal(content.getBytes());
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     */
    public static String aesDecrypt(String content, String password) {
        try {
            SecretKeySpec key = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] result = cipher.doFinal(Base64.getDecoder().decode(content));
            return new String(result);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 字节数组转十六进制
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    // ====================== 数字处理 ======================

    /**
     * 保留指定小数位
     */
    public static double round(double value, int scale) {
        return BigDecimal.valueOf(value)
                .setScale(scale, BigDecimal.ROUND_HALF_UP)
                .doubleValue();
    }

    /**
     * 判断字符串是否为数字
     */
    public static boolean isNumeric(String str) {
        if (isEmpty(str)) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 单号生成器
     * @param dlrId 专营店ID
     * @return 生成的单号
     */
    public static String generateOrderNumber(String prefix, String dlrId) {
        // 如果dlrId为空或者长度为0，使用默认值"000000"
        if (dlrId == null || dlrId.isEmpty()) {
            dlrId = generateRandomNumber(7);
        }

        // 获取当前时间并格式化为8位数字字符串（yyyyMMddHHmmss）
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDate = now.format(formatter);

        // 拼接"HV"前缀、dlrId和当前时间字符串
        String orderNumber = prefix + dlrId + formattedDate;

        // 如果拼接后的单号长度超过16位，则截取前16位
        if (orderNumber.length() > 16) {
            orderNumber = orderNumber.substring(0, 16);
        }

        // 如果拼接后的单号长度小于16位，需要在前面补0
        while (orderNumber.length() < 16) {
            orderNumber += "0"; // 在后面补0
        }

        // 返回生成的单号
        return orderNumber;
    }

    // ====================== 其他工具 ======================

    /**
     * 生成UUID
     */
    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取随机数
     */
    public static int random(int min, int max) {
        return new Random().nextInt(max - min + 1) + min;
    }

    /**
     * 休眠
     */
    public static void sleep(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取异常堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 生成随机的n位数字字符串
     * @param n 需要生成的随机数的位数
     * @return 随机的n位数字字符串
     */
    public static String generateRandomNumber(int n) {
        Random random = new Random();
        int randomNumber = random.nextInt((int) Math.pow(10, n) - 1) + (int) Math.pow(10, n-1);
        return String.format("%0" + n + "d", randomNumber);
    }

    /**
     * 对门店上线状态以及pno18进行筛选
     * @param list
     * @param onlineFlag
     * @param carConfigCode
     * @return
     */
    public  static List checkOnlineFalgAndPno18 (List<Map<String, Object>> list, String onlineFlag, String carConfigCode){

        if (onlineFlag != null && !onlineFlag.equals("")) {
            Iterator<Map<String, Object>> iterator = list.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> list1 = iterator.next();
                if (list1.get("onlineFlag") != null) {
                    if (!list1.get("onlineFlag").equals(onlineFlag)) {
                        iterator.remove(); // 使用迭代器的 remove 方法
                    }
                }else{
                    iterator.remove();
                }
            }
        }
        if (carConfigCode != null && !carConfigCode.equals("")) {
            Iterator<Map<String, Object>> iterator = list.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> list1 = iterator.next();
                String carConfigCode1 = (String) list1.get("carConfigCode");
                if (!carConfigCode1.contains(carConfigCode)) {
                    iterator.remove(); // 使用迭代器的 remove 方法
                }

            }
        }
        if(true){
            Iterator<Map<String, Object>> iterator = list.iterator();
            while(iterator.hasNext()){
                Map<String, Object> list1 = iterator.next();
                if("0".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "未上线");
                }
                if("1".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "已上线");
                }
                if("2".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "即将上线");
                }
                if("3".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "退网中");
                }
                if("4".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "已退网");
                }
                if("5".equals(list1.get("onlineFlag"))){
                    list1.put("onlineFlag", "已下线");
                }
            }
        }
        return  list;
    }

    /**
     * 对列表进行分页处理
     *
     * @param list      需要分页的列表
     * @param pageIndex 当前页码（从1开始）
     * @param pageSize  每页大小
     * @param <T>       列表元素类型
     * @return 分页后的列表
     * @throws IllegalArgumentException 如果pageIndex小于1或pageSize小于1
     * @throws IndexOutOfBoundsException 如果fromIndex > toIndex或超出列表范围
     */
    public static <T> List<T> servicePageHelp(List<T> list, int pageIndex, int pageSize) {
        int fromIndex = (pageIndex - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, list.size());
        return list.subList(fromIndex, toIndex);
    }

    /**
     * 安全返回列表，避免空指针
     *
     * @param list 输入列表
     * @param <T>  列表元素类型
     * @return 如果输入为null则返回空列表，否则返回原列表
     */
    public static <T> List<T> listReturn(List<T> list) {
        return list == null ? Lists.newArrayList() : list;
    }

    /**
     * 是否系统管理员
     * @param token
     * @return
     */
    public static boolean isXtAdmin(String token) {
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        return XTADMIN.equals(userBusiEntity.getEmpName());
    }


}
