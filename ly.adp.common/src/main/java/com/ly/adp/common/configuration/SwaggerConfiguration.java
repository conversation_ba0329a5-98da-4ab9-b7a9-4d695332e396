package com.ly.adp.common.configuration;

import com.google.common.base.Predicate;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;
import java.util.List;

import static com.google.common.base.Predicates.or;
import static springfox.documentation.builders.PathSelectors.regex;

import static com.google.common.base.Predicates.or;
@EnableSwagger2
public class SwaggerConfiguration {
    @Order(5)
    @Bean
    public Docket allApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("API")
                //.globalOperationParameters(parameterList())
                .apiInfo(apiInfo())
                .select()
               // .paths(petstorePaths())
                .paths(PathSelectors.any())
                .build();

    }

    List<Parameter> parameterList(){
        ParameterBuilder aParameterBuilder = new ParameterBuilder();
        aParameterBuilder.name("Authorization").description("用户认证").modelRef(new ModelRef("string")).parameterType("header").required(false).build();
        return Collections.singletonList(aParameterBuilder.build());
    }


    @SuppressWarnings("unchecked")
    private Predicate<String> petstorePaths() {
        return or(
                regex("/ly/adp.*"),
                regex("/ly/busicen.*"),
                regex("/*.*")
        );
    }


    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("微服务管理平台接口")
                .description("微服务管理平台接口")
                .termsOfServiceUrl("http://www.szlanyou.com")
                .contact("深圳联友 MP2.0平台组")
                //.license("Copyright © 2002-2016 LANYOU 联友科技 版权所有")
                //.licenseUrl("http://www.szlanyou.com")
                .version("2.0")
                .build();
    }
}