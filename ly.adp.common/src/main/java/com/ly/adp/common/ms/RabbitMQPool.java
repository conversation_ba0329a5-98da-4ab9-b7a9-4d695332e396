package com.ly.adp.common.ms;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Configuration
@Import({RabbitMQPool.RabbitMQPoolProperties.class})
public class RabbitMQPool implements InitializingBean, DisposableBean {

    @Autowired
    RabbitMQPoolProperties rabbitMQPoolProperties;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (rabbitMQPoolProperties.isInit()) {
            rabbitMQPoolProperties.pool.forEach((k, v) -> initChannelPool(k, v, rabbitMQPoolProperties.config));
        }
    }

    @Override
    public void destroy() throws Exception {
        if (rabbitMQPoolProperties.isInit()) {
            for (GenericObjectPool<Channel> value : poolMap.values()) {
                value.close();
                PooledObjectFactory<Channel> factory = value.getFactory();
                if (factory instanceof ChannelFactory) {
                    ChannelFactory channelFactory = (ChannelFactory) factory;
                    channelFactory.connection.close();
                }
            }
        }
    }

    @Component
    @ConfigurationProperties(prefix = "bucn.rabbitmq")
    public static class RabbitMQPoolProperties {

        private boolean init = true;

        private String master = "master";

        @NestedConfigurationProperty
        private Map<String, RabbitMQProperties> pool;

        @NestedConfigurationProperty
        private GenericObjectPoolConfig config = new GenericObjectPoolConfig();

        public Map<String, RabbitMQProperties> getPool() {
            return pool;
        }

        public void setPool(Map<String, RabbitMQProperties> pool) {
            this.pool = pool;
        }

        public String getMaster() {
            return master;
        }

        public void setMaster(String master) {
            this.master = master;
        }

        public boolean isInit() {
            return init;
        }

        public void setInit(boolean init) {
            this.init = init;
        }

        public GenericObjectPoolConfig getConfig() {
            return config;
        }

        public void setConfig(GenericObjectPoolConfig config) {
            this.config = config;
        }
    }

    private Map<String, GenericObjectPool<Channel>> poolMap = new HashMap<>(4);

    public GenericObjectPool<Channel> objectPoolMaster() {
        return objectPool(rabbitMQPoolProperties.master);
    }

    public GenericObjectPool<Channel> objectPool(String group) {
        GenericObjectPool<Channel> genericObjectPool = poolMap.get(group);
        if (genericObjectPool == null) {
            throw new NullPointerException(String.format("RabbitMQPool Get error,[%s] is absent", group));
        }
        return genericObjectPool;
    }

    public interface Consumer<T> {
        void accept(T t) throws IOException;
    }

    public void channelTemplate(Consumer<Channel> consumer) {
        channelTemplate(rabbitMQPoolProperties.master, consumer);
    }

    public void channelTemplate(String group, Consumer<Channel> consumer) {
        GenericObjectPool<Channel> genericObjectPool = objectPool(group);

        Channel channel = null;
        try {
            try {
                channel = genericObjectPool.borrowObject();
                consumer.accept(channel);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } finally {
            if (channel != null) {
                genericObjectPool.returnObject(channel);
            }
        }
    }

    public GenericObjectPool<Channel> initChannelPool(String group, RabbitMQProperties rabbitMQProperties, GenericObjectPoolConfig genericObjectPoolConfig) {

        if (genericObjectPoolConfig == null) {
            genericObjectPoolConfig = new GenericObjectPoolConfig();
        }
        GenericObjectPool<Channel> channelGenericObjectPool = new GenericObjectPool<>(new ChannelFactory(rabbitMQProperties), genericObjectPoolConfig);
        poolMap.put(group, channelGenericObjectPool);
        return channelGenericObjectPool;
    }

    public static class RabbitMQProperties {

        public RabbitMQProperties() {
        }

        public RabbitMQProperties(String host, Integer port, String username, String password, String virtualhost) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
            this.virtualhost = virtualhost;
        }

        private String host;
        private Integer port;
        private String username;
        private String password;
        private String virtualhost;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getVirtualhost() {
            return virtualhost;
        }

        public void setVirtualhost(String virtualhost) {
            this.virtualhost = virtualhost;
        }
    }

    class ChannelFactory implements PooledObjectFactory<Channel> {

        private ConnectionFactory factory;
        private Connection connection;

        public ChannelFactory(ConnectionFactory factory) {
            this.factory = factory;
        }

        public ChannelFactory(RabbitMQProperties rabbitMQProperties) {
            factory = new ConnectionFactory();
            factory.setAutomaticRecoveryEnabled(true);
            factory.setConnectionTimeout(2000);
            factory.setUsername(rabbitMQProperties.getUsername());
            factory.setPassword(rabbitMQProperties.getPassword());
            factory.setHost(rabbitMQProperties.getHost());
            factory.setPort(rabbitMQProperties.getPort());
            factory.setVirtualHost(rabbitMQProperties.getVirtualhost());
        }


        @Override
        public PooledObject<Channel> makeObject() throws Exception {
            if (connection == null) {
                connection = factory.newConnection();
            }
            Channel channel = connection.createChannel();
            // 池对象创建实例化资源
            return new DefaultPooledObject<>(channel);
        }

        @Override
        public void destroyObject(PooledObject<Channel> p) throws Exception {
            if (p != null && p.getObject() != null && p.getObject().isOpen()) {
                p.getObject().close();
            }
        }

        @Override
        public boolean validateObject(PooledObject<Channel> p) {
            return p.getObject() != null && p.getObject().isOpen();
        }

        @Override
        public void activateObject(PooledObject<Channel> p) throws Exception {

        }

        @Override
        public void passivateObject(PooledObject<Channel> p) throws Exception {

        }
    }

}
