package com.ly.adp.common.cloud;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@ConditionalOnClass({ RequestInterceptor.class })
public class FeignInterceptorEx implements RequestInterceptor {
	public FeignInterceptorEx() {
	}

	public void apply(RequestTemplate requestTemplate) {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes != null) {
			HttpServletRequest request = attributes.getRequest();
			String auth = request.getHeader("Authorization");
			if (auth != null && !auth.isEmpty()) {
				requestTemplate.header("Authorization", new String[] { auth });
			}
			// Add x-smart-tag header to Feign request
			String xSmartTag = request.getHeader("x-smart-tag");
			if (xSmartTag != null && !xSmartTag.isEmpty()) {
				requestTemplate.header("x-smart-tag", new String[]{xSmartTag});
			}
		}
	}
}
