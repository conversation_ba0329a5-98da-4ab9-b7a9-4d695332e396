package com.ly.adp.common.configuration;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "ly.adp.base.service",url="${ref.url.adp.base}")
public interface AdpBaseServiceRef {

    @PostMapping("ly/busicen/base/usc/org/userlogin/getUserLoginInfo.do")
    Map<String,Object> getUserLogInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token);

}
