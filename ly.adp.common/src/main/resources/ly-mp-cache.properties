#Rediséç½®
#å¤ä¸ªserversç¨éå·(",")éå¼,ä¸éè¦éç½®redisä»æºçIP,åªéè¦redisä¸»æºIP
#sentinelæ¨¡å¼çæ ¼å¼masterName?sentineIp1:sentinePort,sentineIp2:sentinePort,sentineIp3:sentinePort ,ä¾å¦mymaster?**************:26379,**************:26379,**************:26379
#å¼å
redis.session.servers =**************:6379
redis.session.password =La<PERSON><PERSON>@2021
redis.servers =**************:6379
#rediså¯ç ,ææredisæå¡å¯ç å¿é¡»ä¸æ ·
redis.password=Lanyou@2021
#æå¤§è¿æ¥çº¿ç¨æ°
redis.pool.maxActive=10000
#è¿æ¥è¶æ¶æ¶é´(åä½:ç§)
redis.pool.timeout=3000
#ç¼å­æ¶é´(åä½:ç§)
redis.pool.expires=86400
#å¨è·åä¸ä¸ªjediså®ä¾æ¶ï¼æ¯å¦æåè¿è¡alidateæä½ï¼å¦æä¸ºtrueï¼åå¾å°çjediså®ä¾åæ¯å¯ç¨çï¼
redis.pool.testOnBorrow=true
#å¨returnç»poolæ¶ï¼æ¯å¦æåè¿è¡validateæä½ï¼
redis.pool.testOnReturn=true

#session time out (åä½:ç§)
session.timeout=1800

#é»è®¤ä¸ºfalseï¼ä¸åæ¥å¿ï¼true åæ¥å¿
#ç¨åºè¿è¡æ¥å¿
RunLog = false
#æå¡è°ç¨æ¥å¿
InvokingLog = false
#ä¸å¡æ°æ®æ¥å¿
BssLog = false

#éè¿MQè½¬åè®°å½æ¥å¿
# NONE:ä¸åæ¥å¿ï¼MQ:åMQï¼FILE:åæä»¶ï¼ALL:æææ¹å¼ï¼MQåæä»¶ï¼ï¼FILEæ¹å¼æ¶ï¼å¹³å°çæ¥å¿åæåè½æ æ³ä½¿ç¨
logStorageType=MQ

# æ¥å¿å½æ¡£
# æ¯å¦å¯ç¨æ¥å¿å½æ¡£
log.job.filing.enable=true
# å½æ¡£å¤ä¹ä¹åçæ°æ®
log.job.filing.interval=30
# å½æ¡£è§¦åçæ¶æºï¼æ¯æ1å·00:04:00
log.job.filing.cron=0 4 0 1 * ?
# æå¡è°ç¨æ¥å¿æåè½ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.invoking.func.cron=0 0 0/2 * * ?
# æå¡è°ç¨æ¥å¿æç¨æ·ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.invoking.user.cron=0 0 0/2 * * ?
# ç³»ç»è¿è¡æ¥å¿æåè½ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.run.func.cron=0 0 0/2 * * ?

#-----------------------------------
#  redissonåå¸å¼ééç½®
# æ¯å¦å¯ç¨åå¸å¼é true:å¯ç¨ false:ä¸å¯ç¨,é»è®¤å¯ç¨,å·¥ä½æµå®æ¶ä»»å¡åæ¥å¿å®æ¶ä»»å¡ä½¿ç¨åå¸å¼é
redisson.redis.enable=true
# éç¾¤æ¶ï¼éè¦ææä¸»ä»èç¹å°å
#redisson.redis.servers=172.26.223.56:6379,172.26.223.57:6379,172.26.223.85:6379,172.26.223.56:6380,172.26.223.57:6380,172.26.223.85:6380
redisson.redis.servers=**************:6379
#redisson.redis.servers=172.26.223.109:6379
redisson.redis.password=Lanyou@2021
# çæ§éççé¨çè¶æ¶ï¼å®æºæè¿ç¨æäºéæ¾éçè¶æ¶æ¶é´ï¼ï¼åä½ï¼æ¯«ç§ãé»è®¤å¼ï¼30000
redisson.redis.lockWatchdogTimeout=20000
# éç¾¤ç¶ææ«æé´éæ¶é´ï¼åä½æ¯æ¯«ç§ãé»è®¤å¼ï¼ 1000
redisson.redis.scanInterval=1000
# å¤ä¸»èç¹çç¯å¢éï¼æ¯ä¸ª ä¸»èç¹çè¿æ¥æ± æå¤§å®¹éãè¿æ¥æ± çè¿æ¥æ°éèªå¨å¼¹æ§ä¼¸ç¼©ãé»è®¤å¼ï¼64
redisson.redis.masterConnectionPoolSize=64
# å¤ä»èç¹çç¯å¢éï¼æ¯ä¸ª ä»æå¡èç¹éç¨äºæ®éæä½ï¼é åå¸åè®¢éï¼è¿æ¥çè¿æ¥æ± æå¤§å®¹éãè¿æ¥æ± çè¿æ¥æ°éèªå¨å¼¹æ§ä¼¸ç¼©ãé»è®¤å¼ï¼64
redisson.redis.slaveConnectionPoolSize=64
