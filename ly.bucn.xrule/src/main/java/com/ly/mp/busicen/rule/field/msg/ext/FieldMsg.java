package com.ly.mp.busicen.rule.field.msg.ext;

import com.ly.mp.busicen.rule.field.msg.IFieldMsg;

public class FieldMsg implements IFieldMsg {

    private String messageId;

    private String messageTitle;

    private String brandCode;

    private String oemCode;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    @Override
    public String getName() {
        return "msg";
    }

    @Override
    public String getLabel() {

        return getMessageId();
    }

    @Override
    public String getMsg() {
        //return messageTitle;
        return getMessageTitle();
    }

}
