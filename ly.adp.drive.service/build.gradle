apply from: "$rootDir/gradle/docker.gradle"
//apply plugin: "org.cyclonedx.bom"
//apply plugin: 'org.owasp.dependencycheck'

//buildscript {
//    dependencies {
//        classpath "com.cyclonedx:cyclonedx-gradle-plugin:1.5.0"
//        classpath 'org.owasp:dependency-check-gradle:7.0.3'
//    }
//}
ext{
    busiJarVersion = "1.0-SNAPSHOT"
}
dependencies {

	compile project(':ly.adp.common')
    compile project(':ly.bucn.xrule')
    compile project(':ly.adp.drive.protocol')

	compile project(':ly.adp.drive.otherservice.interface')	
	compile fileTree(dir:"$rootDir/extjar/bucn/bucnpack", include: ['*.jar'])
    compile libs.swagger2Core

    // Hutool 工具类
    implementation 'cn.hutool:hutool-all:5.6.5'
}

if (!project.hasProperty('upload')){
	apply from: "$rootDir/gradle/springboot.gradle"
}



