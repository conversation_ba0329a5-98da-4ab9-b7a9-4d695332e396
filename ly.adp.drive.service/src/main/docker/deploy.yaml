apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    field.cattle.io/creatorId: user-s95l8
  creationTimestamp: "2020-08-31T10:29:33Z"
  generation: 1
  labels:
    cattle.io/creator: norman
    workload.user.cattle.io/workloadselector: deployment-simpleproject2-simpleproject
  name: simpleproject
  namespace: simpleproject2
  resourceVersion: "312231"
  selfLink: /apis/apps/v1/namespaces/simpleproject2/deployments/simpleproject
  uid: d6209faf-850d-474c-bbab-229efdb8b26c
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      workload.user.cattle.io/workloadselector: deployment-simpleproject2-simpleproject
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        cattle.io/timestamp: "2020-08-31T10:29:33Z"
      creationTimestamp: null
      labels:
        workload.user.cattle.io/workloadselector: deployment-simpleproject2-simpleproject
    spec:
      containers:
      - env:
        - name: app_name
          value: ly-mp-module-cloud
        - name: dev_meta
          value: nacos-headless.mp-msa:8848
        - name: env
          value: mysql
        image: 172.26.165.197:5000/lycloud/ly.mp.project.module.service
        imagePullPolicy: Always
        name: simpleproject
        resources: {}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities: {}
          privileged: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
        stdin: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        tty: true
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
status:
  availableReplicas: 1
  conditions:
  - lastTransitionTime: "2020-08-31T10:29:35Z"
    lastUpdateTime: "2020-08-31T10:29:35Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  - lastTransitionTime: "2020-08-31T10:29:33Z"
    lastUpdateTime: "2020-08-31T10:29:35Z"
    message: ReplicaSet "simpleproject-8458896f5" has successfully progressed.
    reason: NewReplicaSetAvailable
    status: "True"
    type: Progressing
  observedGeneration: 1
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
