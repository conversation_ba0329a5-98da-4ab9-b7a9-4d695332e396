#FROM swr.cn-east-3.myhuaweicloud.com/adp/alpine-oraclejdk8:v2
FROM swr.cn-east-3.myhuaweicloud.com/smart/skywalking-java-agent:8.5.0-jdk8

ENV app_name=ly.adp.drive.service
ENV configDir /home/<USER>/config

ADD $app_name/build/libs/*.jar /home/<USER>/app.jar


ENV env dev
ENV TZ=Asia/Shanghai

ENV JAVA_OPTS="	-XX:+UseContainerSupport"

#skywalking
# ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES="skywalking-oap.skywalking.svc:11800"
# ENV JAVA_TOOL_OPTIONS="-javaagent:/skywalking/agent/skywalking-agent.jar"
COPY --from=swr.cn-east-3.myhuaweicloud.com/adp/arthas:v1 /opt/arthas /mss/arthas
USER root
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add --update ttf-dejavu fontconfig && \
		rm -rf /var/cache/apk/*

#USER mpjava
WORKDIR /home/<USER>
#ENTRYPOINT
ENTRYPOINT  java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd echo 'java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/app.jar' >start.sh && chmod 750 start.sh && ./start.sh
