<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.drive.idal.mapper.SacTestDriveSheetHisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.drive.entities.SacTestDriveSheetHis">
        <id column="TEST_DRIVE_SHEET_ID" property="testDriveSheetId" />
        <result column="TEST_DRIVE_SHEET_HIS_ID" property="testDriveSheetHisId" />
        <result column="TEST_DRIVE_ORDER_NO" property="testDriveOrderNo" />
        <result column="APPOINTMENT_ID" property="appointmentId" />
        <result column="TEST_STATUS" property="testStatus" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="SALES_CONSULTANT_NAME" property="salesConsultantName" />
        <result column="SALES_CONSULTANT_ID" property="salesConsultantId" />
        <result column="INTEN_LEVEL" property="intenLevel" />
        <result column="DRIVER_CUSTOMER_RELATION" property="driverCustomerRelation" />
        <result column="DRIVER_NAME" property="driverName" />
        <result column="DRIVER_PHONE" property="driverPhone" />
        <result column="DRIVING_LICENCE_TYPE" property="drivingLicenceType" />
        <result column="DRIVING_LICENCE_NUMBER" property="drivingLicenceNumber" />
        <result column="ADDRESS" property="address" />
        <result column="DRIVING_LICENCE_PHOTO" property="drivingLicencePhoto" />
        <result column="TEST_ROAD_HAUL" property="testRoadHaul" />
        <result column="TEST_START_ROAD_HAUL" property="testStartRoadHaul" />
        <result column="TEST_END_ROAD_HAUL" property="testEndRoadHaul" />
        <result column="DLR_CLUE_ORDER_NO" property="dlrClueOrderNo" />
        <result column="CUSTOMER_NAME" property="customerName" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="CUSTOMER_PHONE" property="customerPhone" />
        <result column="CUSTOMER_SEX" property="customerSex" />
        <result column="SMALL_CAR_TYPE_CODE" property="smallCarTypeCode" />
        <result column="SMALL_CAR_TYPE_NAME" property="smallCarTypeName" />
        <result column="PLATE_NUMBER" property="plateNumber" />
        <result column="CAR_VIN" property="carVin" />
        <result column="TEST_TYPE" property="testType" />
        <result column="APPOINTMENT_CHANNEL" property="appointmentChannel" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="TEST_DRIVE_AGREEMENT" property="testDriveAgreement" />
        <result column="CUSTOMER_ID_NUMBER_AGREEMENT" property="customerIdNumberAgreement" />
        <result column="CUSTOMER_SIGNATURE_AGREEMENT" property="customerSignatureAgreement" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
        <result column="DEPOSIT" property="deposit" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TEST_DRIVE_SHEET_HIS_ID, TEST_DRIVE_SHEET_ID, TEST_DRIVE_ORDER_NO, APPOINTMENT_ID, TEST_STATUS, DLR_CODE, DLR_NAME, SALES_CONSULTANT_NAME, SALES_CONSULTANT_ID, INTEN_LEVEL, DRIVER_CUSTOMER_RELATION, DRIVER_NAME, DRIVER_PHONE, DRIVING_LICENCE_TYPE, DRIVING_LICENCE_NUMBER, ADDRESS, DRIVING_LICENCE_PHOTO, TEST_ROAD_HAUL, TEST_START_ROAD_HAUL, TEST_END_ROAD_HAUL, DLR_CLUE_ORDER_NO, CUSTOMER_NAME, CUSTOMER_ID, CUSTOMER_PHONE, CUSTOMER_SEX, SMALL_CAR_TYPE_CODE, SMALL_CAR_TYPE_NAME, PLATE_NUMBER, CAR_VIN, TEST_TYPE, APPOINTMENT_CHANNEL, START_TIME, END_TIME, TEST_DRIVE_AGREEMENT, CUSTOMER_ID_NUMBER_AGREEMENT, CUSTOMER_SIGNATURE_AGREEMENT, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, UPDATE_CONTROL_ID, IS_ENABLE, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10, DEPOSIT, REMARK
    </sql>
    <!-- 插入sql -->
	<insert id="insertSacTestDriveSheetHis">
	    insert into t_sac_test_drive_sheet_his(
        TEST_DRIVE_SHEET_HIS_ID,
        TEST_DRIVE_SHEET_ID,
        TEST_DRIVE_ORDER_NO,
        APPOINTMENT_ID,
        TEST_STATUS,
        DLR_CODE,
        DLR_NAME,
        SALES_CONSULTANT_NAME,
        SALES_CONSULTANT_ID,
        INTEN_LEVEL,
        DRIVER_CUSTOMER_RELATION,
        DRIVER_NAME,
        DRIVER_PHONE,
        DRIVING_LICENCE_TYPE,
        DRIVING_LICENCE_NUMBER,
        ADDRESS,
        DRIVING_LICENCE_PHOTO,
        TEST_ROAD_HAUL,
        TEST_START_ROAD_HAUL,
        TEST_END_ROAD_HAUL,
        DLR_CLUE_ORDER_NO,
        CUSTOMER_NAME,
        CUSTOMER_ID,
        CUSTOMER_PHONE,
        CUSTOMER_SEX,
        SMALL_CAR_TYPE_CODE,
        SMALL_CAR_TYPE_NAME,
        PLATE_NUMBER,
        CAR_VIN,
        TEST_TYPE,
        APPOINTMENT_CHANNEL,
        START_TIME,
        END_TIME,
        TEST_DRIVE_AGREEMENT,
        CUSTOMER_ID_NUMBER_AGREEMENT,
        CUSTOMER_SIGNATURE_AGREEMENT,
        OEM_ID,
        GROUP_ID,
        CREATOR,
        CREATED_NAME,
        CREATED_DATE,
        MODIFIER,
        MODIFY_NAME,
        LAST_UPDATED_DATE,
        UPDATE_CONTROL_ID,
        COLUMN1,
        COLUMN2,
        COLUMN3,
        COLUMN4,
        COLUMN5,
        COLUMN6,
        COLUMN7,
        COLUMN8,
        COLUMN9,
        COLUMN10,
        DEPOSIT,
        REMARK,
		IS_ENABLE)
		values(
               #{param.testDriveSheetHisId},
               #{param.testDriveSheetId},
               #{param.testDriveOrderNo},
               #{param.appointmentId},
               #{param.testStatus},
               #{param.dlrCode},
               #{param.dlrName},
               #{param.salesConsultantName},
               #{param.salesConsultantId},
               #{param.intenLevel},
               #{param.driverCustomerRelation},
               #{param.driverName},
               #{param.driverPhone},
               #{param.drivingLicenceType},
               #{param.drivingLicenceNumber},
               #{param.address},
               #{param.drivingLicencePhoto},
               #{param.testRoadHaul},
               #{param.testStartRoadHaul},
               #{param.testEndRoadHaul},
               #{param.dlrClueOrderNo},
               #{param.customerName},
               #{param.customerId},
               #{param.customerPhone},
               #{param.customerSex},
               #{param.smallCarTypeCode},
               #{param.smallCarTypeName},
               #{param.plateNumber},
               #{param.carVin},
               #{param.testType},
               #{param.appointmentChannel},
               #{param.startTime},
               #{param.endTime},
               #{param.testDriveAgreement},
               #{param.customerIdNumberAgreement},
               #{param.customerSignatureAgreement},
               #{param.oemId},
               #{param.groupId},
               #{param.creator},
               #{param.createdName},
               #{param.createdDate},
               #{param.modifier},
               #{param.modifyName},
               #{param.lastUpdatedDate},
               #{param.updateControlId},
               #{param.column1},
               #{param.column2},
               #{param.column3},
               #{param.column4},
               #{param.column5},
               #{param.column6},
               #{param.column7},
               #{param.column8},
               #{param.column9},
               #{param.column10},
               #{param.deposit},
               #{param.remark},
		#{param.isEnable})
	</insert>
  
</mapper>
