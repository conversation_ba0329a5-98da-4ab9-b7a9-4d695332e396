<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.drive.idal.mapper.SacTestDriveTaskMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.adp.drive.entities.SacTestDriveTask">
        <id column="ID" property="id"/>
        <result column="TASK_TITLE" property="taskTitle"/>
        <result column="CUST_NAME" property="custName"/>
        <result column="PHONE" property="phone"/>
        <result column="GENDER_CODE" property="genderCode"/>
        <result column="GENDER_NAME" property="genderName"/>
        <result column="TEST_TYPE" property="testType"/>
        <result column="TASK_PERSON_ID" property="taskPersonId"/>
        <result column="TASK_PERSON_CODE" property="taskPersonCode"/>
        <result column="TASK_PERSON_NAME" property="taskPersonName"/>
        <result column="TASK_PERSON_DLR_CODE" property="taskPersonDlrCode"/>
        <result column="TASK_PERSON_DLR_NAME" property="taskPersonDlrName"/>
        <result column="TASK_STATE_CODE" property="taskStateCode"/>
        <result column="TASK_STATE_NAME" property="taskStateName"/>
        <result column="REMARK" property="remark"/>
        <result column="BUSS_TIME" property="bussTime"/>
        <result column="SMALL_CAR_TYPE_NAME" property="smallCarTypeName"/>
        <result column="APPOINTMENT_TEST_DATE" property="appointmentTestDate"/>
        <result column="APPOINTMENT_TEST_TIME" property="appointmentTestTime"/>
        <result column="APPOINTMENT_START_TIME" property="appointmentStartTime"/>
        <result column="APPOINTMENT_END_TIME" property="appointmentEndTime"/>
        <result column="SEND_DLR_NAME" property="sendDlrName"/>
        <result column="SALES_CONSULTANT_NAME" property="salesConsultantName"/>
        <result column="MSG_TEST_TYPE" property="msgTestType"/>
        <result column="OLD_TEST_DRIVE_SHEET_ID" property="oldTestDriveSheetId"/>
        <result column="INTEN_LEVEL_CODE" property="intenLevelCode"/>
        <result column="INTEN_LEVEL_NAME" property="intenLevelName"/>
        <result column="INTEN_CAR_TYPE_NAME" property="intenCarTypeName"/>
        <result column="PLAN_BUY_DATE_NAME" property="planBuyDateName"/>
        <result column="CHANNEL_NAME" property="channelName"/>
        <result column="NEW_TEST_DRIVE_SHEET_ID" property="newTestDriveSheetId"/>
        <result column="EXTEND_JSON" property="extendJson"/>
        <result column="COLUMN1" property="column1"/>
        <result column="COLUMN2" property="column2"/>
        <result column="COLUMN3" property="column3"/>
        <result column="COLUMN4" property="column4"/>
        <result column="COLUMN5" property="column5"/>
        <result column="_MYCAT_OP_TIME" property="mycatOpTime"/>
        <result column="OEM_ID" property="oemId"/>
        <result column="GROUP_ID" property="groupId"/>
        <result column="OEM_CODE" property="oemCode"/>
        <result column="GROUP_CODE" property="groupCode"/>
        <result column="CREATOR" property="creator"/>
        <result column="CREATED_NAME" property="createdName"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="MODIFIER" property="modifier"/>
        <result column="MODIFY_NAME" property="modifyName"/>
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate"/>
        <result column="IS_ENABLE" property="isEnable"/>
        <result column="SDP_USER_ID" property="sdpUserId"/>
        <result column="SDP_ORG_ID" property="sdpOrgId"/>
        <result column="UPDATE_CONTROL_ID" property="updateControlId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , TASK_TITLE, CUST_NAME, PHONE, GENDER_CODE, GENDER_NAME, TEST_TYPE, TASK_PERSON_ID, TASK_PERSON_CODE, TASK_PERSON_NAME, TASK_PERSON_DLR_CODE, TASK_PERSON_DLR_NAME, TASK_STATE_CODE, TASK_STATE_NAME, REMARK, BUSS_TIME, SMALL_CAR_TYPE_NAME, APPOINTMENT_TEST_DATE, APPOINTMENT_TEST_TIME, APPOINTMENT_START_TIME, APPOINTMENT_END_TIME, SEND_DLR_NAME, SALES_CONSULTANT_NAME, MSG_TEST_TYPE, OLD_TEST_DRIVE_SHEET_ID, INTEN_LEVEL_CODE, INTEN_LEVEL_NAME, INTEN_CAR_TYPE_NAME, PLAN_BUY_DATE_NAME, CHANNEL_NAME, NEW_TEST_DRIVE_SHEET_ID, EXTEND_JSON, COLUMN1, COLUMN2, COLUMN3, COLUMN4, COLUMN5, _MYCAT_OP_TIME, OEM_ID, GROUP_ID, OEM_CODE, GROUP_CODE, CREATOR, CREATED_NAME, CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE, IS_ENABLE, SDP_USER_ID, SDP_ORG_ID, UPDATE_CONTROL_ID,CL_SOURCE5,CL_SOURCE6,X_CHANNEL_ID
    </sql>

    <!-- 通用查询映射结果 -->
    <sql id="where_condition">
        <!-- 时间查询 -->
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>

        <if test="param.id !=null and param.id !=''">
            and ID=#{param.id}
        </if>
        <if test="param.taskTitle !=null and param.taskTitle !=''">
            and TASK_TITLE=#{param.taskTitle}
        </if>
        <if test="param.custName !=null and param.custName !=''">
            and CUST_NAME=#{param.custName}
        </if>
        <if test="param.phone !=null and param.phone !=''">
            and PHONE=#{param.phone}
        </if>
        <if test="param.genderCode !=null and param.genderCode !=''">
            and GENDER_CODE=#{param.genderCode}
        </if>
        <if test="param.genderName !=null and param.genderName !=''">
            and GENDER_NAME=#{param.genderName}
        </if>
        <if test="param.testType !=null and param.testType !=''">
            and TEST_TYPE=#{param.testType}
        </if>
        <if test="param.taskPersonId !=null and param.taskPersonId !=''">
            and TASK_PERSON_ID=#{param.taskPersonId}
        </if>
        <if test="param.taskPersonCode !=null and param.taskPersonCode !=''">
            and TASK_PERSON_CODE=#{param.taskPersonCode}
        </if>
        <if test="param.taskPersonName !=null and param.taskPersonName !=''">
            and TASK_PERSON_NAME=#{param.taskPersonName}
        </if>
        <if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">
            and TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode}
        </if>
        <if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">
            and TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName}
        </if>
        <if test="param.taskStateCode !=null and param.taskStateCode !=''">
            and TASK_STATE_CODE=#{param.taskStateCode}
        </if>
        <if test="param.taskStateName !=null and param.taskStateName !=''">
            and TASK_STATE_NAME=#{param.taskStateName}
        </if>
        <if test="param.remark !=null and param.remark !=''">
            and REMARK=#{param.remark}
        </if>
        <if test="param.bussTime !=null and param.bussTime !=''">
            and BUSS_TIME=#{param.bussTime}
        </if>
        <if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">
            and SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}
        </if>
        <if test="param.appointmentTestDate !=null and param.appointmentTestDate !=''">
            and APPOINTMENT_TEST_DATE=#{param.appointmentTestDate}
        </if>
        <if test="param.appointmentTestTime !=null and param.appointmentTestTime !=''">
            and APPOINTMENT_TEST_TIME=#{param.appointmentTestTime}
        </if>
        <if test="param.appointmentStartTime !=null and param.appointmentStartTime !=''">
            and APPOINTMENT_START_TIME=#{param.appointmentStartTime}
        </if>
        <if test="param.appointmentEndTime !=null and param.appointmentEndTime !=''">
            and APPOINTMENT_END_TIME=#{param.appointmentEndTime}
        </if>
        <if test="param.sendDlrName !=null and param.sendDlrName !=''">
            and SEND_DLR_NAME=#{param.sendDlrName}
        </if>
        <if test="param.salesConsultantName !=null and param.salesConsultantName !=''">
            and SALES_CONSULTANT_NAME=#{param.salesConsultantName}
        </if>
        <if test="param.msgTestType !=null and param.msgTestType !=''">
            and MSG_TEST_TYPE=#{param.msgTestType}
        </if>
        <if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">
            and OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}
        </if>
        <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
            and INTEN_LEVEL_CODE=#{param.intenLevelCode}
        </if>
        <if test="param.intenLevelName !=null and param.intenLevelName !=''">
            and INTEN_LEVEL_NAME=#{param.intenLevelName}
        </if>
        <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
            and INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
        </if>
        <if test="param.planBuyDateName !=null and param.planBuyDateName !=''">
            and PLAN_BUY_DATE_NAME=#{param.planBuyDateName}
        </if>
        <if test="param.channelName !=null and param.channelName !=''">
            and CHANNEL_NAME=#{param.channelName}
        </if>
        <if test="param.newTestDriveSheetId !=null and param.newTestDriveSheetId !=''">
            and NEW_TEST_DRIVE_SHEET_ID=#{param.newTestDriveSheetId}
        </if>
        <if test="param.extendJson !=null and param.extendJson !=''">
            and EXTEND_JSON=#{param.extendJson}
        </if>
        <if test="param.column1 !=null and param.column1 !=''">
            and COLUMN1=#{param.column1}
        </if>
        <if test="param.column2 !=null and param.column2 !=''">
            and COLUMN2=#{param.column2}
        </if>
        <if test="param.column3 !=null and param.column3 !=''">
            and COLUMN3=#{param.column3}
        </if>
        <if test="param.column4 !=null and param.column4 !=''">
            and COLUMN4=#{param.column4}
        </if>
        <if test="param.column5 !=null and param.column5 !=''">
            and COLUMN5=#{param.column5}
        </if>
        <if test="param.mycatOpTime !=null and param.mycatOpTime !=''">
            and _MYCAT_OP_TIME=#{param.mycatOpTime}
        </if>
        <if test="param.oemId !=null and param.oemId !=''">
            and OEM_ID=#{param.oemId}
        </if>
        <if test="param.groupId !=null and param.groupId !=''">
            and GROUP_ID=#{param.groupId}
        </if>
        <if test="param.oemCode !=null and param.oemCode !=''">
            and OEM_CODE=#{param.oemCode}
        </if>
        <if test="param.groupCode !=null and param.groupCode !=''">
            and GROUP_CODE=#{param.groupCode}
        </if>
        <if test="param.creator !=null and param.creator !=''">
            and CREATOR=#{param.creator}
        </if>
        <if test="param.createdName !=null and param.createdName !=''">
            and CREATED_NAME=#{param.createdName}
        </if>
        <if test="param.createdDate !=null and param.createdDate !=''">
            and CREATED_DATE=#{param.createdDate}
        </if>
        <if test="param.modifier !=null and param.modifier !=''">
            and MODIFIER=#{param.modifier}
        </if>
        <if test="param.modifyName !=null and param.modifyName !=''">
            and MODIFY_NAME=#{param.modifyName}
        </if>
        <if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">
            and LAST_UPDATED_DATE=#{param.lastUpdatedDate}
        </if>
        <if test="param.isEnable !=null and param.isEnable !=''">
            and IS_ENABLE=#{param.isEnable}
        </if>
        <if test="param.sdpUserId !=null and param.sdpUserId !=''">
            and SDP_USER_ID=#{param.sdpUserId}
        </if>
        <if test="param.sdpOrgId !=null and param.sdpOrgId !=''">
            and SDP_ORG_ID=#{param.sdpOrgId}
        </if>
        <if test="param.updateControlId !=null and param.updateControlId !=''">
            and UPDATE_CONTROL_ID=#{param.updateControlId}
        </if>
    </sql>

    <!-- 试驾任务 信息查询 -->
    <select id="querySacTestDriveTask" resultType="map">
        select
        t.ID,
        t.TASK_TITLE,
        t.CUST_NAME,
        t.PHONE,
        t.GENDER_CODE,
        t.GENDER_NAME,
        t.TEST_TYPE,
        t.TASK_PERSON_ID,
        t.TASK_PERSON_CODE,
        t.TASK_PERSON_NAME,
        t.TASK_PERSON_DLR_CODE,
        t.TASK_PERSON_DLR_NAME,
        t.TASK_STATE_CODE,
        t.TASK_STATE_NAME,
        t.REMARK,
        t.BUSS_TIME,
        t.SMALL_CAR_TYPE_NAME,
        t.APPOINTMENT_TEST_DATE,
        t.APPOINTMENT_TEST_TIME,
        t.APPOINTMENT_START_TIME,
        t.APPOINTMENT_END_TIME,
        t.SEND_DLR_NAME,
        t.SALES_CONSULTANT_NAME,
        t.MSG_TEST_TYPE,
        t.OLD_TEST_DRIVE_SHEET_ID,
        t.INTEN_LEVEL_CODE,
        t.INTEN_LEVEL_NAME,
        t.INTEN_CAR_TYPE_NAME,
        t.PLAN_BUY_DATE_NAME,
        t.CHANNEL_NAME,
        t.NEW_TEST_DRIVE_SHEET_ID,
        t.EXTEND_JSON,
        t.COLUMN1,
        t.COLUMN2,
        t.COLUMN3,
        t.COLUMN4,
        t.COLUMN5,
        t._MYCAT_OP_TIME,
        t.OEM_ID,
        t.GROUP_ID,
        t.OEM_CODE,
        t.GROUP_CODE,
        t.CREATOR,
        t.CREATED_NAME,
        t.CREATED_DATE,
        t.MODIFIER,
        t.MODIFY_NAME,
        t.LAST_UPDATED_DATE,
        t.IS_ENABLE,
        t.SDP_USER_ID,
        t.SDP_ORG_ID,
        t.UPDATE_CONTROL_ID,
        ts.`REAL_NAME` AS realName,
        ts.`TEST_DRIVE_AGREEMENT_PDF` AS testDriveAgreementPdf,
        ts.`CUSTOMER_ID_NUMBER` AS customerIdNumber,
        '0' as isDriveOrTask,CONCAT(s.STATION_NAME,'(',s.ORG_ID,')') as STATION_NAME,
        ts.CUSTOMER_ID as custId
        from csc.t_sac_test_drive_task t
        LEFT JOIN (select t2.DLR_CODE,t3.AGENT_COMPANY_ID,t4.AGENT_ID FROM mp.t_usc_mdm_org_dlr t2
        LEFT JOIN mp.t_usc_mdm_agent_company t3 ON t2.COMPANY_ID = t3.AGENT_COMPANY_ID
        LEFT JOIN mp.t_usc_mdm_agent_info t4 ON t4.AGENT_ID = t3.AGENT_ID) d on t.TASK_PERSON_DLR_CODE=d.DLR_CODE
        left join mp.t_usc_mdm_org_employee e on e.USER_ID=t.TASK_PERSON_ID
        left join mp.t_usc_mdm_org_station s on s.STATION_ID=e.STATION_ID
        left join csc.t_sac_test_drive_sheet ts on t.NEW_TEST_DRIVE_SHEET_ID = ts.TEST_DRIVE_SHEET_ID
        where 1=1
        <if test="param.isLabel !=null and param.isLabel !=''">
            and t.TASK_STATE_CODE in('0','1')
        </if>
        <if test="param.isLabels !=null and param.isLabels !=''">
            and t.TASK_STATE_CODE in('0','2')
        </if>
        <!-- 时间查询 -->
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and t.CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and t.CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and t.BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and t.BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>

        <if test="param.id !=null and param.id !=''">
            and t.ID=#{param.id}
        </if>
        <if test="param.taskTitle !=null and param.taskTitle !=''">
            and t.TASK_TITLE=#{param.taskTitle}
        </if>
        <if test="param.custName !=null and param.custName !=''">
            and t.CUST_NAME=#{param.custName}
        </if>
        <if test="param.phone !=null and param.phone !=''">
            and t.PHONE=#{param.phone}
        </if>
        <if test="param.genderCode !=null and param.genderCode !=''">
            and t.GENDER_CODE=#{param.genderCode}
        </if>
        <if test="param.genderName !=null and param.genderName !=''">
            and t.GENDER_NAME=#{param.genderName}
        </if>
        <if test="param.testType !=null and param.testType !=''">
            and t.TEST_TYPE=#{param.testType}
        </if>
        <if test="param.taskPersonId !=null and param.taskPersonId !=''">
            and t.TASK_PERSON_ID=#{param.taskPersonId}
        </if>
        <if test="param.taskPersonCode !=null and param.taskPersonCode !=''">
            and t.TASK_PERSON_CODE=#{param.taskPersonCode}
        </if>
        <if test="param.taskPersonName !=null and param.taskPersonName !=''">
            and t.TASK_PERSON_NAME=#{param.taskPersonName}
        </if>
        <if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">
            and t.TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode}
        </if>
        <if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">
            and t.TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName}
        </if>
        <if test="param.taskStateCode !=null and param.taskStateCode !=''">
            and t.TASK_STATE_CODE=#{param.taskStateCode}
        </if>
        <if test="param.taskStateName !=null and param.taskStateName !=''">
            and t.TASK_STATE_NAME=#{param.taskStateName}
        </if>
        <if test="param.remark !=null and param.remark !=''">
            and t.REMARK=#{param.remark}
        </if>
        <if test="param.bussTime !=null and param.bussTime !=''">
            and t.BUSS_TIME=#{param.bussTime}
        </if>
        <if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">
            and t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}
        </if>
        <if test="param.appointmentTestDate !=null and param.appointmentTestDate !=''">
            and t.APPOINTMENT_TEST_DATE=#{param.appointmentTestDate}
        </if>
        <if test="param.appointmentTestTime !=null and param.appointmentTestTime !=''">
            and t.APPOINTMENT_TEST_TIME=#{param.appointmentTestTime}
        </if>
        <if test="param.appointmentStartTime !=null and param.appointmentStartTime !=''">
            and t.APPOINTMENT_START_TIME=#{param.appointmentStartTime}
        </if>
        <if test="param.appointmentEndTime !=null and param.appointmentEndTime !=''">
            and t.APPOINTMENT_END_TIME=#{param.appointmentEndTime}
        </if>
        <if test="param.sendDlrName !=null and param.sendDlrName !=''">
            and t.SEND_DLR_NAME=#{param.sendDlrName}
        </if>
        <if test="param.salesConsultantName !=null and param.salesConsultantName !=''">
            and t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}
        </if>
        <if test="param.msgTestType !=null and param.msgTestType !=''">
            and t.MSG_TEST_TYPE=#{param.msgTestType}
        </if>
        <if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">
            and t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}
        </if>
        <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
            and t.INTEN_LEVEL_CODE=#{param.intenLevelCode}
        </if>
        <if test="param.intenLevelName !=null and param.intenLevelName !=''">
            and t.INTEN_LEVEL_NAME=#{param.intenLevelName}
        </if>
        <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
            and t.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
        </if>
        <if test="param.planBuyDateName !=null and param.planBuyDateName !=''">
            and t.PLAN_BUY_DATE_NAME=#{param.planBuyDateName}
        </if>
        <if test="param.channelName !=null and param.channelName !=''">
            and t.CHANNEL_NAME=#{param.channelName}
        </if>
        <if test="param.newTestDriveSheetId !=null and param.newTestDriveSheetId !=''">
            and t.NEW_TEST_DRIVE_SHEET_ID=#{param.newTestDriveSheetId}
        </if>
        <if test="param.extendJson !=null and param.extendJson !=''">
            and t.EXTEND_JSON=#{param.extendJson}
        </if>
        <if test="param.column1 !=null and param.column1 !=''">
            and t.COLUMN1=#{param.column1}
        </if>
        <if test="param.column2 !=null and param.column2 !=''">
            and t.COLUMN2=#{param.column2}
        </if>
        <if test="param.column3 !=null and param.column3 !=''">
            and t.COLUMN3=#{param.column3}
        </if>
        <if test="param.column4 !=null and param.column4 !=''">
            and t.COLUMN4=#{param.column4}
        </if>
        <if test="param.column5 !=null and param.column5 !=''">
            and t.COLUMN5=#{param.column5}
        </if>
        <if test="param.mycatOpTime !=null and param.mycatOpTime !=''">
            and t._MYCAT_OP_TIME=#{param.mycatOpTime}
        </if>
        <if test="param.oemId !=null and param.oemId !=''">
            and t.OEM_ID=#{param.oemId}
        </if>
        <if test="param.groupId !=null and param.groupId !=''">
            and t.GROUP_ID=#{param.groupId}
        </if>
        <if test="param.oemCode !=null and param.oemCode !=''">
            and t.OEM_CODE=#{param.oemCode}
        </if>
        <if test="param.groupCode !=null and param.groupCode !=''">
            and t.GROUP_CODE=#{param.groupCode}
        </if>
        <if test="param.creator !=null and param.creator !=''">
            and t.CREATOR=#{param.creator}
        </if>
        <if test="param.createdName !=null and param.createdName !=''">
            and t.CREATED_NAME=#{param.createdName}
        </if>
        <if test="param.createdDate !=null and param.createdDate !=''">
            and t.CREATED_DATE=#{param.createdDate}
        </if>
        <if test="param.modifier !=null and param.modifier !=''">
            and t.MODIFIER=#{param.modifier}
        </if>
        <if test="param.modifyName !=null and param.modifyName !=''">
            and t.MODIFY_NAME=#{param.modifyName}
        </if>
        <if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">
            and t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
        </if>
        <if test="param.isEnable !=null and param.isEnable !=''">
            and t.IS_ENABLE=#{param.isEnable}
        </if>
        <if test="param.sdpUserId !=null and param.sdpUserId !=''">
            and t.SDP_USER_ID=#{param.sdpUserId}
        </if>
        <if test="param.sdpOrgId !=null and param.sdpOrgId !=''">
            and t.SDP_ORG_ID=#{param.sdpOrgId}
        </if>
        <if test="param.updateControlId !=null and param.updateControlId !=''">
            and t.UPDATE_CONTROL_ID=#{param.updateControlId}
        </if>
        <if test="param.agentId !=null and param.agentId !=''">
            and d.AGENT_ID=#{param.agentId}
        </if>
        <if test="param.agentCompanyId !=null and param.agentCompanyId !=''">
            and d.AGENT_COMPANY_ID=#{param.agentCompanyId}
        </if>
        <if test="param.orDlrCode !=null and param.orDlrCode.size()>0 ">
            and t.TASK_PERSON_DLR_CODE in
            <foreach collection="param.orDlrCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.stationId !=null and param.stationId !=''">
            and s.STATION_ID=#{param.stationId}
        </if>
        <if test="param.createdDateBegin !=null and param.createdDateBegin !=''">
            and t.CREATED_DATE Between #{param.createdDateBegin} AND #{param.createdDateEnd}
        </if>
        ORDER BY CREATED_DATE DESC
    </select>

    <!-- 试驾任务 信息查询 -->
    <select id="sacTestDriveTaskFindCount" resultType="int">
        select
        COUNT(1)
        from t_sac_test_drive_task
        where 1=1
        <include refid="where_condition">
        </include>
    </select>

    <!-- 试驾任务 信息删除（物理删除） -->
    <delete id="deleteSacTestDriveTask">
        DELETE
        FROM
        t_sac_test_drive_task
        WHERE 1=1
        <!-- 描述条件 -->
        <include refid="where_condition">
        </include>
    </delete>

    <select id="querySacTestDriveCt" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT SUM(COUNT) FROM (select COUNT(1)COUNT from t_sac_test_drive_task where phone=#{phone}
        <if test='label=="1"'>
            and id !=#{id}
        </if>
        AND TASK_STATE_CODE='0' AND IS_ENABLE='1'
        UNION
        SELECT COUNT(1)COUNT FROM `t_sac_test_drive_sheet` where CUSTOMER_PHONE=#{phone} and TEST_STATUS &lt;&gt; '2'
        AND IS_ENABLE='1')T
    </select>

    <!-- 试驾任务 信息新增 -->
    <insert id="createSacTestDriveTask">
        insert into t_sac_test_drive_task(<include refid="Base_Column_List">
    </include>)
        value(
        #{param.id},
        #{param.taskTitle},
        #{param.custName},
        #{param.phone},
        #{param.genderCode},
        #{param.genderName},
        #{param.testType},
        #{param.taskPersonId},
        #{param.taskPersonCode},
        #{param.taskPersonName},
        #{param.taskPersonDlrCode},
        #{param.taskPersonDlrName},
        #{param.taskStateCode},
        #{param.taskStateName},
        #{param.remark},
        #{param.bussTime},
        #{param.smallCarTypeName},
        #{param.appointmentTestDate},
        #{param.appointmentTestTime},
        #{param.appointmentStartTime},
        #{param.appointmentEndTime},
        #{param.sendDlrName},
        #{param.salesConsultantName},
        #{param.msgTestType},
        #{param.oldTestDriveSheetId},
        #{param.intenLevelCode},
        #{param.intenLevelName},
        #{param.intenCarTypeName},
        #{param.planBuyDateName},
        #{param.channelName},
        #{param.newTestDriveSheetId},
        #{param.extendJson},
        #{param.column1},
        #{param.column2},
        #{param.column3},
        #{param.column4},
        #{param.column5},
        #{param.mycatOpTime},
        '1',
        '1',
        '1',
        '1',
        #{param.creator},
        #{param.createdName},
        NOW(),
        #{param.modifier},
        #{param.modifyName},
        NOW(),
        '1',
        '1',
        '1',
        UUID(),
        ifnull(#{param.cl_source5},''),
        ifnull(#{param.cl_source6},''),
        ifnull(#{param.x-channel-id},'')
        )
    </insert>

    <!-- 试驾任务 信息更新 -->
    <update id="updateSacTestDriveTask">
        update t_sac_test_drive_task set
        <!-- 更新列表 -->
        <if test="param.taskTitle !=null and param.taskTitle !=''">
            TASK_TITLE=#{param.taskTitle},
        </if>
        <if test="param.custName !=null and param.custName !=''">
            CUST_NAME=#{param.custName},
        </if>
        <if test="param.phone !=null and param.phone !=''">
            PHONE=#{param.phone},
        </if>
        <if test="param.genderCode !=null and param.genderCode !=''">
            GENDER_CODE=#{param.genderCode},
        </if>
        <if test="param.genderName !=null and param.genderName !=''">
            GENDER_NAME=#{param.genderName},
        </if>
        <if test="param.testType !=null and param.testType !=''">
            TEST_TYPE=#{param.testType},
        </if>
        <if test="param.taskPersonId !=null and param.taskPersonId !=''">
            TASK_PERSON_ID=#{param.taskPersonId},
        </if>
        <if test="param.taskPersonCode !=null and param.taskPersonCode !=''">
            TASK_PERSON_CODE=#{param.taskPersonCode},
        </if>
        <if test="param.taskPersonName !=null and param.taskPersonName !=''">
            TASK_PERSON_NAME=#{param.taskPersonName},
        </if>
        <if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">
            TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode},
        </if>
        <if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">
            TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName},
        </if>
        <if test="param.taskStateCode !=null and param.taskStateCode !=''">
            TASK_STATE_CODE=#{param.taskStateCode},
        </if>
        <if test="param.taskStateName !=null and param.taskStateName !=''">
            TASK_STATE_NAME=#{param.taskStateName},
        </if>
        <if test="param.remark !=null and param.remark !=''">
            REMARK=#{param.remark},
        </if>
        <if test="param.bussTime !=null and param.bussTime !=''">
            BUSS_TIME=#{param.bussTime},
        </if>
        <if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">
            SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName},
        </if>
        <if test="param.appointmentTestDate !=null and param.appointmentTestDate !=''">
            APPOINTMENT_TEST_DATE=#{param.appointmentTestDate},
        </if>
        <if test="param.appointmentTestTime !=null and param.appointmentTestTime !=''">
            APPOINTMENT_TEST_TIME=#{param.appointmentTestTime},
        </if>
        <if test="param.appointmentStartTime !=null and param.appointmentStartTime !=''">
            APPOINTMENT_START_TIME=#{param.appointmentStartTime},
        </if>
        <if test="param.appointmentEndTime !=null and param.appointmentEndTime !=''">
            APPOINTMENT_END_TIME=#{param.appointmentEndTime},
        </if>
        <if test="param.sendDlrName !=null and param.sendDlrName !=''">
            SEND_DLR_NAME=#{param.sendDlrName},
        </if>
        <if test="param.salesConsultantName !=null and param.salesConsultantName !=''">
            SALES_CONSULTANT_NAME=#{param.salesConsultantName},
        </if>
        <if test="param.msgTestType !=null and param.msgTestType !=''">
            MSG_TEST_TYPE=#{param.msgTestType},
        </if>
        <if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">
            OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId},
        </if>
        <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
            INTEN_LEVEL_CODE=#{param.intenLevelCode},
        </if>
        <if test="param.intenLevelName !=null and param.intenLevelName !=''">
            INTEN_LEVEL_NAME=#{param.intenLevelName},
        </if>
        <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
            INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName},
        </if>
        <if test="param.planBuyDateName !=null and param.planBuyDateName !=''">
            PLAN_BUY_DATE_NAME=#{param.planBuyDateName},
        </if>
        <if test="param.channelName !=null and param.channelName !=''">
            CHANNEL_NAME=#{param.channelName},
        </if>
        <if test="param.newTestDriveSheetId !=null and param.newTestDriveSheetId !=''">
            NEW_TEST_DRIVE_SHEET_ID=#{param.newTestDriveSheetId},
        </if>
        <if test="param.extendJson !=null and param.extendJson !=''">
            EXTEND_JSON=#{param.extendJson},
        </if>
        <if test="param.column1 !=null and param.column1 !=''">
            COLUMN1=#{param.column1},
        </if>
        <if test="param.column2 !=null and param.column2 !=''">
            COLUMN2=#{param.column2},
        </if>
        <if test="param.column3 !=null and param.column3 !=''">
            COLUMN3=#{param.column3},
        </if>
        <if test="param.column4 !=null and param.column4 !=''">
            COLUMN4=#{param.column4},
        </if>
        <if test="param.column5 !=null and param.column5 !=''">
            COLUMN5=#{param.column5},
        </if>
        <if test="param.modifier !=null and param.modifier !=''">
            MODIFIER=#{param.modifier},
        </if>
        <if test="param.modifyName !=null and param.modifyName !=''">
            MODIFY_NAME=#{param.modifyName},
        </if>
        LAST_UPDATED_DATE=NOW(),
        <if test="param.isEnable !=null and param.isEnable !=''">
            IS_ENABLE=#{param.isEnable},
        </if>
        UPDATE_CONTROL_ID=UUID()
        <!-- 结束无逗号 -->
        where 1=1
        <!-- 描述条件 -->
        <if test="param.id !=null and param.id !=''">
            and ID=#{param.id}
        </if>
    </update>

    <!-- 试驾任务 信息查询 -->
    <select id="sacTestDriveTaskFindListInfo" resultType="map">
        select
        t.ID,
        t.TASK_TITLE,
        t.CUST_NAME,
        INSERT(t.PHONE,4,4,'****') as PHONE,
        t.GENDER_CODE,
        t.GENDER_NAME,
        t.TEST_TYPE,
        t.TASK_PERSON_ID,
        t.TASK_PERSON_CODE,
        t.TASK_PERSON_NAME,
        t.TASK_PERSON_DLR_CODE,
        t.TASK_PERSON_DLR_NAME,
        t.TASK_STATE_CODE,
        (case when s.STATION_ID in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='0' then '未分配'
        when s.STATION_ID in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='1' then '已分配'
        when s.STATION_ID not in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='0' then '未完成'
        when s.STATION_ID not in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='1' then '已完成'
        when t.TASK_STATE_CODE='2' then '已取消'
        else '' end ) as TASK_STATE_NAME,
        t.REMARK,
        t.BUSS_TIME,
        t.SMALL_CAR_TYPE_NAME,
        t.APPOINTMENT_TEST_DATE,
        t.APPOINTMENT_TEST_TIME,
        CONCAT(t.APPOINTMENT_TEST_DATE,' ',t.APPOINTMENT_TEST_TIME) as APPOINTMENT_TIME,
        t.APPOINTMENT_START_TIME,
        t.APPOINTMENT_END_TIME,
        t.SEND_DLR_NAME,
        t.SALES_CONSULTANT_NAME,
        t.MSG_TEST_TYPE,
        t.OLD_TEST_DRIVE_SHEET_ID,
        t.INTEN_LEVEL_CODE,
        t.INTEN_LEVEL_NAME,
        t.INTEN_CAR_TYPE_NAME,
        t.PLAN_BUY_DATE_NAME,
        t.CHANNEL_NAME,
        t.NEW_TEST_DRIVE_SHEET_ID,
        t.CL_SOURCE5,
        t.CL_SOURCE6,
        t.EXTEND_JSON,
        t.COLUMN1,
        t.COLUMN2,
        t.COLUMN3,
        t.COLUMN4,
        t.COLUMN5,
        t.GROUP_CODE,
        t.CREATOR,
        t.CREATED_NAME,
        t.CREATED_DATE,
        t.MODIFIER,
        t.MODIFY_NAME,
        t.LAST_UPDATED_DATE,
        t.IS_ENABLE,
        t.UPDATE_CONTROL_ID,
        '0' as isDriveOrTask,CONCAT(s.STATION_NAME,'(',s.ORG_ID,')') as STATION_NAME
                    from csc.t_sac_test_drive_task t
        LEFT JOIN mp.t_usc_mdm_org_dlr t2 on  t.TASK_PERSON_DLR_CODE=t2.DLR_CODE
        LEFT JOIN mp.t_usc_mdm_agent_company t3 ON t2.COMPANY_ID = t3.AGENT_COMPANY_ID
        LEFT JOIN mp.t_usc_mdm_agent_info t4 ON t4.AGENT_ID = t3.AGENT_ID
        left join mp.t_usc_mdm_org_employee e on e.USER_ID=t.TASK_PERSON_ID
        left join mp.t_usc_mdm_org_station s on s.STATION_ID=e.STATION_ID
        where 1=1
        <!-- 时间查询 -->
        <if test="param.createdDateStart !=null and param.createdDateStart !=''">
            and t.CREATED_DATE>=#{param.createdDateStart}
        </if>
        <if test="param.createdDateEnd !=null and param.createdDateEnd !=''">
            <![CDATA[and t.CREATED_DATE<=#{param.createdDateEnd}]]>
        </if>
        <if test="param.bussTimeStart !=null and param.bussTimeStart !=''">
            and t.BUSS_TIME>=#{param.bussTimeStart}
        </if>
        <if test="param.bussTimeEnd !=null and param.bussTimeEnd !=''">
            <![CDATA[and t.BUSS_TIME<=#{param.bussTimeEnd}]]>
        </if>

        <if test="param.id !=null and param.id !=''">
            and t.ID=#{param.id}
        </if>
        <if test="param.taskTitle !=null and param.taskTitle !=''">
            and t.TASK_TITLE like CONCAT('%',#{param.taskTitle},'%')
        </if>

        <if test='param.beOverdue=="1"'>
            and TIMESTAMPDIFF(HOUR,t.CREATED_DATE,ifnull(t.BUSS_TIME,now()))>24
            and t.TASK_STATE_CODE in ('0','1')
        </if>
        <if test='param.beOverdue=="2"'>
            and TIMESTAMPDIFF(HOUR,t.CREATED_DATE,ifnull(t.BUSS_TIME,now()))&lt;= 24
        </if>
        <if test="param.custName !=null and param.custName !=''">
            and t.CUST_NAME=#{param.custName}
        </if>
        <if test="param.phone !=null and param.phone !=''">
            and t.PHONE=#{param.phone}
        </if>
        <if test="param.genderCode !=null and param.genderCode !=''">
            and t.GENDER_CODE=#{param.genderCode}
        </if>
        <if test="param.genderName !=null and param.genderName !=''">
            and t.GENDER_NAME=#{param.genderName}
        </if>
        <if test="param.testType !=null and param.testType !=''">
            and t.TEST_TYPE=#{param.testType}
        </if>
        <if test="param.taskPersonId !=null and param.taskPersonId !=''">
            and t.TASK_PERSON_ID=#{param.taskPersonId}
        </if>
        <if test="param.taskPersonCode !=null and param.taskPersonCode !=''">
            and t.TASK_PERSON_CODE=#{param.taskPersonCode}
        </if>
        <if test="param.taskPersonName !=null and param.taskPersonName !=''">
            and t.TASK_PERSON_NAME=#{param.taskPersonName}
        </if>
        <if test="param.taskPersonDlrCode !=null and param.taskPersonDlrCode !=''">
            and t.TASK_PERSON_DLR_CODE=#{param.taskPersonDlrCode}
        </if>
        <if test="param.taskPersonDlrName !=null and param.taskPersonDlrName !=''">
            and t.TASK_PERSON_DLR_NAME=#{param.taskPersonDlrName}
        </if>
        <!--
                <if test="param.taskStateCode !=null and param.taskStateCode !=''">and t.TASK_STATE_CODE=#{param.taskStateCode}</if>
        -->
        <if test="param.taskStateName !=null and param.taskStateName !=''">
            and t.TASK_STATE_NAME=#{param.taskStateName}
        </if>
        <if test="param.remark !=null and param.remark !=''">
            and t.REMARK=#{param.remark}
        </if>
        <if test="param.bussTime !=null and param.bussTime !=''">
            and t.BUSS_TIME=#{param.bussTime}
        </if>
        <if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">
            and t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}
        </if>
        <if test="param.appointmentTestDate !=null and param.appointmentTestDate !=''">
            and t.APPOINTMENT_TEST_DATE=#{param.appointmentTestDate}
        </if>
        <if test="param.appointmentTestTime !=null and param.appointmentTestTime !=''">
            and t.APPOINTMENT_TEST_TIME=#{param.appointmentTestTime}
        </if>
        <if test="param.appointmentStartTime !=null and param.appointmentStartTime !=''">
            and t.APPOINTMENT_START_TIME=#{param.appointmentStartTime}
        </if>
        <if test="param.appointmentEndTime !=null and param.appointmentEndTime !=''">
            and t.APPOINTMENT_END_TIME=#{param.appointmentEndTime}
        </if>
        <if test="param.sendDlrName !=null and param.sendDlrName !=''">
            and t.SEND_DLR_NAME=#{param.sendDlrName}
        </if>
        <if test="param.salesConsultantName !=null and param.salesConsultantName !=''">
            and t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}
        </if>
        <if test="param.msgTestType !=null and param.msgTestType !=''">
            and t.MSG_TEST_TYPE=#{param.msgTestType}
        </if>
        <if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">
            and t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}
        </if>
        <if test="param.intenLevelCode !=null and param.intenLevelCode !=''">
            and t.INTEN_LEVEL_CODE=#{param.intenLevelCode}
        </if>
        <if test="param.intenLevelName !=null and param.intenLevelName !=''">
            and t.INTEN_LEVEL_NAME=#{param.intenLevelName}
        </if>
        <if test="param.intenCarTypeName !=null and param.intenCarTypeName !=''">
            and t.INTEN_CAR_TYPE_NAME=#{param.intenCarTypeName}
        </if>
        <if test="param.planBuyDateName !=null and param.planBuyDateName !=''">
            and t.PLAN_BUY_DATE_NAME=#{param.planBuyDateName}
        </if>
        <if test="param.channelName !=null and param.channelName !=''">
            and t.CHANNEL_NAME=#{param.channelName}
        </if>
        <if test="param.newTestDriveSheetId !=null and param.newTestDriveSheetId !=''">
            and t.NEW_TEST_DRIVE_SHEET_ID=#{param.newTestDriveSheetId}
        </if>
        <if test="param.extendJson !=null and param.extendJson !=''">
            and t.EXTEND_JSON=#{param.extendJson}
        </if>
        <if test="param.column1 !=null and param.column1 !=''">
            and t.COLUMN1=#{param.column1}
        </if>
        <if test="param.column2 !=null and param.column2 !=''">
            and t.COLUMN2=#{param.column2}
        </if>
        <if test="param.column3 !=null and param.column3 !=''">
            and t.COLUMN3=#{param.column3}
        </if>
        <if test="param.column4 !=null and param.column4 !=''">
            and t.COLUMN4=#{param.column4}
        </if>
        <if test="param.column5 !=null and param.column5 !=''">
            and t.COLUMN5=#{param.column5}
        </if>
        <if test="param.mycatOpTime !=null and param.mycatOpTime !=''">
            and t._MYCAT_OP_TIME=#{param.mycatOpTime}
        </if>
        <if test="param.oemId !=null and param.oemId !=''">
            and t.OEM_ID=#{param.oemId}
        </if>
        <if test="param.groupId !=null and param.groupId !=''">
            and t.GROUP_ID=#{param.groupId}
        </if>
        <if test="param.oemCode !=null and param.oemCode !=''">
            and t.OEM_CODE=#{param.oemCode}
        </if>
        <if test="param.groupCode !=null and param.groupCode !=''">
            and t.GROUP_CODE=#{param.groupCode}
        </if>
        <if test="param.creator !=null and param.creator !=''">
            and t.CREATOR=#{param.creator}
        </if>
        <if test="param.createdName !=null and param.createdName !=''">
            and t.CREATED_NAME=#{param.createdName}
        </if>
        <if test="param.createdDate !=null and param.createdDate !=''">
            and t.CREATED_DATE=#{param.createdDate}
        </if>
        <if test="param.modifier !=null and param.modifier !=''">
            and t.MODIFIER=#{param.modifier}
        </if>
        <if test="param.modifyName !=null and param.modifyName !=''">
            and t.MODIFY_NAME=#{param.modifyName}
        </if>
        <if test="param.lastUpdatedDate !=null and param.lastUpdatedDate !=''">
            and t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}
        </if>
        <if test="param.isEnable !=null and param.isEnable !=''">
            and t.IS_ENABLE=#{param.isEnable}
        </if>
        <if test="param.sdpUserId !=null and param.sdpUserId !=''">
            and t.SDP_USER_ID=#{param.sdpUserId}
        </if>
        <if test="param.sdpOrgId !=null and param.sdpOrgId !=''">
            and t.SDP_ORG_ID=#{param.sdpOrgId}
        </if>
        <if test="param.updateControlId !=null and param.updateControlId !=''">
            and t.UPDATE_CONTROL_ID=#{param.updateControlId}
        </if>
        <if test="param.agentId !=null and param.agentId !=''">
            and t4.AGENT_ID=#{param.agentId}
        </if>
        <if test="param.agentCompanyId !=null and param.agentCompanyId !=''">
            and t3.AGENT_COMPANY_ID=#{param.agentCompanyId}
        </if>
        <if test="param.orDlrCode !=null and param.orDlrCode.size()>0 ">
            and t.TASK_PERSON_DLR_CODE in
            <foreach collection="param.orDlrCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="param.dlrCodeLists !=null  ">
            and t.TASK_PERSON_DLR_CODE in
            <foreach collection="param.dlrCodeLists" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.stationId !=null and param.stationId !=''">
            and s.STATION_ID=#{param.stationId}
        </if>
        <if test='param.taskStateCode !=null and param.taskStateCode =="0" '>
            and s.STATION_ID not in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='0'
        </if>
        <if test='param.taskStateCode !=null and param.taskStateCode =="1" '>
            and s.STATION_ID not in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='1'
        </if>
        <if test='param.taskStateCode !=null and param.taskStateCode =="2" '>
            and s.STATION_ID in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='0'
        </if>
        <if test='param.taskStateCode !=null and param.taskStateCode =="3" '>
            and s.STATION_ID in ('smart_bm_0005','smart_bm_0016') and t.TASK_STATE_CODE='1'
        </if>
        <if test='param.taskStateCode !=null and param.taskStateCode =="4" '>
            and t.TASK_STATE_CODE='2'
        </if>
        ORDER BY CREATED_DATE DESC
    </select>

    <select id="locationEmpInfo" resultType="map">
        SELECT
        t.EMP_ID,
        t.EMP_CODE,
        t.EMP_NAME,
        t.USER_ID,
        t.USER_NAME,
        t.DLR_CODE,
        c.DLR_SHORT_NAME,
        c.ONLINE_FLAG,
        t.MOBILE,
        t.HEAD_PORTRAIT,
        t.STATION_ID,
        a.STATION_CODE,
        a.STATION_NAME,
        t.COLUMN2 STATION_ID2,
        b.STATION_CODE STATION_CODE2,
        b.STATION_NAME STATION_NAME2,
        c.POSITION_ID,
        c.POSITION_CODE
        FROM
        mp.t_usc_mdm_org_employee t
        LEFT JOIN mp.t_usc_mdm_org_station a ON a.STATION_ID=t.STATION_ID
        LEFT JOIN mp.t_usc_mdm_org_station b ON b.STATION_ID=t.COLUMN2
        LEFT JOIN mp.t_usc_mdm_org_dlr c ON c.DLR_CODE = t.DLR_CODE
        LEFT JOIN
        (
        SELECT
        t1.EMP_ID,
        CONVERT ( GROUP_CONCAT( t2.POSITION_ID ) USING utf8 ) AS POSITION_ID,
        CONVERT ( GROUP_CONCAT( t2.POSITION_CODE ) USING utf8 ) AS POSITION_CODE
        FROM
        mp.t_usc_mdm_org_employee_station t1
        LEFT JOIN mp.t_usc_mds_sys_position_detail t2 ON t1.STATION_ID = t2.STATION_ID
        LEFT JOIN mp.t_usc_mdm_org_station t3 ON t1.station_id = t3.station_id
        GROUP BY t1.EMP_ID
        ) c ON c.EMP_ID = t.EMP_ID
        WHERE
        1 = 1 AND t.USER_STATUS = '1'
        <if test="param.stationId != null and '' != param.stationId">
            AND t.STATION_ID = #{param.stationId}
        </if>
        <if test="param.stationIdIn !=null and param.stationIdIn !=''">
            and (
            t.STATION_ID IN
            <foreach collection="param.stationIdIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            or
            t.COLUMN2 IN
            <foreach collection="param.stationIdIn.split(',')" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.dlrCode != null and '' != param.dlrCode">
            AND t.DLR_CODE = #{param.dlrCode}
        </if>
        <if test="param.empId != null and '' != param.empId">
            AND t.EMP_ID = #{param.empId}
        </if>
        <if test="param.userId != null and '' != param.userId">
            /*userId调用*/
            AND t.USER_ID = #{param.userId}
        </if>
        <if test="param.userName != null and '' != param.userName">
            AND t.USER_NAME = #{param.userName}
        </if>
    </select>

    <select id="findStation" resultType="java.lang.String">
        select STATION_ID
        from mp.t_usc_mdm_org_employee
        where EMP_ID = #{empId}
    </select>

    <select id="findSmallCarType" resultType="java.lang.String">
        SELECT T.SMALL_CAR_TYPE_NAME FROM (select SMALL_CAR_TYPE_NAME from t_sac_test_drive_task where phone=#{phone}
        <if test='label=="1"'>
            and id !=#{id}
        </if>
        AND TASK_STATE_CODE='0' AND IS_ENABLE='1'
        UNION
        SELECT SMALL_CAR_TYPE_NAME FROM `t_sac_test_drive_sheet` where CUSTOMER_PHONE=#{phone} and TEST_STATUS &lt;&gt;
        '2' AND IS_ENABLE='1')T
    </select>

    <select id="findLookUpValue" resultType="com.ly.adp.drive.entities.LookupValue">
        select LOOKUP_VALUE_CODE,
               LOOKUP_VALUE_NAME,
               IS_ENABLE
        from mp.t_prc_mds_lookup_value
        where LOOKUP_TYPE_CODE = #{code}
    </select>

    <select id="findSmsUrl" resultType="java.lang.String">
        select LOOKUP_VALUE_CODE
        from mp.t_prc_mds_lookup_value
        where LOOKUP_TYPE_CODE = 'VE1037'
          and ORDER_NO = 0
    </select>
    <select id="findSmartUrl" resultType="java.lang.String">
        select LOOKUP_VALUE_CODE
        from mp.t_prc_mds_lookup_value
        where LOOKUP_TYPE_CODE = 'VE1037'
          and ORDER_NO = 1
    </select>

    <insert id="insertCspLeadsEvent">
        insert into interfacecenter.t_ifs_base_cdp_leads_event(logs_id,
                                                               bk,
                                                               EVENT,
                                                               date,
                                                               c_staff_name,
                                                               c_process_date,
                                                               c_store_name,
                                                               c_orderNo,
                                                               insert_date,
                                                               send_flag,
                                                               err_log,
                                                               send_date,
                                                               c_reason,
                                                               c_time_period,
                                                               c_book_date,
                                                               c_car_model,
                                                               c_driver_type,
                                                               c_activity_name,
                                                               c_activity_variety,
                                                               c_store,
                                                               c_store_code)
        values (uuid(),
                #{param.phone},
                'c_bookdrive',
                #{dateTz},
                NULL,
                NULL,
                NULL,
                NULL,
                now(),
                '0',
                '',
                now(),
                NULL,
                #{param.appointmentTestTime},
                #{param.appointmentTestDate},
                #{param.smallCarTypeName},
                #{param.testType},
                NULL,
                NULL,
               #{param.taskPersonDlrName},
               #{param.taskPersonDlrCode}
               )
    </insert>

    <select id="searchClueByPhoneAndDlr" resultType="java.util.Map">
        SELECT
        emp.user_id,
        emp.user_name,
        emp.emp_name
        FROM
        csc.t_sac_clue_info_dlr dlr
        INNER JOIN mp.t_usc_mdm_org_employee emp ON dlr.REVIEW_PERSON_ID = emp.user_id
        WHERE
        dlr.phone = #{param.phone}
        AND dlr.dlr_code = #{param.taskPersonDlrCode}
        limit 1;
    </select>

    <insert id="insertCspLeads">
        insert into interfacecenter.t_ifs_base_cdp_leads(
            logs_id,
            bk,
            mobile,
            c_store_code,
            c_store,
            insert_date,
            send_flag,
            send_date
            )
        values (uuid(),
                #{param.phone},
                #{param.phone},
                #{param.taskPersonDlrCode},
                #{param.taskPersonDlrName},
                now(),
                '0',
                now()
                );
    </insert>

    <select id="findAgentCompany" resultType="java.lang.String">
        SELECT
            DLR_CODE
        FROM
            mp.t_usc_mdm_org_dlr
        WHERE
            COMPANY_ID=#{orgId}
    </select>

    <select id="findCompany" resultType="java.lang.String">
        select
            d.DLR_CODE
            from
            mp.t_usc_mdm_org_dlr d
            LEFT JOIN mp.t_usc_mdm_agent_company C1 ON d.COMPANY_ID = C1.AGENT_COMPANY_ID
            LEFT JOIN mp.t_usc_mdm_agent_info c2 ON C1.AGENT_ID = c2.AGENT_ID
        where
            C2.AGENT_CODE = #{orgCode}
    </select>
</mapper>
