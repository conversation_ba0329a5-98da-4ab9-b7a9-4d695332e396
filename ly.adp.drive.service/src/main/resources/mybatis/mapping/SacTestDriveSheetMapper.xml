<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.ly.adp.drive.idal.mapper.SacTestDriveSheetMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="com.ly.adp.drive.entities.SacTestDriveSheet">
		<id column="TEST_DRIVE_SHEET_ID" property="testDriveSheetId" />
        <result column="TEST_DRIVE_ORDER_NO" property="testDriveOrderNo" />
        <result column="APPOINTMENT_ID" property="appointmentId" />
        <result column="TEST_STATUS" property="testStatus" />
        <result column="DLR_CODE" property="dlrCode" />
        <result column="DLR_NAME" property="dlrName" />
        <result column="SALES_CONSULTANT_NAME" property="salesConsultantName" />
        <result column="SALES_CONSULTANT_ID" property="salesConsultantId" />
        <result column="INTEN_LEVEL_CODE" property="intenLevelCode" />
        <result column="INTEN_LEVEL_NAME" property="intenLevelName" />
        <result column="DRIVER_CUSTOMER_RELATION" property="driverCustomerRelation" />
        <result column="DRIVER_NAME" property="driverName" />
        <result column="DRIVER_PHONE" property="driverPhone" />
        <result column="DRIVING_LICENCE_TYPE" property="drivingLicenceType" />
        <result column="DRIVING_LICENCE_NUMBER" property="drivingLicenceNumber" />
        <result column="ADDRESS" property="address" />
        <result column="DRIVING_LICENCE_PHOTO" property="drivingLicencePhoto" />
        <result column="TEST_ROAD_HAUL" property="testRoadHaul" />
        <result column="TEST_START_ROAD_HAUL" property="testStartRoadHaul" />
        <result column="TEST_END_ROAD_HAUL" property="testEndRoadHaul" />
        <result column="DLR_CLUE_ORDER_NO" property="dlrClueOrderNo" />
        <result column="CUSTOMER_NAME" property="customerName" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="CUSTOMER_PHONE" property="customerPhone" />
        <result column="CUSTOMER_SEX" property="customerSex" />
        <result column="SMALL_CAR_TYPE_CODE" property="smallCarTypeCode" />
        <result column="SMALL_CAR_TYPE_NAME" property="smallCarTypeName" />
        <result column="PLATE_NUMBER" property="plateNumber" />
        <result column="CAR_VIN" property="carVin" />
        <result column="TEST_TYPE" property="testType" />
        <result column="APPOINTMENT_CHANNEL" property="appointmentChannel" />
        <result column="START_TIME" property="startTime" />
        <result column="END_TIME" property="endTime" />
        <result column="TEST_DRIVE_AGREEMENT" property="testDriveAgreement" />
        <result column="CUSTOMER_ID_NUMBER_AGREEMENT" property="customerIdNumberAgreement" />
        <result column="OTHER_AGREEMENT" property="otherAgreement" />
        <result column="CUSTOMER_SIGNATURE_AGREEMENT" property="customerSignatureAgreement" />
        <result column="OEM_ID" property="oemId" />
        <result column="GROUP_ID" property="groupId" />
        <result column="CREATOR" property="creator" />
        <result column="CREATED_NAME" property="createdName" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="MODIFIER" property="modifier" />
        <result column="MODIFY_NAME" property="modifyName" />
        <result column="LAST_UPDATED_DATE" property="lastUpdatedDate" />
        <result column="UPDATE_CONTROL_ID" property="updateControlId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_CAN_CHANGE" property="isCanChange" />
        <result column="DEPOSIT" property="deposit" />
        <result column="OLD_TEST_DRIVE_SHEET_ID" property="oldTestDriveSheetId" />
        <result column="COLUMN1" property="column1" />
        <result column="COLUMN2" property="column2" />
        <result column="COLUMN3" property="column3" />
        <result column="COLUMN4" property="column4" />
        <result column="COLUMN5" property="column5" />
        <result column="COLUMN6" property="column6" />
        <result column="COLUMN7" property="column7" />
        <result column="COLUMN8" property="column8" />
        <result column="COLUMN9" property="column9" />
        <result column="COLUMN10" property="column10" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
	TEST_DRIVE_SHEET_ID, TEST_DRIVE_ORDER_NO, APPOINTMENT_ID, TEST_STATUS,
	DLR_CODE, DLR_NAME, SALES_CONSULTANT_NAME, SALES_CONSULTANT_ID,
	INTEN_LEVEL_CODE,INTEN_LEVEL_NAME, DRIVER_CUSTOMER_RELATION, DRIVER_NAME, DRIVER_PHONE,
	DRIVING_LICENCE_TYPE, DRIVING_LICENCE_NUMBER, ADDRESS,
	DRIVING_LICENCE_PHOTO, TEST_ROAD_HAUL, TEST_START_ROAD_HAUL,
	TEST_END_ROAD_HAUL, DLR_CLUE_ORDER_NO, CUSTOMER_NAME, CUSTOMER_ID,
	CUSTOMER_PHONE, CUSTOMER_SEX, SMALL_CAR_TYPE_CODE, SMALL_CAR_TYPE_NAME,
	PLATE_NUMBER, CAR_VIN, TEST_TYPE, APPOINTMENT_CHANNEL, START_TIME,
	END_TIME, TEST_DRIVE_AGREEMENT, CUSTOMER_ID_NUMBER_AGREEMENT,
	CUSTOMER_SIGNATURE_AGREEMENT, OEM_ID, GROUP_ID, CREATOR, CREATED_NAME,
	CREATED_DATE, MODIFIER, MODIFY_NAME, LAST_UPDATED_DATE,DEPOSIT,OLD_TEST_DRIVE_SHEET_ID,OTHER_AGREEMENT,
	UPDATE_CONTROL_ID, IS_ENABLE,IS_CAN_CHANGE, COLUMN1, COLUMN2, COLUMN3, COLUMN4,
	COLUMN5, COLUMN6, COLUMN7, COLUMN8, COLUMN9, COLUMN10
	</sql>

	<select id="selectSacTestDriveSheetDetail" resultType="java.util.Map">
	select t.TEST_DRIVE_SHEET_ID,
	t.TEST_DRIVE_ORDER_NO,
	t.APPOINTMENT_ID,
	t.TEST_STATUS,
	(case when
	t.TEST_STATUS='0' then '未开始' when t.TEST_STATUS='1' then '试乘试驾中' when t.TEST_STATUS='2' then '已结束' end) as testStatusName,
	t.DLR_CODE,
	t.DLR_NAME,
	t.SALES_CONSULTANT_NAME,
	t.SALES_CONSULTANT_ID,
	t.INTEN_LEVEL_CODE,
	t.INTEN_LEVEL_NAME,
	t.DRIVER_CUSTOMER_RELATION,
	t.DRIVER_NAME,
	t.DRIVER_PHONE,
	t.DRIVING_LICENCE_TYPE,
	t.DRIVING_LICENCE_NUMBER,
	t.ADDRESS,
	t.DRIVING_LICENCE_PHOTO,
	t.TEST_ROAD_HAUL,
	t.TEST_START_ROAD_HAUL,
	t.TEST_END_ROAD_HAUL,
	t.DLR_CLUE_ORDER_NO,
	t.CUSTOMER_NAME,
	t.CUSTOMER_ID,
	t.CUSTOMER_PHONE,
	t.CUSTOMER_SEX,
	t.SMALL_CAR_TYPE_CODE,
	t.SMALL_CAR_TYPE_NAME,
	t.PLATE_NUMBER,
	t.DEPOSIT,
	t.OLD_TEST_DRIVE_SHEET_ID,
	t.IS_CAN_CHANGE,
	t.OTHER_AGREEMENT,
	t1.APPOINTMENT_TEST_DATE,
	t1.APPOINTMENT_TEST_TIME,
	t1.APPOINTMENT_START_TIME,
	t1.APPOINTMENT_END_TIME,
	t1.APPOINTMENT_ORDER_NO,
	t1.IS_TEST_DRIVE,
	t1.NEW_DLR_APPOINTMENT_START_TIME,
    t1.NEW_DLR_APPOINTMENT_END_TIME,
	t.CAR_VIN,
	t.TEST_TYPE,
	(case when
	t.TEST_TYPE='0' then '试乘' when t.TEST_TYPE='1' then '试驾' when t.TEST_TYPE='2' then '深度试驾' end) as testTypeName,
	t.APPOINTMENT_CHANNEL,
	t.START_TIME,
	t.END_TIME,
	t.TEST_DRIVE_AGREEMENT,
	t.CUSTOMER_ID_NUMBER_AGREEMENT,
	t.CUSTOMER_SIGNATURE_AGREEMENT,
	t.OEM_ID,
	t.GROUP_ID,
	t.CREATOR,
	t.CREATED_NAME,
	t.CREATED_DATE,
	t.MODIFIER,
	t.MODIFY_NAME,
	t.LAST_UPDATED_DATE,
	t.UPDATE_CONTROL_ID,
	t.IS_ENABLE
	from t_sac_test_drive_sheet t
	left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO and t1.IS_ENABLE='1')
	where t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}
	</select>


	<!-- 试乘试驾单查询 -->
	<select id="selectSacTestDriveSheet" resultType="java.util.Map">
		select t.TEST_DRIVE_SHEET_ID,
		t.TEST_DRIVE_ORDER_NO,
		t.APPOINTMENT_ID,
		t.TEST_STATUS,
		(case
		when t.TEST_STATUS='0' then '未开始'
		when t.TEST_STATUS='1' then '试乘试驾中'
		when t.TEST_STATUS='2' then '已结束' end) as testStatusName,
		t.DLR_CODE,
		t.DLR_NAME,
		t.SALES_CONSULTANT_NAME,
		t.SALES_CONSULTANT_ID,
		t.INTEN_LEVEL_CODE,
		t.INTEN_LEVEL_NAME,
		t.DRIVER_CUSTOMER_RELATION,
		t.DRIVER_NAME,
		t.DRIVER_PHONE,
		t.DRIVING_LICENCE_TYPE,
		t.DRIVING_LICENCE_NUMBER,
		t.ADDRESS,
		t.DRIVING_LICENCE_PHOTO,
		t.TEST_ROAD_HAUL,
		t.OTHER_AGREEMENT,
		t.TEST_START_ROAD_HAUL,
		t.TEST_END_ROAD_HAUL,
		t.DLR_CLUE_ORDER_NO,
		t.CUSTOMER_NAME,
		t.CUSTOMER_ID,
		t.CUSTOMER_PHONE,
		t2.INFO_CHAN_M_CODE,
		t2.INFO_CHAN_M_NAME,
		t2.INFO_CHAN_D_CODE,
		t2.INFO_CHAN_D_NAME,
		INSERT(t.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM,
		t.CUSTOMER_SEX,
		t.SMALL_CAR_TYPE_CODE,
		t.SMALL_CAR_TYPE_NAME,
		t.PLATE_NUMBER,
		t.CAR_VIN,
		t.TEST_TYPE,
		t.IS_CAN_CHANGE,
		(case when
		t.TEST_TYPE='0' then '试乘' when t.TEST_TYPE='1' then '试驾' when t.TEST_TYPE='2' then '深度试驾' end) as testTypeName,
		t1.APPOINTMENT_TEST_DATE,
		t1.APPOINTMENT_TEST_TIME,
		t1.APPOINTMENT_START_TIME,
		t1.APPOINTMENT_END_TIME,
		t1.APPOINTMENT_ORDER_NO,
		t1.IS_TEST_DRIVE,
		t1.NEW_DLR_APPOINTMENT_START_TIME,
		t1.NEW_DLR_APPOINTMENT_END_TIME,
		t.APPOINTMENT_CHANNEL,
		t.START_TIME,
		t.END_TIME,
		t.TEST_DRIVE_AGREEMENT,
		t.CUSTOMER_ID_NUMBER_AGREEMENT,
 		t.CUSTOMER_SIGNATURE_AGREEMENT,
		t.DEPOSIT,
		t.OLD_TEST_DRIVE_SHEET_ID,
		t.OEM_ID,
		t.GROUP_ID,
		t.CREATOR,
		t.CREATED_NAME,
		t.CREATED_DATE,
		t.MODIFIER,
		t.MODIFY_NAME,
		t.LAST_UPDATED_DATE,
		t.IS_ENABLE,
		t2.CUST_ID,
		t2.CUST_NAME,
		t2.PHONE,
		t2.INTEN_CAR_TYPE_CODE,
		t2.INTEN_CAR_TYPE_NAME,
		t2.CHANNEL_CODE,
		t2.CHANNEL_NAME,
		t2.COLUMN2 planBuyDate,
		t2.COLUMN1 planBuyDateName,
		t2.COLUMN6 businessHeatCode,
		t2.COLUMN5 businessHeatName,
		t3.REVIEW_ID,
		t3.BILL_CODE,
		t3.UPDATE_CONTROL_ID,
		t.UPDATE_CONTROL_ID updateControlSheetId,
		t2.GENDER_CODE gender,
		t2.GENDER_CODE,
		t2.GENDER_NAME,
		(case when t.DLR_CODE=t2.DLR_CODE then '1' else '0' end) as isThisDlr,
		t.record_id,
		t.TEST_DRIVE_METHOD,
		t.TEST_DRIVE_AGREEMENT_PDF,
		t.CUSTOMER_ID_NUMBER as idNumber,
		t.REAL_NAME
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
		left join t_sac_clue_info_dlr t2  on t.CUSTOMER_PHONE = t2.phone
		left join t_sac_review t3 on t3.REVIEW_ID=t2.REVIEW_ID
		where 1=1
		<if test='param.isEnd !=null and param.isEnd =="0"'>and t.TEST_STATUS in('0','1')</if>
		<if test='param.isEnd !=null and param.isEnd =="1"'>
			and t.TEST_STATUS ='2'
			<if test="param.clueDlrCode !=null and param.clueDlrCode !=''">
				and  t.DLR_CODE=#{param.clueDlrCode}
			</if>
			<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">
				and #{param.reviewPersonId} IN (t2.REVIEW_PERSON_ID, t.SALES_CONSULTANT_ID)
			</if>
		</if>
		<if test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and  t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
		<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">and  t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}</if>
		<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''">and  t.TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}</if>
		<if test="param.deposit !=null and param.deposit !=''">and  t.DEPOSIT=#{param.deposit}</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''">and  t.APPOINTMENT_ID=#{param.appointmentId}</if>
		<if test="param.testStatus !=null and param.testStatus !=''">and  t.TEST_STATUS=#{param.testStatus}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and  t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and  t.DLR_NAME=#{param.dlrName}</if>
		<if test="param.otherAgreement !=null and param.otherAgreement !=''">and  t.OTHER_AGREEMENT=#{param.otherAgreement}</if>
		<if test="param.salesConsultantName !=null ">and  t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}</if>
		<if test="param.salesConsultantId !=null ">and  t.SALES_CONSULTANT_ID=#{param.salesConsultantId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and  t.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and  t.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''">and  t.DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation}</if>
		<if test="param.driverName !=null and param.driverName !=''">and  t.DRIVER_NAME=#{param.driverName}</if>
		<if test="param.driverPhone !=null and param.driverPhone !=''">and  t.DRIVER_PHONE=#{param.driverPhone}</if>
		<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''">and  t.DRIVING_LICENCE_TYPE=#{param.drivingLicenceType}</if>
		<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''">and  t.DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber}</if>
		<if test="param.address !=null and param.address !=''">and  t.ADDRESS=#{param.address}</if>
		<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''">and  t.DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto}</if>
		<if test="param.testRoadHaul !=null and param.testRoadHaul !=''">and  t.TEST_ROAD_HAUL=#{param.testRoadHaul}</if>
		<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''">and  t.TEST_START_ROAD_HAUL=#{param.testStartRoadHaul}</if>
		<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''">and  t.TEST_END_ROAD_HAUL=#{param.testEndRoadHaul}</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''">and  t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
		<if test="param.customerName !=null and param.customerName !=''">and  t.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerId !=null and param.customerId !=''">and  t.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''">and  t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerSex !=null and param.customerSex !=''">and  t.CUSTOMER_SEX=#{param.customerSex}</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">and  t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">and  t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''">and  t.PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.carVin !=null and param.carVin !=''">and  t.CAR_VIN=#{param.carVin}</if>
		<if test="param.testType !=null and param.testType !=''">and  t.TEST_TYPE=#{param.testType}</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''">and  t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
		<if test="param.startTime !=null and param.startTime !=''">and  t.START_TIME=#{param.startTime}</if>
		<if test="param.endTime !=null and param.endTime !=''">and  t.END_TIME=#{param.endTime}</if>
		<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''">and  t.TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement}</if>
		<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''">and  t.CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement}</if>
		<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''">and  t.CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement}</if>
		<if test="param.oemId !=null and param.oemId !=''">and  t.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and  t.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and  t.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and  t.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and  t.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and  t.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and  t.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and  t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.startTimeMin !=null"><![CDATA[and t.START_TIME>=#{param.startTimeMin}]]></if>
		<if test="param.startTimeMax !=null"><![CDATA[and t.START_TIME<date_add(#{param.startTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.endTimeMin !=null"><![CDATA[and t.END_TIME>=#{param.endTimeMin}]]></if>
		<if test="param.endTimeMax !=null"><![CDATA[and t.END_TIME<date_add(#{param.endTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
		<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t.CUSTOMER_PHONE,#{param.searchCondition})>0 or INSTR(t.CUSTOMER_NAME,#{param.searchCondition})>0)</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">and t2.INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t2.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		order by t1.APPOINTMENT_START_TIME
		limit #{param.pageNo},#{param.pageSize}
		</select>

		<select id="selectSacTestDriveSheetCount" resultType="java.lang.Long">
		select count(*)
		from
		t_sac_test_drive_sheet t
		left join t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
		left join t_sac_clue_info_dlr t2  on t.CUSTOMER_PHONE = t2.phone
		left join t_sac_review t3 on t3.REVIEW_ID=t2.REVIEW_ID
		where 1=1
		<if test='param.isEnd !=null and param.isEnd =="0"'>and t.TEST_STATUS in('0','1')</if>
		<if test='param.isEnd !=null and param.isEnd =="1"'>
			and t.TEST_STATUS ='2'
			<if test="param.clueDlrCode !=null and param.clueDlrCode !=''">
				and  t.DLR_CODE=#{param.clueDlrCode}
			</if>
			<if test="param.reviewPersonId !=null and param.reviewPersonId !=''">
				and #{param.reviewPersonId} IN (t2.REVIEW_PERSON_ID, t.SALES_CONSULTANT_ID)
			</if>
		</if>
		<if test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and  t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
		<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">and  t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}</if>
		<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''">and  t.TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}</if>
		<if test="param.deposit !=null and param.deposit !=''">and  t.DEPOSIT=#{param.deposit}</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''">and  t.APPOINTMENT_ID=#{param.appointmentId}</if>
		<if test="param.testStatus !=null and param.testStatus !=''">and  t.TEST_STATUS=#{param.testStatus}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and  t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and  t.DLR_NAME=#{param.dlrName}</if>
		<if test="param.otherAgreement !=null and param.otherAgreement !=''">and  t.OTHER_AGREEMENT=#{param.otherAgreement}</if>
		<if test="param.salesConsultantName !=null ">and  t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}</if>
		<if test="param.salesConsultantId !=null ">and  t.SALES_CONSULTANT_ID=#{param.salesConsultantId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and  t.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and  t.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''">and  t.DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation}</if>
		<if test="param.driverName !=null and param.driverName !=''">and  t.DRIVER_NAME=#{param.driverName}</if>
		<if test="param.driverPhone !=null and param.driverPhone !=''">and  t.DRIVER_PHONE=#{param.driverPhone}</if>
		<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''">and  t.DRIVING_LICENCE_TYPE=#{param.drivingLicenceType}</if>
		<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''">and  t.DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber}</if>
		<if test="param.address !=null and param.address !=''">and  t.ADDRESS=#{param.address}</if>
		<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''">and  t.DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto}</if>
		<if test="param.testRoadHaul !=null and param.testRoadHaul !=''">and  t.TEST_ROAD_HAUL=#{param.testRoadHaul}</if>
		<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''">and  t.TEST_START_ROAD_HAUL=#{param.testStartRoadHaul}</if>
		<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''">and  t.TEST_END_ROAD_HAUL=#{param.testEndRoadHaul}</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''">and  t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
		<if test="param.customerName !=null and param.customerName !=''">and  t.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerId !=null and param.customerId !=''">and  t.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''">and  t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerSex !=null and param.customerSex !=''">and  t.CUSTOMER_SEX=#{param.customerSex}</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">and  t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">and  t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''">and  t.PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.carVin !=null and param.carVin !=''">and  t.CAR_VIN=#{param.carVin}</if>
		<if test="param.testType !=null and param.testType !=''">and  t.TEST_TYPE=#{param.testType}</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''">and  t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
		<if test="param.startTime !=null and param.startTime !=''">and  t.START_TIME=#{param.startTime}</if>
		<if test="param.endTime !=null and param.endTime !=''">and  t.END_TIME=#{param.endTime}</if>
		<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''">and  t.TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement}</if>
		<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''">and  t.CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement}</if>
		<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''">and  t.CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement}</if>
		<if test="param.oemId !=null and param.oemId !=''">and  t.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and  t.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and  t.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and  t.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and  t.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and  t.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and  t.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and  t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.startTimeMin !=null"><![CDATA[and t.START_TIME>=#{param.startTimeMin}]]></if>
		<if test="param.startTimeMax !=null"><![CDATA[and t.START_TIME<date_add(#{param.startTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.endTimeMin !=null"><![CDATA[and t.END_TIME>=#{param.endTimeMin}]]></if>
		<if test="param.endTimeMax !=null"><![CDATA[and t.END_TIME<date_add(#{param.endTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
		<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t.CUSTOMER_PHONE,#{param.searchCondition})>0 or INSTR(t.CUSTOMER_NAME,#{param.searchCondition})>0)</if>
		<if test="param.infoChanMCode !=null and param.infoChanMCode != ''">and t2.INFO_CHAN_M_CODE = #{param.infoChanMCode}</if>
		<if test="param.infoChanDCode !=null and param.infoChanDCode != ''">
			and t2.INFO_CHAN_D_CODE IN
			<foreach item="item" index="index" collection="param.infoChanDCode.split(',')" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<!-- 个人试乘试驾单查询 -->
	<select id="selectSacTestDriveSheetSingle" resultType="java.util.Map">
	SELECT *,'1' as isDriveOrTask FROM (
	select t.TEST_DRIVE_SHEET_ID,
		t.TEST_DRIVE_ORDER_NO,
		t.APPOINTMENT_ID,
		<![CDATA[
		(case
			when t.IS_ENABLE='0' then '-1'
			when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '3'
			else t.TEST_STATUS end) as testStatus,
		(case
			when t.IS_ENABLE='0' then '已取消'
			when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '已过期']]>
			when t.TEST_STATUS='0' then '未开始'
			when t.TEST_STATUS='1' then '试乘试驾中'
			when t.TEST_STATUS='2' then '已结束' end) as testStatusName,
		t.TEST_STATUS AS TEST_STATUS1,
		<if test="param.testStatus1 !=null and param.testStatus1 !=''">
			(SELECT LOOKUP_VALUE_NAME FROM t_prc_mds_lookup_value T WHERE T.LOOKUP_TYPE_CODE = 'ADP_CLUE_054' AND LOOKUP_VALUE_CODE = #{param.testStatus1}) AS testStatusName1,
		</if>
		t.DLR_CODE,
		t.DLR_NAME,
		t.SALES_CONSULTANT_NAME,
		t.SALES_CONSULTANT_ID,
		t.INTEN_LEVEL_CODE,
		t.INTEN_LEVEL_NAME,
		t.DRIVER_CUSTOMER_RELATION,
		t.DRIVER_NAME,
		t.DRIVER_PHONE,
		t.DRIVING_LICENCE_TYPE,
		t.DRIVING_LICENCE_NUMBER,
		t.ADDRESS,
		t.DRIVING_LICENCE_PHOTO,
		t.TEST_ROAD_HAUL,
		t.OTHER_AGREEMENT,
		t.TEST_START_ROAD_HAUL,
		t.TEST_END_ROAD_HAUL,
		t.DLR_CLUE_ORDER_NO,
		t.CUSTOMER_NAME,
		t.CUSTOMER_ID as custId,
		t.CUSTOMER_PHONE,
		INSERT(t.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM,
		t.CUSTOMER_SEX,
		CASE WHEN t.CUSTOMER_SEX = '0' THEN '女' WHEN t.CUSTOMER_SEX = '1' THEN '男' ELSE t.CUSTOMER_SEX END customerSexName,
		t.SMALL_CAR_TYPE_CODE,
		t.SMALL_CAR_TYPE_NAME,
		t.PLATE_NUMBER,
		t.CAR_VIN,
		t.TEST_TYPE,
		t.IS_CAN_CHANGE,
		(case when t.TEST_TYPE='0' then '试乘' when t.TEST_TYPE='1' then '试驾' when t.TEST_TYPE='2' then '深度试驾' end) as testTypeName,
		t1.APPOINTMENT_TEST_DATE,
		t1.APPOINTMENT_TEST_TIME,
		t1.APPOINTMENT_START_TIME,
		t1.APPOINTMENT_END_TIME,
		t1.APPOINTMENT_ORDER_NO,
		t1.IS_TEST_DRIVE,
		t1.NEW_DLR_APPOINTMENT_START_TIME,
		t1.NEW_DLR_APPOINTMENT_END_TIME,
		t.APPOINTMENT_CHANNEL,
		t.START_TIME,
		t.END_TIME,
		CASE WHEN t.TEST_TYPE = '2' THEN CONCAT( t1.APPOINTMENT_START_TIME, " ~ ", t1.APPOINTMENT_END_TIME ) ELSE CONCAT( t1.APPOINTMENT_TEST_DATE, " ", t1.APPOINTMENT_TEST_TIME ) END testDriveTimePJ,
		t.TEST_DRIVE_AGREEMENT,
		t.CUSTOMER_ID_NUMBER_AGREEMENT,
		t.CUSTOMER_SIGNATURE_AGREEMENT,
		t.DEPOSIT,
		t.OLD_TEST_DRIVE_SHEET_ID,
		t.OEM_ID,
		t.GROUP_ID,
		t.CREATOR,
		t.CREATED_NAME,
		t.CREATED_DATE,
		t.MODIFIER,
		t.MODIFY_NAME,
		t.LAST_UPDATED_DATE,
		t.UPDATE_CONTROL_ID,
		t.IS_ENABLE,
		t2.DLR_CODE	AS clueDlrCode,
		t4.CITY_CODE,
		t4.CITY_NAME,
		t5.TESTCAR_TIME,
		t5.TESTCAR_DISTANCE,
		t5.TESTCAR_DESCRIPTION,
		t2.DLR_SHORT_NAME	AS clueDlrName,
		t.IMPORT_FLAG	AS importFlag,
		(CASE WHEN t.IMPORT_FLAG = '1' THEN '补录数据' else '' end ) as importFlagName,
		t.REPEAT_FLAG	AS repeatFlag,
		(CASE WHEN t.REPEAT_FLAG = '1' THEN '重复数据' else '' end ) as repeatFlagName,
		t.EVALUATE_FLAG	AS evaluateFlag,
		(CASE WHEN t.EVALUATE_FLAG = '1' THEN '已发送' else '' end ) as evaluateFlagName,
		t.record_id,
		t.TEST_DRIVE_METHOD,
		(CASE WHEN t.TEST_DRIVE_METHOD = '1' THEN '上门试驾'
		WHEN t.TEST_DRIVE_METHOD = '2' THEN '门店试驾'
		else '' end ) as TEST_DRIVE_METHOD_NAME,
		t2.REVIEW_PERSON_ID AS clueReviewPersonId,
		t2.REVIEW_PERSON_NAME AS clueReviewPerson,
		(CASE
		when t.dlr_code = t2.dlr_code THEN '1'
		else '0' end
		) as isOurStore
		from
		csc.t_sac_test_drive_sheet t
		left join csc.t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
		left join csc.t_sac_clue_info_dlr t2 on t.CUSTOMER_PHONE = t2.phone
		left join mp.t_usc_mdm_org_dlr t3 on t.DLR_CODE =t3.DLR_CODE
	    left join mp.t_usc_mdm_org_city t4 on t3.CITY_ID =t4.CITY_ID
		left join mp.t_usc_bu_testcar_route t5 on t.COLUMN3=t5.TESTCAR_ROUTE_ID
		where 1=1
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">
		and t4.CITY_CODE IN
		<foreach
				collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">  #{item}
		</foreach>
		</if>
		<if test="param.routeTypeName !=null and param.routeTypeName !=''"> and INSTR(t.column4,#{param.routeTypeName})>0</if>
		<if test='param.isOurStore !=null and param.isOurStore =="0"'> and t.dlr_code <![CDATA[<>]]> t2.dlr_code</if>
		<if test='param.isOurStore !=null and param.isOurStore =="1"'> and t.dlr_code = t2.dlr_code</if>
		<if test='param.isEnd !=null and param.isEnd =="0"'> and t.TEST_STATUS in('0','1')</if>
		<if test='param.isEnd !=null and param.isEnd =="1"'> and t.TEST_STATUS ='2'</if>
		<if test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and  t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
		<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">and  t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}</if>
		<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''">and  t.TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}</if>
		<if test="param.deposit !=null and param.deposit !=''">and  t.DEPOSIT=#{param.deposit}</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''">and  t.APPOINTMENT_ID=#{param.appointmentId}</if>
		<if test="param.testStatus !=null and param.testStatus !=''">and  t.TEST_STATUS=#{param.testStatus}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and  t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and  t.DLR_NAME=#{param.dlrName}</if>
		<if test="param.otherAgreement !=null and param.otherAgreement !=''">and  t.OTHER_AGREEMENT=#{param.otherAgreement}</if>
		<if test="param.salesConsultantName !=null ">and  t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}</if>
		<if test="param.salesConsultantId !=null ">and  t.SALES_CONSULTANT_ID=#{param.salesConsultantId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and  t.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and  t.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''">and  t.DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation}</if>
		<if test="param.driverName !=null and param.driverName !=''">and  t.DRIVER_NAME=#{param.driverName}</if>
		<if test="param.driverPhone !=null and param.driverPhone !=''">and  t.DRIVER_PHONE=#{param.driverPhone}</if>
		<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''">and  t.DRIVING_LICENCE_TYPE=#{param.drivingLicenceType}</if>
		<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''">and  t.DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber}</if>
		<if test="param.address !=null and param.address !=''">and  t.ADDRESS=#{param.address}</if>
		<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''">and  t.DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto}</if>
		<if test="param.testRoadHaul !=null and param.testRoadHaul !=''">and  t.TEST_ROAD_HAUL=#{param.testRoadHaul}</if>
		<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''">and  t.TEST_START_ROAD_HAUL=#{param.testStartRoadHaul}</if>
		<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''">and  t.TEST_END_ROAD_HAUL=#{param.testEndRoadHaul}</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''">and  t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
		<if test="param.customerName !=null and param.customerName !=''">and  t.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerId !=null and param.customerId !=''">and  t.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''">and  t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerSex !=null and param.customerSex !=''">and  t.CUSTOMER_SEX=#{param.customerSex}</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">and  t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">and  t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''">and  t.PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.carVin !=null and param.carVin !=''">and  t.CAR_VIN=#{param.carVin}</if>
		<if test="param.testType !=null and param.testType !=''">and  t.TEST_TYPE=#{param.testType}</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''">and  t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
		<if test="param.startTime !=null and param.startTime !=''">and  t.START_TIME=#{param.startTime}</if>
		<if test="param.endTime !=null and param.endTime !=''">and  t.END_TIME=#{param.endTime}</if>
		<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''">and  t.TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement}</if>
		<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''">and  t.CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement}</if>
		<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''">and  t.CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement}</if>
		<if test="param.oemId !=null and param.oemId !=''">and  t.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and  t.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and  t.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and  t.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and  t.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and  t.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and  t.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and  t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.startTimeMin !=null"><![CDATA[and t.START_TIME>=#{param.startTimeMin}]]></if>
		<if test="param.startTimeMax !=null"><![CDATA[and t.START_TIME<date_add(#{param.startTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.endTimeMin !=null"><![CDATA[and t.END_TIME>=#{param.endTimeMin}]]></if>
		<if test="param.endTimeMax !=null"><![CDATA[and t.END_TIME<date_add(#{param.endTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
		<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeList.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t.CUSTOMER_PHONE,#{param.searchCondition})>0 or INSTR(t.CUSTOMER_NAME,#{param.searchCondition})>0)</if>
		<if test="param.importFlag !=null and param.importFlag != ''" >and  t.IMPORT_FLAG=#{param.importFlag}</if>
		<if test="param.testDriveMethod !=null and param.testDriveMethod != ''" >
			and  t.TEST_DRIVE_METHOD=#{param.testDriveMethod}
		</if>
		<!-- <if test="param.importFlag ==null or param.importFlag == ''" >and  isnull(t.IMPORT_FLAG) </if>  -->
		<if test="param.salesConsultantIdIn !=null and param.salesConsultantIdIn !=''">
			and  t.SALES_CONSULTANT_ID IN
			<foreach collection="param.salesConsultantIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.salesConsultantNameIn !=null and param.salesConsultantNameIn !=''">
			and  t.SALES_CONSULTANT_NAME IN
			<foreach collection="param.salesConsultantNameIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">
			and t2.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdDateBeg !=null">
			and t.CREATED_DATE&gt;=#{param.createdDateBeg}
		</if>

		<if test="param.createdDateEnd !=null">
			and t.CREATED_DATE&lt;#{param.createdDateEnd}
		</if>
		order by t.CREATED_DATE desc,t1.APPOINTMENT_START_TIME
	) M
	WHERE 1 = 1
	<if test="param.testStatusName !=null and param.testStatusName !=''">
		AND M.testStatusName = #{param.testStatusName}</if>
	</select>

	<select id="selectSacTestDriveSheetSingleNoPage" resultType="java.util.Map">
	SELECT *,'1' as isDriveOrTask FROM (
	select t.TEST_DRIVE_SHEET_ID,
		t.TEST_DRIVE_ORDER_NO,
		t.APPOINTMENT_ID,
		<![CDATA[
		(case
			when t.IS_ENABLE='0' then '-1'
			when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '3'
			else t.TEST_STATUS end) as testStatus,
		(case
			when t.IS_ENABLE='0' then '已取消'
			when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '已过期']]>
			when t.TEST_STATUS='0' then '未开始'
			when t.TEST_STATUS='1' then '试乘试驾中'
			when t.TEST_STATUS='2' then '已结束' end) as testStatusName,
		t.TEST_STATUS AS TEST_STATUS1,
		<if test="param.testStatus1 !=null and param.testStatus1 !=''">
			(SELECT LOOKUP_VALUE_NAME FROM t_prc_mds_lookup_value T WHERE T.LOOKUP_TYPE_CODE = 'ADP_CLUE_054' AND LOOKUP_VALUE_CODE = #{param.testStatus1}) AS testStatusName1,
		</if>
		t.DLR_CODE,
		t.DLR_NAME,
		t.`CUSTOMER_ID_NUMBER` AS customerIdNumber,
		t.`REAL_NAME` AS realName,
		t.`TEST_DRIVE_AGREEMENT_PDF` AS testDriveAgreementPdf,
		t.SALES_CONSULTANT_NAME,
		t.SALES_CONSULTANT_ID,
		t.INTEN_LEVEL_CODE,
		t.INTEN_LEVEL_NAME,
		t.DRIVER_CUSTOMER_RELATION,
		t.DRIVER_NAME,
		t.DRIVER_PHONE,
		t.DRIVING_LICENCE_TYPE,
		t.DRIVING_LICENCE_NUMBER,
		t.ADDRESS,
		t.DRIVING_LICENCE_PHOTO,
<!--
		t.TEST_ROAD_HAUL,
-->
		t.OTHER_AGREEMENT,
		t.TEST_START_ROAD_HAUL,
		t.TEST_END_ROAD_HAUL,
		t.DLR_CLUE_ORDER_NO,
		t.CUSTOMER_NAME,
		t.CUSTOMER_ID as custId,
		t.CUSTOMER_PHONE,
		INSERT(t.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM,
		t.CUSTOMER_SEX,
		CASE WHEN t.CUSTOMER_SEX = '0' THEN '女' WHEN t.CUSTOMER_SEX = '1' THEN '男' ELSE t.CUSTOMER_SEX END customerSexName,
		t.SMALL_CAR_TYPE_CODE,
		t.SMALL_CAR_TYPE_NAME,
		t.PLATE_NUMBER,
		t.CAR_VIN,
		t.TEST_TYPE,
		t.IS_CAN_CHANGE,
		(case when t.TEST_TYPE='0' then '试乘' when t.TEST_TYPE='1' then '试驾' when t.TEST_TYPE='2' then '深度试驾' end) as testTypeName,
		t1.APPOINTMENT_TEST_DATE,
		t1.APPOINTMENT_TEST_TIME,
		t1.APPOINTMENT_START_TIME,
		t1.APPOINTMENT_END_TIME,
		t1.APPOINTMENT_ORDER_NO,
		t1.IS_TEST_DRIVE,
		t1.NEW_DLR_APPOINTMENT_START_TIME,
		t1.NEW_DLR_APPOINTMENT_END_TIME,
		t.APPOINTMENT_CHANNEL,
		t.START_TIME,
		t.END_TIME,
		CASE WHEN t.TEST_TYPE = '2' THEN CONCAT( t1.APPOINTMENT_START_TIME, " ~ ", t1.APPOINTMENT_END_TIME ) ELSE CONCAT( t1.APPOINTMENT_TEST_DATE, " ", t1.APPOINTMENT_TEST_TIME ) END testDriveTimePJ,
		t.TEST_DRIVE_AGREEMENT,
		t.CUSTOMER_ID_NUMBER_AGREEMENT,
		t.CUSTOMER_SIGNATURE_AGREEMENT,
		t.DEPOSIT,
		t.OLD_TEST_DRIVE_SHEET_ID,
		t.OEM_ID,
		t.GROUP_ID,
		t.CREATOR,
		t.CREATED_NAME,
		t.CREATED_DATE,
		t.MODIFIER,
		t.MODIFY_NAME,
		t.LAST_UPDATED_DATE,
		t.UPDATE_CONTROL_ID,
		t.IS_ENABLE,
		t2.DLR_CODE	AS clueDlrCode,
		t4.CITY_CODE,
		t4.CITY_NAME,
<!--
		t5.TESTCAR_TIME,
		t5.TESTCAR_DISTANCE,
-->
		t5.TESTCAR_DESCRIPTION,
		t6.is_valid_test_drive,
		t6.veh_start_voc,
		t6.veh_start_mileage,
		t6.veh_end_mileage,
		t6.veh_total_mileage,
		t6.veh_total_dur,
		t6.veh_max_speed,
		t6.test_drive_avg_speed,
		t6.query_bi_times,
		t6.is_enable AS biDriveDataIsEnable,
		t6.`is_good_test_drive` AS isGoodTestDrive,
		t6.`is_match_record`    AS isMatchRecord,
		t6.`record_time`        AS recordTime,
		t6.`record_duration`    AS recordDuration,
		t6.`record_score`       AS recordScore,
		t2.DLR_SHORT_NAME	AS clueDlrName,
		t.IMPORT_FLAG	AS importFlag,
		(CASE WHEN t.IMPORT_FLAG = '1' THEN '补录数据' else '' end ) as importFlagName,
		t.REPEAT_FLAG	AS repeatFlag,
		(CASE WHEN t.REPEAT_FLAG = '1' THEN '重复数据' else '' end ) as repeatFlagName,
		t.EVALUATE_FLAG	AS evaluateFlag,
		(CASE WHEN t.EVALUATE_FLAG = '1' THEN '已发送' else '' end ) as evaluateFlagName,
		t.record_id,
		t.TEST_DRIVE_METHOD,
		(CASE WHEN t.TEST_DRIVE_METHOD = '1' THEN '上门试驾'
		WHEN t.TEST_DRIVE_METHOD = '2' THEN '门店试驾'
		else '' end ) as TEST_DRIVE_METHOD_NAME,
		t2.REVIEW_PERSON_ID AS clueReviewPersonId,
		t2.REVIEW_PERSON_NAME AS clueReviewPerson,
		(CASE
		when t.dlr_code = t2.dlr_code THEN '1'
		else '0' end
		) as isOurStore
		from
		csc.t_sac_test_drive_sheet t
		left join csc.t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
		left join csc.t_sac_clue_info_dlr t2 on t.CUSTOMER_PHONE = t2.phone
		left join csc.t_sac_test_drive_vehicle_data t6 on t.TEST_DRIVE_ORDER_NO = t6.test_drive_order_no
		left join mp.t_usc_mdm_org_dlr t3 on t.DLR_CODE =t3.DLR_CODE
	    left join mp.t_usc_mdm_org_city t4 on t3.CITY_ID =t4.CITY_ID
		left join mp.t_usc_bu_testcar_route t5 on t.COLUMN3=t5.TESTCAR_ROUTE_ID
		where 1=1
		<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">
		and t4.CITY_CODE IN
			<foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">
		    	#{item}
			</foreach>
		</if>
		<if test="param.routeTypeName !=null and param.routeTypeName !=''"> and INSTR(t.column4,#{param.routeTypeName})>0</if>
		<if test='param.isOurStore !=null and param.isOurStore =="0"'> and t.dlr_code <![CDATA[<>]]> t2.dlr_code</if>
		<if test='param.isOurStore !=null and param.isOurStore =="1"'> and t.dlr_code = t2.dlr_code</if>
		<if test='param.isEnd !=null and param.isEnd =="0"'> and t.TEST_STATUS in('0','1')</if>
		<if test='param.isEnd !=null and param.isEnd =="1"'> and t.TEST_STATUS ='2'</if>
		<if test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and  t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
		<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">and  t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}</if>
		<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''">and  t.TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}</if>
		<if test="param.deposit !=null and param.deposit !=''">and  t.DEPOSIT=#{param.deposit}</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''">and  t.APPOINTMENT_ID=#{param.appointmentId}</if>
		<if test="param.testStatus !=null and param.testStatus !=''">and  t.TEST_STATUS=#{param.testStatus}</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''">and  t.DLR_CODE=#{param.dlrCode}</if>
		<if test="param.dlrName !=null and param.dlrName !=''">and  t.DLR_NAME=#{param.dlrName}</if>
		<if test="param.otherAgreement !=null and param.otherAgreement !=''">and  t.OTHER_AGREEMENT=#{param.otherAgreement}</if>
		<if test="param.salesConsultantName !=null ">and  t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}</if>
		<if test="param.salesConsultantId !=null ">and  t.SALES_CONSULTANT_ID=#{param.salesConsultantId}</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and  t.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''">and  t.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
		<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''">and  t.DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation}</if>
		<if test="param.driverName !=null and param.driverName !=''">and  t.DRIVER_NAME=#{param.driverName}</if>
		<if test="param.driverPhone !=null and param.driverPhone !=''">and  t.DRIVER_PHONE=#{param.driverPhone}</if>
		<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''">and  t.DRIVING_LICENCE_TYPE=#{param.drivingLicenceType}</if>
		<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''">and  t.DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber}</if>
		<if test="param.address !=null and param.address !=''">and  t.ADDRESS=#{param.address}</if>
		<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''">and  t.DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto}</if>
		<if test="param.testRoadHaul !=null and param.testRoadHaul !=''">and  t.TEST_ROAD_HAUL=#{param.testRoadHaul}</if>
		<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''">and  t.TEST_START_ROAD_HAUL=#{param.testStartRoadHaul}</if>
		<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''">and  t.TEST_END_ROAD_HAUL=#{param.testEndRoadHaul}</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''">and  t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
		<if test="param.customerName !=null and param.customerName !=''">and  t.CUSTOMER_NAME=#{param.customerName}</if>
		<if test="param.customerId !=null and param.customerId !=''">and  t.CUSTOMER_ID=#{param.customerId}</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''">and  t.CUSTOMER_PHONE=#{param.customerPhone}</if>
		<if test="param.customerSex !=null and param.customerSex !=''">and  t.CUSTOMER_SEX=#{param.customerSex}</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">and  t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">and  t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''">and  t.PLATE_NUMBER=#{param.plateNumber}</if>
		<if test="param.carVin !=null and param.carVin !=''">and t.CAR_VIN=#{param.carVin}</if>
		<if test="param.testType !=null and param.testType !=''">and t.TEST_TYPE=#{param.testType}</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''">and  t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
		<if test="param.startTime !=null and param.startTime !=''">and t.START_TIME=#{param.startTime}</if>
		<if test="param.endTime !=null and param.endTime !=''">and t.END_TIME=#{param.endTime}</if>
		<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''">and t.TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement}</if>
		<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''">and t.CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement}</if>
		<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''">and t.CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement}</if>
		<if test="param.oemId !=null and param.oemId !=''">and t.OEM_ID=#{param.oemId}</if>
		<if test="param.groupId !=null and param.groupId !=''">and t.GROUP_ID=#{param.groupId}</if>
		<if test="param.creator !=null and param.creator !=''">and t.CREATOR=#{param.creator}</if>
		<if test="param.createdName !=null and param.createdName !=''">and t.CREATED_NAME=#{param.createdName}</if>
		<if test="param.createdDate !=null">and t.CREATED_DATE=#{param.createdDate}</if>
		<if test="param.modifier !=null and param.modifier !=''">and t.MODIFIER=#{param.modifier}</if>
		<if test="param.modifyName !=null and param.modifyName !=''">and t.MODIFY_NAME=#{param.modifyName}</if>
		<if test="param.lastUpdatedDate !=null">and t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
		<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
		<if test="param.startTimeMin !=null"><![CDATA[and t.START_TIME>=#{param.startTimeMin}]]></if>
		<if test="param.startTimeMax !=null"><![CDATA[and t.START_TIME<date_add(#{param.startTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.endTimeMin !=null"><![CDATA[and t.END_TIME>=#{param.endTimeMin}]]></if>
		<if test="param.endTimeMax !=null"><![CDATA[and t.END_TIME<date_add(#{param.endTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
		<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
		<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeList.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
		<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t.CUSTOMER_PHONE,#{param.searchCondition})>0 or INSTR(t.CUSTOMER_NAME,#{param.searchCondition})>0)</if>
		<if test="param.importFlag !=null and param.importFlag != ''" >and t.IMPORT_FLAG=#{param.importFlag}</if>
		<if test="param.testDriveMethod !=null and param.testDriveMethod != ''" >
			and t.TEST_DRIVE_METHOD=#{param.testDriveMethod}
		</if>
		<if test="param.salesConsultantIdIn !=null and param.salesConsultantIdIn !=''">
			and t.SALES_CONSULTANT_ID IN
			<foreach collection="param.salesConsultantIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.salesConsultantNameIn !=null and param.salesConsultantNameIn !=''">
			and t.SALES_CONSULTANT_NAME IN
			<foreach collection="param.salesConsultantNameIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">
			and t2.REVIEW_PERSON_ID IN
			<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		<if test="param.createdDateBeg !=null">
			and t.CREATED_DATE&gt;=#{param.createdDateBeg}
		</if>

		<if test="param.createdDateEnd !=null">
			and t.CREATED_DATE&lt;#{param.createdDateEnd}
		</if>
		<if test="param.isValidTestDriveCn !=null and param.isValidTestDriveCn != ''" >
			<choose>
				<when test='param.isValidTestDriveCn == "1"'>and t6.is_valid_test_drive = 1</when>
				<when test='param.isValidTestDriveCn == "0"'>and (t6.is_valid_test_drive = 0 or t6.is_valid_test_drive is null)</when>
			</choose>
		</if>
		order by t.CREATED_DATE desc,t1.APPOINTMENT_START_TIME
	) M
	WHERE 1 = 1
		<if test="param.testStatusName !=null and param.testStatusName !=''">
	  		AND M.testStatusName = #{param.testStatusName}
		</if>
		<if test="param.pageNo !=null and param.pageNo >=0 and param.pageSize !=null and param.pageSize > 0">
			LIMIT #{param.pageNo}, #{param.pageSize}
		</if>
	</select>

	<select id="selectSacTestDriveSheetSingleCount" resultType="java.lang.Long">
	SELECT count(*) FROM (
		select
		<![CDATA[
			(case
				when t.IS_ENABLE='0' then '-1'
				when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '3'
				else t.TEST_STATUS end) as testStatus,
			(case
				when t.IS_ENABLE='0' then '已取消'
				when t1.APPOINTMENT_END_TIME < now() and t.TEST_STATUS='0' then '已过期']]>
			when t.TEST_STATUS='0' then '未开始'
			when t.TEST_STATUS='1' then '试乘试驾中'
			when t.TEST_STATUS='2' then '已结束' end) as testStatusName
			from
			csc.t_sac_test_drive_sheet t
			left join csc.t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
			left join csc.t_sac_clue_info_dlr t2 on t.CUSTOMER_PHONE = t2.phone
			left join csc.t_sac_test_drive_vehicle_data t6 on t.TEST_DRIVE_ORDER_NO = t6.test_drive_order_no
			left join mp.t_usc_mdm_org_dlr t3 on t.DLR_CODE =t3.DLR_CODE
			left join mp.t_usc_mdm_org_city t4 on t3.CITY_ID =t4.CITY_ID
			left join mp.t_usc_bu_testcar_route t5 on t.COLUMN3=t5.TESTCAR_ROUTE_ID
			where 1=1
			<if test="param.cityCodeIn !=null and param.cityCodeIn !=''">
			and t4.CITY_CODE IN
				<foreach collection="param.cityCodeIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.routeTypeName !=null and param.routeTypeName !=''"> and INSTR(t.column4,#{param.routeTypeName})>0</if>
			<if test='param.isOurStore !=null and param.isOurStore =="0"'> and t.dlr_code <![CDATA[<>]]> t2.dlr_code</if>
			<if test='param.isOurStore !=null and param.isOurStore =="1"'> and t.dlr_code = t2.dlr_code</if>
			<if test='param.isEnd !=null and param.isEnd =="0"'> and t.TEST_STATUS in('0','1')</if>
			<if test='param.isEnd !=null and param.isEnd =="1"'> and t.TEST_STATUS ='2'</if>
			<if test="param.testDriveSheetId !=null and param.testDriveSheetId !=''">and  t.TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}</if>
			<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">and  t.OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId}</if>
			<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''">and  t.TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}</if>
			<if test="param.deposit !=null and param.deposit !=''">and  t.DEPOSIT=#{param.deposit}</if>
			<if test="param.appointmentId !=null and param.appointmentId !=''">and  t.APPOINTMENT_ID=#{param.appointmentId}</if>
			<if test="param.testStatus !=null and param.testStatus !=''">and  t.TEST_STATUS=#{param.testStatus}</if>
			<if test="param.dlrCode !=null and param.dlrCode !=''">and  t.DLR_CODE=#{param.dlrCode}</if>
			<if test="param.dlrName !=null and param.dlrName !=''">and  t.DLR_NAME=#{param.dlrName}</if>
			<if test="param.otherAgreement !=null and param.otherAgreement !=''">and  t.OTHER_AGREEMENT=#{param.otherAgreement}</if>
			<if test="param.salesConsultantName !=null ">and  t.SALES_CONSULTANT_NAME=#{param.salesConsultantName}</if>
			<if test="param.salesConsultantId !=null ">and  t.SALES_CONSULTANT_ID=#{param.salesConsultantId}</if>
			<if test="param.intenLevelCode !=null and param.intenLevelCode !=''">and  t.INTEN_LEVEL_CODE=#{param.intenLevelCode}</if>
			<if test="param.intenLevelName !=null and param.intenLevelName !=''">and  t.INTEN_LEVEL_NAME=#{param.intenLevelName}</if>
			<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''">and  t.DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation}</if>
			<if test="param.driverName !=null and param.driverName !=''">and  t.DRIVER_NAME=#{param.driverName}</if>
			<if test="param.driverPhone !=null and param.driverPhone !=''">and  t.DRIVER_PHONE=#{param.driverPhone}</if>
			<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''">and  t.DRIVING_LICENCE_TYPE=#{param.drivingLicenceType}</if>
			<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''">and  t.DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber}</if>
			<if test="param.address !=null and param.address !=''">and  t.ADDRESS=#{param.address}</if>
			<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''">and  t.DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto}</if>
			<if test="param.testRoadHaul !=null and param.testRoadHaul !=''">and  t.TEST_ROAD_HAUL=#{param.testRoadHaul}</if>
			<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''">and  t.TEST_START_ROAD_HAUL=#{param.testStartRoadHaul}</if>
			<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''">and  t.TEST_END_ROAD_HAUL=#{param.testEndRoadHaul}</if>
			<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''">and  t.DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo}</if>
			<if test="param.customerName !=null and param.customerName !=''">and  t.CUSTOMER_NAME=#{param.customerName}</if>
			<if test="param.customerId !=null and param.customerId !=''">and  t.CUSTOMER_ID=#{param.customerId}</if>
			<if test="param.customerPhone !=null and param.customerPhone !=''">and  t.CUSTOMER_PHONE=#{param.customerPhone}</if>
			<if test="param.customerSex !=null and param.customerSex !=''">and  t.CUSTOMER_SEX=#{param.customerSex}</if>
			<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''">and  t.SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode}</if>
			<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''">and  t.SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName}</if>
			<if test="param.plateNumber !=null and param.plateNumber !=''">and  t.PLATE_NUMBER=#{param.plateNumber}</if>
			<if test="param.carVin !=null and param.carVin !=''">and t.CAR_VIN=#{param.carVin}</if>
			<if test="param.testType !=null and param.testType !=''">and t.TEST_TYPE=#{param.testType}</if>
			<if test="param.appointmentChannel !=null and param.appointmentChannel !=''">and  t.APPOINTMENT_CHANNEL=#{param.appointmentChannel}</if>
			<if test="param.startTime !=null and param.startTime !=''">and t.START_TIME=#{param.startTime}</if>
			<if test="param.endTime !=null and param.endTime !=''">and t.END_TIME=#{param.endTime}</if>
			<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''">and t.TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement}</if>
			<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''">and t.CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement}</if>
			<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''">and t.CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement}</if>
			<if test="param.oemId !=null and param.oemId !=''">and t.OEM_ID=#{param.oemId}</if>
			<if test="param.groupId !=null and param.groupId !=''">and t.GROUP_ID=#{param.groupId}</if>
			<if test="param.creator !=null and param.creator !=''">and t.CREATOR=#{param.creator}</if>
			<if test="param.createdName !=null and param.createdName !=''">and t.CREATED_NAME=#{param.createdName}</if>
			<if test="param.createdDate !=null">and t.CREATED_DATE=#{param.createdDate}</if>
			<if test="param.modifier !=null and param.modifier !=''">and t.MODIFIER=#{param.modifier}</if>
			<if test="param.modifyName !=null and param.modifyName !=''">and t.MODIFY_NAME=#{param.modifyName}</if>
			<if test="param.lastUpdatedDate !=null">and t.LAST_UPDATED_DATE=#{param.lastUpdatedDate}</if>
			<if test="param.updateControlId !=null and param.updateControlId !=''">and t.UPDATE_CONTROL_ID=#{param.updateControlId}</if>
			<if test="param.isEnable !=null and param.isEnable !=''">and t.IS_ENABLE=#{param.isEnable}</if>
			<if test="param.startTimeMin !=null"><![CDATA[and t.START_TIME>=#{param.startTimeMin}]]></if>
			<if test="param.startTimeMax !=null"><![CDATA[and t.START_TIME<date_add(#{param.startTimeMax}, INTERVAL 1 day)]]></if>
			<if test="param.endTimeMin !=null"><![CDATA[and t.END_TIME>=#{param.endTimeMin}]]></if>
			<if test="param.endTimeMax !=null"><![CDATA[and t.END_TIME<date_add(#{param.endTimeMax}, INTERVAL 1 day)]]></if>
			<if test="param.appointmentStartTimeMin !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME>=#{param.appointmentStartTimeMin}]]></if>
			<if test="param.appointmentStartTimeMax !=null"> <![CDATA[ and t1.APPOINTMENT_START_TIME<date_add(#{param.appointmentStartTimeMax}, INTERVAL 1 day)]]></if>
			<if test="param.dlrCodeIn !=null and param.dlrCodeIn !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeIn.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
			<if test="param.dlrCodeList !=null and param.dlrCodeList !=''">and t.DLR_CODE IN <foreach collection="param.dlrCodeList.split(',')" item="item" separator="," open="(" close=")"> #{item} </foreach> </if>
			<if test="param.searchCondition !=null and param.searchCondition != ''" >and (INSTR(t.CUSTOMER_PHONE,#{param.searchCondition})>0 or INSTR(t.CUSTOMER_NAME,#{param.searchCondition})>0)</if>
			<if test="param.importFlag !=null and param.importFlag != ''" >and t.IMPORT_FLAG=#{param.importFlag}</if>
			<if test="param.isValidTestDriveCn !=null and param.isValidTestDriveCn != ''" >
				<choose>
					<when test='param.isValidTestDriveCn == "1"'>and t6.is_valid_test_drive = 1</when>
					<when test='param.isValidTestDriveCn == "0"'>and (t6.is_valid_test_drive = 0 or t6.is_valid_test_drive is null)</when>
				</choose>
			</if>
			<if test="param.testDriveMethod !=null and param.testDriveMethod != ''" >
				and t.TEST_DRIVE_METHOD=#{param.testDriveMethod}
			</if>
			<if test="param.salesConsultantIdIn !=null and param.salesConsultantIdIn !=''">
				and t.SALES_CONSULTANT_ID IN
				<foreach collection="param.salesConsultantIdIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.salesConsultantNameIn !=null and param.salesConsultantNameIn !=''">
				and t.SALES_CONSULTANT_NAME IN
				<foreach collection="param.salesConsultantNameIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.reviewPersonIdIn !=null and param.reviewPersonIdIn !=''">
				and t2.REVIEW_PERSON_ID IN
				<foreach collection="param.reviewPersonIdIn.split(',')" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="param.createdDateBeg !=null">
				and t.CREATED_DATE&gt;=#{param.createdDateBeg}
			</if>

			<if test="param.createdDateEnd !=null">
				and t.CREATED_DATE&lt;#{param.createdDateEnd}
			</if>
		) M
		WHERE 1 = 1
		<if test="param.testStatusName !=null and param.testStatusName !=''"> AND M.testStatusName = #{param.testStatusName}</if>
	</select>
	<select id="selectSacTestDriveSheetSingleCountP" resultType="java.lang.Long">
		select
			count(*)
		from
		csc.t_sac_test_drive_sheet t
-- 			left join csc.t_sac_appointment_sheet t1 on (t.APPOINTMENT_ID=t1.APPOINTMENT_ID and t.DLR_CLUE_ORDER_NO=t1.DLR_CLUE_ORDER_NO)
-- 			left join csc.t_sac_clue_info_dlr t2 on t.CUSTOMER_PHONE = t2.phone
-- 			left join mp.t_usc_mdm_org_dlr t3 on t.DLR_CODE =t3.DLR_CODE
-- 			left join mp.t_usc_mdm_org_city t4 on t3.CITY_ID =t4.CITY_ID
-- 			left join mp.t_usc_bu_testcar_route t5 on t.COLUMN3=t5.TESTCAR_ROUTE_ID
		where 1=1 and t.CUSTOMER_PHONE=#{param.customerPhone}
	</select>

	<!-- 试乘试驾单新增 -->
	<insert id="insertSacTestDriveSheet">
	insert into t_sac_test_drive_sheet(
	TEST_DRIVE_SHEET_ID,
	TEST_DRIVE_ORDER_NO,
	APPOINTMENT_ID,
	TEST_STATUS,
	DLR_CODE,
	DLR_NAME,
	SALES_CONSULTANT_NAME,
	SALES_CONSULTANT_ID,
	INTEN_LEVEL_CODE,
	INTEN_LEVEL_NAME,
	DRIVER_CUSTOMER_RELATION,
	DRIVER_NAME,
	DRIVER_PHONE,
	DRIVING_LICENCE_TYPE,
	DRIVING_LICENCE_NUMBER,
	ADDRESS,
	DRIVING_LICENCE_PHOTO,
	TEST_ROAD_HAUL,
	TEST_START_ROAD_HAUL,
	TEST_END_ROAD_HAUL,
	DLR_CLUE_ORDER_NO,
	CUSTOMER_NAME,
	CUSTOMER_ID,
	CUSTOMER_PHONE,
	CUSTOMER_SEX,
	SMALL_CAR_TYPE_CODE,
	SMALL_CAR_TYPE_NAME,
	PLATE_NUMBER,
	CAR_VIN,
	TEST_TYPE,
	APPOINTMENT_CHANNEL,
	START_TIME,
	END_TIME,
	TEST_DRIVE_AGREEMENT,
	CUSTOMER_ID_NUMBER_AGREEMENT,
	CUSTOMER_SIGNATURE_AGREEMENT,
	OEM_ID,
	GROUP_ID,
	CREATOR,
	CREATED_NAME,
	CREATED_DATE,
	MODIFIER,
	MODIFY_NAME,
	LAST_UPDATED_DATE,
	UPDATE_CONTROL_ID,
	OTHER_AGREEMENT,
	IS_ENABLE,
	DEPOSIT,
	IS_CAN_CHANGE,
	OLD_TEST_DRIVE_SHEET_ID,
	TEST_DRIVE_METHOD
	)
	values(
	#{param.testDriveSheetId},
	#{param.testDriveOrderNo},
	#{param.appointmentId},
	#{param.testStatus},
	#{param.dlrCode},
	#{param.dlrName},
	#{param.salesConsultantName},
	#{param.salesConsultantId},
	#{param.intenLevelCode},
	#{param.intenLevelName},
	#{param.driverCustomerRelation},
	#{param.driverName},
	#{param.driverPhone},
	#{param.drivingLicenceType},
	#{param.drivingLicenceNumber},
	#{param.address},
	#{param.drivingLicencePhoto},
	#{param.testRoadHaul},
	#{param.testStartRoadHaul},
	#{param.testEndRoadHaul},
	#{param.dlrClueOrderNo},
	#{param.customerName},
	#{param.customerId},
	#{param.customerPhone},
	#{param.customerSex},
	#{param.smallCarTypeCode},
	#{param.smallCarTypeName},
	#{param.plateNumber},
	#{param.carVin},
	#{param.testType},
	#{param.appointmentChannel},
	#{param.startTime},
	#{param.endTime},
	#{param.testDriveAgreement},
	#{param.customerIdNumberAgreement},
	#{param.customerSignatureAgreement},
	#{param.oemId},
	#{param.groupId},
	#{param.creator},
	#{param.createdName},
	#{param.createdDate},
	#{param.modifier},
	#{param.modifyName},
	#{param.lastUpdatedDate},
	#{param.updateControlId},
	#{param.otherAgreement},
	#{param.isEnable},
	#{param.deposit},
	#{param.isCanChange},
	#{param.oldTestDriveSheetId},
	#{param.testDriveMethod}
		)
	</insert>

	<!-- 试乘试驾单批量新增 -->
	<insert id="insertSacTestDriveSheetList">
		insert into t_sac_test_drive_sheet(
			TEST_DRIVE_SHEET_ID,
			TEST_DRIVE_ORDER_NO,
			APPOINTMENT_ID,
			TEST_STATUS,
			DLR_CODE,
			DLR_NAME,
			SALES_CONSULTANT_NAME,
			SALES_CONSULTANT_ID,
			INTEN_LEVEL_CODE,
			INTEN_LEVEL_NAME,
			DRIVER_CUSTOMER_RELATION,
			DRIVER_NAME,
			DRIVER_PHONE,
			DRIVING_LICENCE_TYPE,
			DRIVING_LICENCE_NUMBER,
			ADDRESS,
			DRIVING_LICENCE_PHOTO,
			TEST_ROAD_HAUL,
			TEST_START_ROAD_HAUL,
			TEST_END_ROAD_HAUL,
			DLR_CLUE_ORDER_NO,
			CUSTOMER_NAME,
			CUSTOMER_ID,
			CUSTOMER_PHONE,
			CUSTOMER_SEX,
			SMALL_CAR_TYPE_CODE,
			SMALL_CAR_TYPE_NAME,
			PLATE_NUMBER,
			CAR_VIN,
			TEST_TYPE,
			APPOINTMENT_CHANNEL,
			START_TIME,
			END_TIME,
			TEST_DRIVE_AGREEMENT,
			CUSTOMER_ID_NUMBER_AGREEMENT,
			CUSTOMER_SIGNATURE_AGREEMENT,
			OEM_ID,
			GROUP_ID,
			CREATOR,
			CREATED_NAME,
			CREATED_DATE,
			MODIFIER,
			MODIFY_NAME,
			LAST_UPDATED_DATE,
			UPDATE_CONTROL_ID,
			OTHER_AGREEMENT,
			IS_ENABLE,
			DEPOSIT,
			IS_CAN_CHANGE,
			OLD_TEST_DRIVE_SHEET_ID,
			IMPORT_FLAG,
		    REPEAT_FLAG)
		values
		<foreach collection="list" item="param" index="index" separator=",">
		(
				  #{param.testDriveSheetId},
				  #{param.testDriveOrderNo},
				  #{param.appointmentId},
				  #{param.testStatus},
				  #{param.dlrCode},
				  #{param.dlrName},
				  #{param.salesConsultantName},
				  #{param.salesConsultantId},
				  #{param.intenLevelCode},
				  #{param.intenLevelName},
				  #{param.driverCustomerRelation},
				  #{param.driverName},
				  #{param.driverPhone},
				  #{param.drivingLicenceType},
				  #{param.drivingLicenceNumber},
				  #{param.address},
				  #{param.drivingLicencePhoto},
				  #{param.testRoadHaul},
				  #{param.testStartRoadHaul},
				  #{param.testEndRoadHaul},
				  #{param.dlrClueOrderNo},
				  #{param.customerName},
				  #{param.customerId},
				  #{param.customerPhone},
				  #{param.customerSex},
				  #{param.smallCarTypeCode},
				  #{param.smallCarTypeName},
				  #{param.plateNumber},
				  #{param.carVin},
				  #{param.testType},
				  #{param.appointmentChannel},
				  #{param.startTime},
				  #{param.endTime},
				  #{param.testDriveAgreement},
				  #{param.customerIdNumberAgreement},
				  #{param.customerSignatureAgreement},
				  #{param.oemId},
				  #{param.groupId},
				  #{param.creator},
				  #{param.createdName},
				  #{param.createdDate},
				  #{param.modifier},
				  #{param.modifyName},
				  #{param.lastUpdatedDate},
				  #{param.updateControlId},
				  #{param.otherAgreement},
				  #{param.isEnable},
				  #{param.deposit},
				  #{param.isCanChange},
				  #{param.oldTestDriveSheetId},
				  #{param.importFlag},
				  #{param.repeatFlag})
		</foreach>
	</insert>

	<!-- 试乘试驾单更新 -->
	<update id="updateSacTestDriveSheet">
		update t_sac_test_drive_sheet set
		<if test="param.testDriveOrderNo !=null and param.testDriveOrderNo !=''"> TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo},</if>
		<if test="param.oldTestDriveSheetId !=null and param.oldTestDriveSheetId !=''">OLD_TEST_DRIVE_SHEET_ID=#{param.oldTestDriveSheetId},</if>
		<if test="param.deposit !=null and param.deposit !=''">DEPOSIT=#{param.deposit},</if>
		<if test="param.isCanChange !=null and param.isCanChange !=''">IS_CAN_CHANGE=#{param.isCanChange},</if>
		<if test="param.appointmentId !=null and param.appointmentId !=''"> APPOINTMENT_ID=#{param.appointmentId},</if>
		<if test="param.testStatus !=null and param.testStatus !=''"> TEST_STATUS=#{param.testStatus},</if>
		<if test="param.dlrCode !=null and param.dlrCode !=''"> DLR_CODE=#{param.dlrCode},</if>
		<if test="param.dlrName !=null and param.dlrName !=''"> DLR_NAME=#{param.dlrName},</if>
		<if test="param.salesConsultantName !=null and param.salesConsultantName !=''"> SALES_CONSULTANT_NAME=#{param.salesConsultantName},</if>
		<if test="param.salesConsultantId !=null and param.salesConsultantId !=''"> SALES_CONSULTANT_ID=#{param.salesConsultantId},</if>
		<if test="param.intenLevelCode !=null and param.intenLevelCode !=''"> INTEN_LEVEL_CODE=#{param.intenLevelCode},</if>
		<if test="param.intenLevelName !=null and param.intenLevelName !=''"> INTEN_LEVEL_NAME=#{param.intenLevelName},</if>
		<if test="param.driverCustomerRelation !=null and param.driverCustomerRelation !=''"> DRIVER_CUSTOMER_RELATION=#{param.driverCustomerRelation},</if>
		<if test="param.driverName !=null and param.driverName !=''"> DRIVER_NAME=#{param.driverName},</if>
		<if test="param.driverPhone !=null and param.driverPhone !=''"> DRIVER_PHONE=#{param.driverPhone},</if>
		<if test="param.drivingLicenceType !=null and param.drivingLicenceType !=''"> DRIVING_LICENCE_TYPE=#{param.drivingLicenceType},</if>
		<if test="param.drivingLicenceNumber !=null and param.drivingLicenceNumber !=''"> DRIVING_LICENCE_NUMBER=#{param.drivingLicenceNumber},</if>
		<if test="param.address !=null and param.address !=''"> ADDRESS=#{param.address},</if>
		<if test="param.otherAgreement !=null">OTHER_AGREEMENT=#{param.otherAgreement},</if>
		<if test="param.drivingLicencePhoto !=null and param.drivingLicencePhoto !=''"> DRIVING_LICENCE_PHOTO=#{param.drivingLicencePhoto},</if>
		<if test="param.testRoadHaul !=null and param.testRoadHaul !=''"> TEST_ROAD_HAUL=#{param.testRoadHaul},</if>
		<if test="param.testStartRoadHaul !=null and param.testStartRoadHaul !=''"> TEST_START_ROAD_HAUL=#{param.testStartRoadHaul},</if>
		<if test="param.testEndRoadHaul !=null and param.testEndRoadHaul !=''"> TEST_END_ROAD_HAUL=#{param.testEndRoadHaul},</if>
		<if test="param.dlrClueOrderNo !=null and param.dlrClueOrderNo !=''"> DLR_CLUE_ORDER_NO=#{param.dlrClueOrderNo},</if>
		<if test="param.customerName !=null and param.customerName !=''"> CUSTOMER_NAME=#{param.customerName},</if>
		<if test="param.customerId !=null and param.customerId !=''"> CUSTOMER_ID=#{param.customerId},</if>
		<if test="param.customerPhone !=null and param.customerPhone !=''"> CUSTOMER_PHONE=#{param.customerPhone},</if>
		<if test="param.customerSex !=null and param.customerSex !=''"> CUSTOMER_SEX=#{param.customerSex},</if>
		<if test="param.smallCarTypeCode !=null and param.smallCarTypeCode !=''"> SMALL_CAR_TYPE_CODE=#{param.smallCarTypeCode},</if>
		<if test="param.smallCarTypeName !=null and param.smallCarTypeName !=''"> SMALL_CAR_TYPE_NAME=#{param.smallCarTypeName},</if>
		<if test="param.plateNumber !=null and param.plateNumber !=''"> PLATE_NUMBER=#{param.plateNumber},</if>
		<if test="param.carVin !=null and param.carVin !=''"> CAR_VIN=#{param.carVin},</if>
		<if test="param.testType !=null and param.testType !=''"> TEST_TYPE=#{param.testType},</if>
		<if test="param.appointmentChannel !=null and param.appointmentChannel !=''"> APPOINTMENT_CHANNEL=#{param.appointmentChannel},</if>
		<if test="param.startTime !=null and param.startTime !=''"> START_TIME=#{param.startTime},</if>
		<if test="param.endTime !=null and param.endTime !=''"> END_TIME=#{param.endTime},</if>
		<if test="param.testDriveAgreement !=null and param.testDriveAgreement !=''"> TEST_DRIVE_AGREEMENT=#{param.testDriveAgreement},</if>
		<if test="param.customerIdNumberAgreement !=null and param.customerIdNumberAgreement !=''"> CUSTOMER_ID_NUMBER_AGREEMENT=#{param.customerIdNumberAgreement},</if>
		<if test="param.customerSignatureAgreement !=null and param.customerSignatureAgreement !=''"> CUSTOMER_SIGNATURE_AGREEMENT=#{param.customerSignatureAgreement},</if>
		<if test="param.isEnable !=null and param.isEnable !=''"> IS_ENABLE=#{param.isEnable},</if>
		<if test="param.oemId !=null and param.oemId !=''"> OEM_ID=#{param.oemId},</if>
		<if test="param.groupId !=null and param.groupId !=''"> GROUP_ID=#{param.groupId},</if>
		<if test="param.creator !=null and param.creator !=''"> CREATOR=#{param.creator},</if>
		<if test="param.createdName !=null and param.createdName !=''"> CREATED_NAME=#{param.createdName},</if>
		<if test="param.createdDate !=null"> CREATED_DATE=#{param.createdDate},</if>
		<if test="param.modifier !=null and param.modifier !=''"> MODIFIER=#{param.modifier},</if>
		<if test="param.modifyName !=null and param.modifyName !=''"> MODIFY_NAME=#{param.modifyName},</if>

		<if test="param.column1 !=null and param.column1 !=''"> COLUMN1=#{param.column1},</if>
		<if test="param.column2 !=null and param.column2 !=''"> COLUMN2=#{param.column2},</if>
		<if test="param.column3 !=null and param.column3 !=''"> COLUMN3=#{param.column3},</if>
		<if test="param.column4 !=null and param.column4 !=''"> COLUMN4=#{param.column4},</if>
		<if test="param.column5 !=null and param.column5 !=''"> COLUMN5=#{param.column5},</if>
		<if test="param.column6 !=null and param.column6 !=''"> COLUMN6=#{param.column6},</if>
		<if test="param.column7 !=null and param.column7 !=''"> COLUMN7=#{param.column7},</if>
		<if test="param.column8 !=null and param.column8 !=''"> COLUMN8=#{param.column8},</if>
		<if test="param.column9 !=null and param.column9 !=''"> COLUMN9=#{param.column9},</if>
		<if test="param.column10 !=null and param.column10 !=''"> COLUMN10=#{param.column10},</if>
		<if test="param.updateControlId !=null and param.updateControlId !=''"> UPDATE_CONTROL_ID=#{param.updateControlId},</if>
		<if test="param.evaluateFlag !=null and param.evaluateFlag !=''"> EVALUATE_FLAG=#{param.evaluateFlag},</if>
		<if test="param.testDriveMethod !=null and param.testDriveMethod !=''"> TEST_DRIVE_METHOD=#{param.testDriveMethod},</if>
		<if test="param.IDNumberEncryption !=null and param.IDNumberEncryption !=''"> CUSTOMER_ID_NUMBER=#{param.IDNumberEncryption},</if>
		<if test="param.realName !=null and param.realName !=''"> REAL_NAME=#{param.realName},</if>
		<if test="param.testDriveAgreementPDF !=null and param.testDriveAgreementPDF !=''"> TEST_DRIVE_AGREEMENT_PDF=#{param.testDriveAgreementPDF},</if>
		LAST_UPDATED_DATE=now()
		where TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}
	</update>

	<delete id="deleteSacTestDriveSheet">
		DELETE
		FROM
		t_sac_test_drive_sheet
		WHERE
		TEST_DRIVE_SHEET_ID=#{param.testDriveSheetId}
		and  APPOINTMENT_ID=#{param.appointmentId}
	</delete>

	<select id="selectClueInfoDlr" resultType="java.util.Map">
		select
			INTEN_CAR_TYPE_CODE
			from  t_sac_clue_info_dlr
		where PHONE=#{customerPhone}
    </select>

	<update id="updateClueInfoDlrActivation">
		update
			adp_leads.t_sac_clue_info_dlr
			set
				INTEN_CAR_TYPE_CODE=#{carTypeCode},
				INTEN_CAR_TYPE_NAME=#{carTypeCode},
       			FIRST_TESTDRIVER_TIME=(case when FIRST_TESTDRIVER_TIME  is null
		then  #{param.lastTestdriverTime} else FIRST_TESTDRIVER_TIME end),
			LAST_TESTDRIVER_TIME=#{param.lastTestdriverTime},
				LAST_UPDATED_DATE=now()
		where PHONE=#{param.customerPhone}
	</update>

	<update id="updateClueInfoDlr">
		update
			csc.t_sac_clue_info_dlr
			set
				INTEN_CAR_TYPE_CODE=#{carTypeCode},
				INTEN_CAR_TYPE_NAME=#{carTypeCode},
       			FIRST_TESTDRIVER_TIME=(case when FIRST_TESTDRIVER_TIME  is null
		then  #{param.lastTestdriverTime} else FIRST_TESTDRIVER_TIME end),
			LAST_TESTDRIVER_TIME=#{param.lastTestdriverTime},
				LAST_UPDATED_DATE=now()
		where PHONE=#{param.customerPhone}
	</update>

	<update id="updateReview">
		update
			csc.t_sac_review
		set
			INTEN_CAR_TYPE_CODE=#{carTypeCode},
			INTEN_CAR_TYPE_NAME=#{carTypeCode},
			LAST_UPDATED_DATE=now()
		where PHONE=#{param.customerPhone}
	</update>

	<insert id="insertCdpLeads">
		insert into interfacecenter.t_ifs_base_cdp_leads(
			logs_id,
			bk,
			mobile,
			c_interested_car_model,
			insert_date,
			send_flag,
			send_date,
			remark,
			c_lastupdate_system
		)values (
				uuid(),
			#{bk},
			#{mobile},
			#{c_interested_car_model},
			now(),
			'0',
			now(),
			#{remark},
			'ADP'
						)
    </insert>


	<select id="findSheet" resultType="java.util.HashMap">
		select
			a.TEST_DRIVE_ORDER_NO ,
			b.EMP_CODE ,
			DATE_FORMAT(a.RECEIVER_TIME,'%Y-%m-%d %H:%i:%s') RECEIVER_TIME,
			a.CUSTOMER_NAME,
			a.CUSTOMER_ID,
			a.CUSTOMER_PHONE,
			a.SMALL_CAR_TYPE_CODE,
			DATE_FORMAT(a.START_TIME,'%Y-%m-%d %H:%i:%s') START_TIME,
			a.COLUMN2 inviteCode,
			t1.APPOINTMENT_END_TIME,
			a.SMALL_CAR_TYPE_NAME,
			a.APPOINTMENT_ID,
			a.DLR_CODE,
			a.DLR_NAME,
			a.CUSTOMER_NAME,
			a.CUSTOMER_PHONE,
		INSERT(a.CUSTOMER_PHONE,4,4,'****') as CUSTOMER_PHONE_TM,
			a.COLUMN4,
			a.TEST_STATUS,
			a.SALES_CONSULTANT_NAME
		from t_sac_test_drive_sheet a
				 LEFT JOIN csc.t_sac_appointment_sheet t1 ON  a.APPOINTMENT_ID = t1.APPOINTMENT_ID
				 left join mp.t_usc_mdm_org_employee b on a.SALES_CONSULTANT_ID=b.USER_ID
		where TEST_DRIVE_SHEET_ID=#{testDriveSheetId}
	</select>

	<select id="findUrl" resultType="java.util.Map">
		select
			LOOKUP_VALUE_NAME url,
			IS_ENABLE
		from mp.t_prc_mds_lookup_value
		where
			LOOKUP_TYPE_CODE='VE1040'
		and LOOKUP_VALUE_CODE='1'
    </select>

	<select id="findProductSpecialist" resultType="com.ly.adp.drive.entities.out.ProductSpecialistVO">
		select
			a.EMP_NAME,
			a.USER_ID,
			b.STATION_NAME,
			a.mobile
			from mp.t_usc_mdm_org_employee a
			left join mp.t_usc_mdm_org_station b on a.STATION_ID=b.STATION_ID
		<trim prefix="WHERE" prefixOverrides="AND |OR ">
			and a.DLR_CODE=#{dlrCode}
			and a.USER_STATUS='1'
          and a.STATION_ID in
			<foreach collection="stationId.split(',')"
					 item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</trim>
	</select>

	<update id="testDriveTransfer">

		update
			t_sac_test_drive_sheet
			set
				SALES_CONSULTANT_NAME=#{param.empName},
				SALES_CONSULTANT_ID=#{param.userId},
				MODIFY_NAME=#{empName},
				LAST_UPDATED_DATE=now(),
				UPDATE_CONTROL_ID=uuid()
	where
			UPDATE_CONTROL_ID=#{param.updateControlSheetId}
		and TEST_DRIVE_ORDER_NO=#{param.testDriveOrderNo}

	</update>

	<select id="findReceiverTime" resultType="java.util.Map">
		select
			RECEIVER_TIME
		from csc.t_sac_test_drive_sheet
		where
			TEST_DRIVE_SHEET_ID=#{testDriveSheetId}
	</select>
</mapper>

