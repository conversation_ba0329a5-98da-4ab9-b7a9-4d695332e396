<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.adp.drive.idal.mapper.TestDriveVehicleDataMapper">

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE t_sac_test_drive_vehicle_data
            SET
            is_valid_test_drive = #{item.isValidTestDrive},
            veh_start_voc = #{item.vehStartVoc},
            veh_start_mileage = #{item.vehStartMileage},
            veh_end_mileage = #{item.vehEndMileage},
            veh_total_mileage = #{item.vehTotalMileage},
            veh_total_dur = #{item.vehTotalDur},
            veh_max_speed = #{item.vehMaxSpeed},
            test_drive_avg_speed = #{item.testDriveAvgSpeed},
            `is_good_test_drive` = #{item.isGoodTestDrive},
            `is_match_record` = #{item.isMatchRecord},
            `record_time` = #{item.recordTime},
            `record_duration` = #{item.recordDuration},
            `record_score` = #{item.recordScore},
            modifier = #{item.modifier},
            last_updated_time = #{item.lastUpdatedTime},
            query_bi_times = #{item.queryBiTimes},
            is_enable = #{item.isEnable}
            WHERE vehicle_data_id = #{item.vehicleDataId}
        </foreach>
    </update>

    <insert id="batchInsert">
        INSERT INTO t_sac_test_drive_vehicle_data (
        vehicle_data_id,
        test_drive_order_no,
        is_valid_test_drive,
        veh_start_voc,
        veh_start_mileage,
        veh_end_mileage,
        veh_total_mileage,
        veh_total_dur,
        veh_max_speed,
        test_drive_avg_speed,
        is_enable,
        `is_good_test_drive`,
        `is_match_record`,
        `record_time`,
        `record_duration`,
        `record_score`,
        creator,
        created_time,
        modifier,
        last_updated_time,
        query_bi_times
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.vehicleDataId},
            #{item.testDriveOrderNo},
            #{item.isValidTestDrive},
            #{item.vehStartVoc},
            #{item.vehStartMileage},
            #{item.vehEndMileage},
            #{item.vehTotalMileage},
            #{item.vehTotalDur},
            #{item.vehMaxSpeed},
            #{item.testDriveAvgSpeed},
            #{item.isEnable},
            #{item.isGoodTestDrive},
            #{item.isMatchRecord},
            #{item.recordTime},
            #{item.recordDuration},
            #{item.recordScore},
            #{item.creator},
            #{item.createdTime},
            #{item.modifier},
            #{item.lastUpdatedTime},
            #{item.queryBiTimes}
            )
        </foreach>
    </insert>
</mapper>