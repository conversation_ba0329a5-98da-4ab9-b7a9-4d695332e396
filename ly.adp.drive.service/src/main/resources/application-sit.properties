# ????Feign token????

mp.component.enable.feign.token=true

# allinone ??

mp.component.token.url=http://ly-mp-allinone-cloud-service.adp-sit.svc.cluster.local:8080

# base??

ref.url.adp.base=http://adp-java-base-service.adp-sit.svc:8080

#????

bucn.ms.adapter=bucn.redis.mq

#????

csc.clue.addreview.strategy=addReviewMq

spring.application.name=ly-adp-drive-service

# seata.service.default.grouplist=adp-seata-server.adp-sit.svc:8091
seata.enabled=true
seata.application-id=ly-adp-drive-service
seata.tx-service-group=default_tx_group
seata.scan-packages=com.ly.adp
seata.enable-auto-data-source-proxy=false
seata.data-source-proxy-mode=AT
seata.service.vgroup-mapping.default_tx_group=default
seata.service.default.grouplist=seata-server-v2.adp-sit.svc.cluster.local:8091
service.vgroupMapping.default_tx_group=seata-server-v2.adp-sit.svc.cluster.local:8091

#feign.refer

refer.url.adp.clue=http://adp-java-csc-service.adp-sit.svc:8080

refer.url.xapi.api=http://adp-java-xapi-api.adp-sit.svc:8080

refer.url.mp.allinone=http://ly-mp-allinone-cloud-service.adp-sit.svc:8080

refer.url.adp.base=http://adp-java-base-service.adp-sit.svc:8080

#??????

server.port=8083

#Redis??

#??servers???(",")??,?????redis???IP,???redis??IP

#sentinel?????masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,??mymaster?**************:63793,**************:63794

#redis.session.servers=**************:6379,**************:6379,**************:6379

redis.session.servers=**************:6379

#redis??,??redis????????

redis.session.password=adb@smart2021

#redis.servers=**************:6379,**************:6379,**************:6379

redis.servers=**************:6379

redis.password=adb@smart2021

#???????

redis.pool.maxActive=10000

#??????(??:?)

redis.pool.timeout=3000

#????(??:?)

redis.pool.expires=86400

#?????jedis??????????alidate??????true?????jedis????????

redis.pool.testOnBorrow=true

#?return?pool????????validate???

redis.pool.testOnReturn=true

#session time out (??:?)

session.timeout=3600

#  redisson??????

# ???????? true:?? false:???,?????, ????????? ????

redisson.redis.enable=true

# ??????????????

redisson.redis.servers=**************:6379

redisson.redis.password=adb@smart2021

# ?????????????????????????????????????30000

redisson.redis.lockWatchdogTimeout=20000

# ????????????????????? 1000

redisson.redis.scanInterval=1000

# ??????????? ???????????????????????????????64

redisson.redis.masterConnectionPoolSize=64

# ??????????? ?????????????? ????????????????????????????????????64

redisson.redis.slaveConnectionPoolSize=64

#????AMQ(true,false)

mp.component.amqOpen=false

# MQ?? 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (????1:RabbitMQ, ??????amqType, ?????????ActiveMQ)

mp.component.amqType=3

mp.component.amqUrl=172.26.165.86:9876;172.26.165.93:9876

# MQ?????RabbitMQ??

mp.component.amqPort=5672

mp.component.amqUser=rocketadmin

mp.component.amqPwd=Mp@2020

#????????:???:????;???:???:????????????????????1????????????????????

mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue

#????????, ???false, ?????????, ????

mp.component.pendingMsg=true

#????????, ???false, ?????????, ????

mp.component.noticeMsg=true

#????CC??, ???false, ?????CC??, ????

mp.component.ccMsg=false

# ????????

mp.component.form.fileDir=/mpjava/form/files

#????????????????DAL?,???","????;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver

write.mp.jdbc.packagescan=com.ly.mp.**.mysql

#mp????(??)???? ??: normal ??(jta) : jta  ???:tcc

write.mp.jdbc.transactionPolicy=gtsx

#?????????????,??????????????????.?????????true:key????,false:key????

write.mp.jdbc.upperCaseColumn=true

# url,username,password???????????

write.mp.jdbc.name=mp_write

write.mp.jdbc.url=*************************************************************************************************************************************

write.mp.jdbc.username=adpuser

write.mp.jdbc.password=Adp@smart2024

other.write.mp.jdbc.name[0]=tidb

other.write.mp.jdbc.url[0]=********************************************************************************************************************

other.write.mp.jdbc.username[0]=adpuser

other.write.mp.jdbc.password[0]=Adp@smart2024

#read.jdbc.name[mp_write#1]=default_mp_read

#read.jdbc.url[mp_write#1]=********************************************

#read.jdbc.username[mp_write#1]=mp24

#read.jdbc.password[mp_write#1]=mp24

mp.read.db.size=0

#druid datasource

#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE

druid.initialSize=10

druid.minIdle=10

druid.maxActive=200

druid.maxWait=60000

druid.timeBetweenEvictionRunsMillis=60000

druid.minEvictableIdleTimeMillis=300000

druid.validationQuery=select 1 from dual

druid.testWhileIdle=true

druid.testOnBorrow=false

druid.testOnReturn=false

druid.poolPreparedStatements=false

druid.maxPoolPreparedStatementPerConnectionSize=20

#druid.keepAlive=true

druid.phyTimeoutMillis=1200000

#wall,slf4j,stat

druid.filters=stat

#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3

#mp2.24????

#mybatis

mybatis-plus.mapperLocations=classpath:/mybatis/mapping/*Mapper.xml

#???????package?????????

mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities

mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums

#???????

#????  AUTO:"???ID??", INPUT:"????ID",ID_WORKER:"????ID (??????ID)", UUID:"????ID UUID";

mybatis-plus.global-config.db-config.id-type=UUID

#???? IGNORED:"????",NOT_NULL:"? NULL ??"),NOT_EMPTY:"????"

mybatis-plus.global-config.db-config.field-strategy=not_empty

#???????

mybatis-plus.global-config.db-config.column-underline=true

#??????????

#capital-mode: true

#??????

mybatis-plus.global-config.db-config.logic-delete-value=0

mybatis-plus.global-config.db-config.logic-not-delete-value=1

#mybatis-plus.global-config.db-config.db-type=sqlserver

#??mapper ????

mybatis-plus.global-config.refresh=true

# ????

mybatis-plus.configuration.map-underscore-to-camel-case=true

mybatis-plus.configuration.cache-enabled=false

mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

bucn.result.stack.enable=false
# ????????
weCom.msgType=text
weCom.driveTask.url=https://adp-uat.smart.cn/wx/#/?wxToken=%7B%22empname%22%3A%22%E5%86%85%E7%BD%AE%E8%B6%85%E7%BA%A7%E7%AE%A1%E7%90%86%E5%91%98%22,%22expires%22%3A10,%22initstatus%22%3A%221%22,%22msg%22%3A%22Success%22,%22result%22%3A%221%22,%22timeout%22%3A%221800%22,%22token%22%3A%22{0}%22,%22userid%22%3A%221%22,%22username%22%3A%22xtadmin%22,%22usertype%22%3A%221%22,%22wxbind%22%3A%220%22%7D

# biConfig
refer.bi.url=http://0165f8404b7a41f5b4e24bcddbe72ff4-cn-hangzhou.alicloudapi.com/adp/test_drive
refer.bi.retryTime=3
refere.bi.timeOut=30000
