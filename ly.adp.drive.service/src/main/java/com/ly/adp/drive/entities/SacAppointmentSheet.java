package com.ly.adp.drive.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 试乘试驾预约单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@TableName("t_sac_appointment_sheet")
public class SacAppointmentSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预约单ID
     */
    @TableId("APPOINTMENT_ID")
    private String appointmentId;

    /**
     * 是否已试乘试驾
     */
    @TableField("IS_TEST_DRIVE")
    private String isTestDrive;

    /**
     * 所属专营店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 所属专营店名称
     */
    @TableField("DLR_NAME")
    private String dlrName;

    /**
     * 线索单号
     */
    @TableField("DLR_CLUE_ORDER_NO")
    private String dlrClueOrderNo;

    /**
     * 客户姓名
     */
    @TableField("CUSTOMER_NAME")
    private String customerName;

    /**
     * 客户ID
     */
    @TableField("CUSTOMER_ID")
    private String customerId;

    /**
     * 客户电话
     */
    @TableField("CUSTOMER_PHONE")
    private String customerPhone;

    /**
     * 性别
     */
    @TableField("CUSTOMER_SEX")
    private String customerSex;

    /**
     * 预约单号
     */
    @TableField("APPOINTMENT_ORDER_NO")
    private String appointmentOrderNo;

    /**
     * 预约时间(什么时候预约)
     */
    @TableField("APPOINTMENT_TIME")
    private String appointmentTime;

    /**
     * 试乘试驾车型编码
     */
    @TableField("SMALL_CAR_TYPE_CODE")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @TableField("SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @TableField("PLATE_NUMBER")
    private String plateNumber;

    /**
     * VIN(车架号)
     */
    @TableField("CAR_VIN")
    private String carVin;


    /**
     * 试乘试驾类型(0：试乘，1：试驾,2:超长试乘试驾)
     */
    @TableField("TEST_TYPE")
    private String testType;

    /**
     * 预约试乘试驾日期(普通试乘试驾)
     */
    @TableField("APPOINTMENT_TEST_DATE")
    private String appointmentTestDate;

    /**
     * 预约试乘试驾时间段(普通试乘试驾)
     */
    @TableField("APPOINTMENT_TEST_TIME")
    private String appointmentTestTime;

    /**
     * 预约超长试驾开始时间
     */
    @TableField("APPOINTMENT_START_TIME")
    private String appointmentStartTime;

    /**
     * 预约超长试驾结束时间
     */
    @TableField("APPOINTMENT_END_TIME")
    private String appointmentEndTime;

    /**
     * 新门店预约试乘试驾开始时间
     */
    @TableField("NEW_DLR_APPOINTMENT_START_TIME")
    private String newDlrAppointmentStartTime;

    /**
     * 新门店预约试乘试驾结束时间
     */
    @TableField("NEW_DLR_APPOINTMENT_END_TIME")
    private String newDlrAppointmentEndTime;
    /**
     * 预约渠道(0：门店自建，1：线上预约)
     */
    @TableField("APPOINTMENT_CHANNEL")
    private String appointmentChannel;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    public String getAppointmentId() {
        return appointmentId;
    }

    public void setAppointmentId(String appointmentId) {
        this.appointmentId = appointmentId;
    }

    public String getIsTestDrive() {
        return isTestDrive;
    }

    public void setIsTestDrive(String isTestDrive) {
        this.isTestDrive = isTestDrive;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }

    public String getDlrClueOrderNo() {
        return dlrClueOrderNo;
    }

    public void setDlrClueOrderNo(String dlrClueOrderNo) {
        this.dlrClueOrderNo = dlrClueOrderNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getCustomerSex() {
        return customerSex;
    }

    public void setCustomerSex(String customerSex) {
        this.customerSex = customerSex;
    }

    public String getAppointmentOrderNo() {
        return appointmentOrderNo;
    }

    public void setAppointmentOrderNo(String appointmentOrderNo) {
        this.appointmentOrderNo = appointmentOrderNo;
    }

    public String getAppointmentTime() {
        return appointmentTime;
    }

    public void setAppointmentTime(String appointmentTime) {
        this.appointmentTime = appointmentTime;
    }

    public String getSmallCarTypeCode() {
        return smallCarTypeCode;
    }

    public void setSmallCarTypeCode(String smallCarTypeCode) {
        this.smallCarTypeCode = smallCarTypeCode;
    }

    public String getSmallCarTypeName() {
        return smallCarTypeName;
    }

    public void setSmallCarTypeName(String smallCarTypeName) {
        this.smallCarTypeName = smallCarTypeName;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getCarVin() {
        return carVin;
    }

    public void setCarVin(String carVin) {
        this.carVin = carVin;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public String getAppointmentTestDate() {
        return appointmentTestDate;
    }

    public void setAppointmentTestDate(String appointmentTestDate) {
        this.appointmentTestDate = appointmentTestDate;
    }

    public String getAppointmentTestTime() {
        return appointmentTestTime;
    }

    public void setAppointmentTestTime(String appointmentTestTime) {
        this.appointmentTestTime = appointmentTestTime;
    }

    public String getAppointmentStartTime() {
        return appointmentStartTime;
    }

    public void setAppointmentStartTime(String appointmentStartTime) {
        this.appointmentStartTime = appointmentStartTime;
    }

    public String getNewDlrAppointmentStartTime() {
        return newDlrAppointmentStartTime;
    }

    public void setNewDlrAppointmentStartTime(String newDlrAppointmentStartTime) {
        this.newDlrAppointmentStartTime = newDlrAppointmentStartTime;
    }

    public String getNewDlrAppointmentEndTime() {
        return newDlrAppointmentEndTime;
    }

    public void setNewDlrAppointmentEndTime(String newDlrAppointmentEndTime) {
        this.newDlrAppointmentEndTime = newDlrAppointmentEndTime;
    }

    public String getAppointmentEndTime() {
        return appointmentEndTime;
    }

    public void setAppointmentEndTime(String appointmentEndTime) {
        this.appointmentEndTime = appointmentEndTime;
    }

    public String getAppointmentChannel() {
        return appointmentChannel;
    }

    public void setAppointmentChannel(String appointmentChannel) {
        this.appointmentChannel = appointmentChannel;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    @Override
    public String toString() {
        return "SacAppointmentSheet{" + "appointmentId=" + appointmentId + ", isTestDrive=" + isTestDrive + ", dlrCode="
                + dlrCode + ", dlrName=" + dlrName + ", dlrClueOrderNo=" + dlrClueOrderNo + ", customerName="
                + customerName + ", customerId=" + customerId + ", customerPhone=" + customerPhone + ", customerSex="
                + customerSex + ", appointmentOrderNo=" + appointmentOrderNo + ", appointmentTime=" + appointmentTime
                + ", smallCarTypeCode=" + smallCarTypeCode + ", smallCarTypeName=" + smallCarTypeName + ", plateNumber="
                + plateNumber + ", testType=" + testType + ", appointmentTestDate=" + appointmentTestDate
                + ", appointmentTestTime=" + appointmentTestTime + ", appointmentStartTime=" + appointmentStartTime
                + ", appointmentEndTime=" + appointmentEndTime + ", appointmentChannel=" + appointmentChannel
                + ", oemId=" + oemId + ", groupId=" + groupId + ", creator=" + creator + ", createdName=" + createdName
                + ", createdDate=" + createdDate + ", modifier=" + modifier + ", modifyName=" + modifyName
                + ", newDlrAppointmentStartTime=" + newDlrAppointmentStartTime + ", newDlrAppointmentEndTime=" + newDlrAppointmentEndTime
                + ", lastUpdatedDate=" + lastUpdatedDate + ", updateControlId=" + updateControlId + ", isEnable="
                + isEnable + ", carVin=" + carVin + ", column1=" + column1 + ", column2=" + column2 + ", column3=" + column3 + ", column4="
                + column4 + ", column5=" + column5 + ", column6=" + column6 + ", column7=" + column7 + ", column8="
                + column8 + ", column9=" + column9 + ", column10=" + column10 + "}";
    }
}
