package com.ly.adp.drive.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class SacOneCustResumeIn extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	// 定义参与排序字段
	String columnArr = "CLUE_LEVEL_CODE,BUSS_TIME,CREATED_DATE,LAST_UPDATED_DATE";

	@ApiModelProperty(value = "排序列", required = false, example = "")
	private String column;
	@ApiModelProperty(value = "排序方式", required = false, example = "")
	private String sorting;
	@ApiModelProperty(value = "客户ID", required = false, example = "")
	private String custId;
	@ApiModelProperty(value = "SMARTID", required = false, example = "")
	private String smartId;
	@ApiModelProperty(value = "意向级别", required = false, example = "")
	private String clueLevelCode;
	@ApiModelProperty(value = "数据所属门店编码", required = false, example = "")
	private String dlrCodeOwner;
	@ApiModelProperty(value = "数据所属门店名称", required = false, example = "")
	private String dlrNameOwner;
	@ApiModelProperty(value = "场景编码", required = false, example = "")
	private String senceCode;
	@ApiModelProperty(value = "场景名称", required = false, example = "")
	private String senceName;
	@ApiModelProperty(value = "客户履历内容", required = false, example = "")
	private String resumeDesc;
	@ApiModelProperty(value = "关联单据ID", required = false, example = "")
	private String relationBillId;
	@ApiModelProperty(value = "作业时间", required = false, example = "")
	private String bussTime;
	
	public String getBussTime() {
		return bussTime;
	}
	public void setBussTime(String bussTime) {
		this.bussTime = bussTime;
	}
	public String getColumnArr() {
		return columnArr;
	}
	public void setColumnArr(String columnArr) {
		this.columnArr = columnArr;
	}
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	public String getSorting() {
		return sorting;
	}
	public void setSorting(String sorting) {
		this.sorting = sorting;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getSmartId() {
		return smartId;
	}
	public void setSmartId(String smartId) {
		this.smartId = smartId;
	}
	public String getClueLevelCode() {
		return clueLevelCode;
	}
	public void setClueLevelCode(String clueLevelCode) {
		this.clueLevelCode = clueLevelCode;
	}
	public String getDlrCodeOwner() {
		return dlrCodeOwner;
	}
	public void setDlrCodeOwner(String dlrCodeOwner) {
		this.dlrCodeOwner = dlrCodeOwner;
	}
	public String getDlrNameOwner() {
		return dlrNameOwner;
	}
	public void setDlrNameOwner(String dlrNameOwner) {
		this.dlrNameOwner = dlrNameOwner;
	}
	public String getSenceCode() {
		return senceCode;
	}
	public void setSenceCode(String senceCode) {
		this.senceCode = senceCode;
	}
	public String getSenceName() {
		return senceName;
	}
	public void setSenceName(String senceName) {
		this.senceName = senceName;
	}
	public String getResumeDesc() {
		return resumeDesc;
	}
	public void setResumeDesc(String resumeDesc) {
		this.resumeDesc = resumeDesc;
	}
	public String getRelationBillId() {
		return relationBillId;
	}
	public void setRelationBillId(String relationBillId) {
		this.relationBillId = relationBillId;
	}
}
