package com.ly.adp.drive.entities;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

public class TimeLineEffective {
    Logger log = LoggerFactory.getLogger(TimeLineEffective.class);
    private List<String> old;
    private List<String> performance;

    public static String checkCurrentTimeInRange(TimeLineEffective timeLineEffective) {
        LocalTime currentTime = LocalTime.now();

        // 判断当前时间是否在 old 的时间范围内
        if (isInOldRange(timeLineEffective.getOld(), currentTime)) {
            return "old";
        }

        // 如果不在 old 范围内，直接返回 performance
        return "performance";
    }

    /**
     * 判断当前时间是否在 old 的时间范围内
     */
    private static boolean isInOldRange(List<String> oldRanges, LocalTime currentTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        for (String range : oldRanges) {
            String[] times = range.split("~");
            LocalTime start = LocalTime.parse(times[0], formatter);
            LocalTime end = LocalTime.parse(times[1], formatter);

            if (isTimeInRange(currentTime, start, end)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断当前时间是否在时间范围内（支持跨午夜）
     */
    private static boolean isTimeInRange(LocalTime currentTime, LocalTime start, LocalTime end) {
        if (start.isBefore(end)) {
            // 普通时间范围，例如 10:00~12:00
            return !currentTime.isBefore(start) && !currentTime.isAfter(end);
        } else {
            // 跨午夜时间范围，例如 23:00~01:00
            return !currentTime.isBefore(start) || !currentTime.isAfter(end);
        }
    }

    /**
     * 将字符串时间范围解析为 TimeRange 对象列表
     */
    private static List<TimeRange> parseTimeRanges(List<String> ranges) {
        return ranges.stream()
                .map(TimeRange::new)
                .collect(Collectors.toList());
    }

    public List<String> getOld() {
        return old;
    }

    public void setOld(List<String> old) {
        this.old = old;
    }

    public List<String> getPerformance() {
        return performance;
    }

    public void setPerformance(List<String> performance) {
        this.performance = performance;
    }

    public String getSwitchNameFromTimeLine() {
        // 判断当前时间在哪个范围内
        String result = checkCurrentTimeInRange(this);
        log.info("通过当前时间判断是属于哪个阶段: " + result);
        return result;
    }

    /**
     * 时间范围类，用于封装时间范围的解析和判断逻辑
     */
    public static class TimeRange {
        private final LocalTime start;
        private final LocalTime end;

        public TimeRange(String range) {
            String[] times = range.split("~");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            this.start = LocalTime.parse(times[0], formatter);
            this.end = LocalTime.parse(times[1], formatter);
        }

        /**
         * 判断当前时间是否在时间范围内
         */
        public boolean contains(LocalTime currentTime) {
            if (start.isBefore(end)) {
                return !currentTime.isBefore(start) && !currentTime.isAfter(end);
            } else {
                // 处理跨午夜的时间范围
                return !currentTime.isBefore(start) || !currentTime.isAfter(end);
            }
        }
    }
}