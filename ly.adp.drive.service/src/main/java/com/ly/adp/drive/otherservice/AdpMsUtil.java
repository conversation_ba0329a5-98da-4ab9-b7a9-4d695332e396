package com.ly.adp.drive.otherservice;

import com.ly.adp.drive.otherservice.IXapiPushFeignService;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/23
 */
@Service
public class AdpMsUtil {

    @Autowired
    IXapiPushFeignService aipModelIBiz;

    static IXapiPushFeignService aipModelIBizStaic;

    @PostConstruct
    public void init() {
        aipModelIBizStaic = aipModelIBiz;
    }

    public static void sendMq(String ms, Object message) {

        aipModelIBizStaic.ms(ms, message);

    }

    public static void sendMqExp(String ms, Object message) {
        EntityResult<List<String>> result = aipModelIBizStaic.ms(ms, message);
        if (!"1".equals(result.getResult())) {
            String m = String.format("消息【%s】发送失败", ms);
            throw BusicenException.create(m);
        }
    }

    //发送写接口表
    public static OptResult sendApiData(String ms, Map<String, Object> mapParam) {
        OptResult result = aipModelIBizStaic.sendApiData(ms, mapParam);
        return result;
    }
}
