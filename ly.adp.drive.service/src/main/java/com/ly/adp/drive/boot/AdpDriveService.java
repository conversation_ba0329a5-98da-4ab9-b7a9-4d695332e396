package com.ly.adp.drive.boot;

import com.ly.adp.common.configuration.BusiUserByXUid;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.XADataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

import com.ly.adp.common.cloud.CloudInitializerEx;
import com.ly.adp.common.configuration.AdpConfiguration;
import com.ly.adp.common.configuration.SwaggerConfiguration;
import com.ly.adp.drive.config.ThreadPoolConfig;
import com.ly.mp.bucn.pack.ms.MsAdapterRedisMq;
import com.ly.mp.busi.base.config.BusicenMybatisPlusConfig;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BucnWebContextFilter;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.helper.SpringContextHolder;
import com.ly.mp.busicen.rule.config.XruleConfig;
import com.ly.mp.busicen.rule.config.XruleDataCfg;
import com.ly.mp.busicen.rule.field.FieldRuleConfig;
import com.ly.mp.busicen.rule.flow.FlowConfig;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterConfig;
import com.ly.mp.busicen.rule.instrumentation.FlowInstrumentationConfig;
import com.ly.mp.cache.data.RedisTemplateConfiguration;
import com.ly.mp.component.conf.AppConfig;
import com.ly.mp.component.conf.web.CommonInitializer;
import com.ly.mp.dal.comm.jdbc.DynamicDataSource;
import com.ly.mp.dal.comm.jdbc.PagedJdbcTemplate;
import com.szlanyou.common.redis.util.RedisExtUtil;
import com.szlanyou.common.redis.util.RedisUtil;

@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, XADataSourceAutoConfiguration.class,
		ActiveMQAutoConfiguration.class })
@Import({ CommonInitializer.class, AppConfig.class, CloudInitializerEx.class, SwaggerConfiguration.class,
		AdpConfiguration.class, FieldRuleConfig.class, FlowConfig.class, FlowFilterConfig.class,
		SpringContextHolder.class, RedisTemplateConfiguration.class, FlowInstrumentationConfig.class,
		BusicenMybatisPlusConfig.class,ThreadPoolConfig.class,MsAdapterRedisMq.class

})

@ComponentScan(basePackages = { "com.ly.adp.drive.**", "com.ly.mp.dal.comm.config","com.annotationSelf","com.ly.mp.busicen.common.helper",
		"com.ly.mp.dal.comm.mybatis","com.ly.mp.sys.org.**"})
@MapperScan({ "com.szlanyou.busicen.**.idal.mapper","com.ly.adp.**.idal.mapper"})
@EnableFeignClients({ "com.ly.adp.**.otherservice", "com.ly.mp.cloud.api.service", "com.ly.adp.common.configuration" })
public class AdpDriveService {

	public static void main(String[] args) {
		System.setProperty("service.name", "ly.adp.drive.AdpDriveService");
		wrapBusicenContext();
		SpringApplication.run(AdpDriveService.class, args);
		
	}

	@Bean
	public RedisUtil redisUtil() {
		return new RedisUtil();
	}

	@Bean
	public RedisExtUtil redisExtUtil() {
		return new RedisExtUtil();
	}

	@Bean
	public PagedJdbcTemplate PagedJdbcTemplate(DynamicDataSource dataSource) {
		return new PagedJdbcTemplate(dataSource);
	}
	@Bean
	public XruleConfig  xruleConfig() {
		XruleDataCfg xdc = XruleDataCfg.createEmpty()
					.dataRule("t_prc_db_datarule_sac")
			       .flowRule("t_prc_db_sence_datarule_sac")
			       .fieldRule("t_prc_db_sence_validatecolum_sac")
			       .messageRule("t_prc_db_log_model_sac")
			       .pkPublish("t_prc_msg_table_register_sac");  
		return XruleConfig.createBucn().xruleDataCfg(xdc);
	}
	
	@Bean
    public FilterRegistrationBean<BucnWebContextFilter> bucnWebContextFilterRegist() {
        FilterRegistrationBean<BucnWebContextFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        BucnWebContextFilter bucnContextFilter = new BucnWebContextFilter();
        filterRegistrationBean.setFilter(bucnContextFilter);
        filterRegistrationBean.addUrlPatterns("*.do");
        filterRegistrationBean.setName("bucnWebContextFilterRegist");
        filterRegistrationBean.setOrder(200);
        return filterRegistrationBean;
    }

	public static void wrapBusicenContext() {

		com.ly.mp.busi.base.context.BusicenContext.setDefaultT2u(AdpDriveService::u2u);

	}

	public static com.ly.mp.busi.base.constant.UserBusiEntity u2u(String token) {
		UserBusiEntity busiUser = BusicenContext.getCurrentUserBusiInfo(token);
//		UserEntity busiUser = (UserEntity) SessionHelper.get(token);
		
		com.ly.mp.busi.base.constant.UserBusiEntity baseUser = new com.ly.mp.busi.base.constant.UserBusiEntity();
		if (busiUser != null) {
			BeanUtils.copyProperties(busiUser,baseUser);
			baseUser.setStationId(busiUser.getStationId());
		} else {

		}

		return baseUser;

	}

	@Bean
	public BusiUserByXUid busiUserByXUid(){
		return new BusiUserByXUid();
	}
}
