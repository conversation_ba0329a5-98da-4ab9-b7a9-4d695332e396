package com.ly.adp.drive.service;

import java.util.Map;

import com.ly.mp.component.entities.OptResult;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.drive.entities.SacTestDriveLongApply;
import com.ly.adp.drive.entities.in.SacTestDriveLongApplyIn;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 超长试驾申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface ISacTestDriveLongApplyService extends IService<SacTestDriveLongApply> {
	
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacTestDriveLongApplyQueryFindAll(String token, SacTestDriveLongApplyIn dataInfo);

	/**
	 * 超长试驾申请查询导出
	 *
	 * @param info
	 * @return
	 */
	OptResult querylongapplyexport(SacTestDriveLongApplyIn dataInfo, String token, HttpServletResponse response);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacTestDriveLongApplySave(SacTestDriveLongApplyIn dataInfo, String token);
	
	ListResult<Map<String, Object>> uploadImage(MultipartFile uploadfiles, String token);
}
