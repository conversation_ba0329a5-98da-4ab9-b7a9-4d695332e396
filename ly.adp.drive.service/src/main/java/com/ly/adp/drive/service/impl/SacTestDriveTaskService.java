package com.ly.adp.drive.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ly.adp.common.util.BaseSpecialDateUtils;
import com.ly.adp.drive.entities.*;
import com.ly.adp.drive.entities.enums.SendWeComMsgTypeEnum;
import com.ly.adp.drive.helper.RestTemplateUtil;
import com.ly.adp.drive.idal.mapper.SacTestDriveTaskMapper;
import com.ly.adp.drive.otherservice.IBasedataFeignClient;
import com.ly.adp.drive.otherservice.IXapiPushFeignService;
import com.ly.adp.drive.otherservice.entities.MdsLookupValue;
import com.ly.adp.drive.otherservice.entities.in.MdsLookupValueIn;
import com.ly.adp.drive.service.ISacTestDriveTaskService;
import com.ly.adp.drive.util.QYApiWeixinUtil;
import com.ly.mp.busi.base.constant.UserBusiEntity;
import com.ly.mp.busi.base.context.BusicenContext;
import com.ly.mp.busi.base.handler.RedisLockUtil;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.excel.ExcelExportUtil;
import com.ly.mp.busicen.common.handler.ResultHandler;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 试驾任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Service
public class SacTestDriveTaskService extends ServiceImpl<SacTestDriveTaskMapper, SacTestDriveTask> implements ISacTestDriveTaskService {

    private final Logger log = LoggerFactory.getLogger(SacTestDriveTaskService.class);

    @Autowired
    SacTestDriveTaskMapper sacTestDriveTaskMapper;
    @Autowired
    IBasedataFeignClient basedataFeignClient;
    @Autowired
    RedisLockUtil redisLockUtil;
    @Autowired
    IXapiPushFeignService accPushFeignService;

    @Value("${weCom.driveTask.url}")
    private String driveTaskPageUrl;

    @Value("${weCom.msgType}")
    private  String msgType;

    @Autowired
    private QYApiWeixinUtil qyApiWeixinUtil;

    @Autowired
    private Executor asyncTaskExecutor;

    /**
     * 分页查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveTaskFindInfo(Map<String, Object> dateInfo, String token) {
        ListResult<Map<String, Object>> result;
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);

        // orgType == 1 (专营店)  orgType == 0 (总部)
        String orgType = userBusiEntity.getOrgType();
        if ("1".equals(orgType)) {
            List<String> list = new ArrayList<>();
            list.add(userBusiEntity.getDlrCode());
            String empID = userBusiEntity.getEmpID();
            String stationString = sacTestDriveTaskMapper.findStation(empID);
            if ("smart_bm_0016".equals(stationString) || "smart_bm_0005".equals(stationString)) {
                dateInfo.put("taskPersonId", null);
            }
            //0 门店 1 大使/大区，2 总部
            dateInfo.put("queryType", "0");
            dateInfo.put("orDlrCode", list);
        } else if ("0".equals(orgType)) {
            // 0 门店，1 大使/大区，2 总部
            dateInfo.put("queryType", "1");
            dateInfo.put("orDlrCode", _getUserDlrCodeList(token, userBusiEntity.getUserID()));
        }
        try {
            IPage<Map<String, Object>> page = new Page<>(Integer.parseInt(dateInfo.get("pageIndex").toString()), Integer.parseInt(dateInfo.get("pageSize").toString()));
            List<Map<String, Object>> list = sacTestDriveTaskMapper.querySacTestDriveTask(page, dateInfo);
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
            return result;
        } catch (Exception e) {
            log.error("sacTestDriveTaskFindInfo异常", e);
            //抛出RuntimeException，事务才会回滚
            throw new RuntimeException(e);
        }
    }

    /**
     * 分页查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveTaskFindListInfo(Map<String, Object> dateInfo, String token) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        // orgType == 1 (专营店)  orgType == 0 (总部)
        String orgType = userBusiEntity.getOrgType();
        if ("1".equals(orgType)) {
            List<String> list = new ArrayList<>();
            list.add(userBusiEntity.getDlrCode());
            // 0-门店 1-大使/大区 2-总部
            dateInfo.put("queryType", "0");
            dateInfo.put("orDlrCode", list);
        } else if ("0".equals(orgType)) {
            // 0-门店 1-大使/大区 2-总部
            dateInfo.put("queryType", "1");
            dateInfo.put("orDlrCode", _getUserDlrCodeList(token, userBusiEntity.getUserID()));
        }

        if ("3".equals(orgType)) {
            List<String> dlrCodeList = sacTestDriveTaskMapper.findAgentCompany(userBusiEntity.getOrgID());
            dateInfo.put("dlrCodeLists", dlrCodeList);
        }
        if ("2".equals(orgType)) {
            List<String> dlrCodeList = sacTestDriveTaskMapper.findCompany(userBusiEntity.getOrgCode());
            dateInfo.put("dlrCodeLists", dlrCodeList);
        }
        try {
            IPage<Map<String, Object>> page = new Page<>(Integer.parseInt(dateInfo.get("pageIndex").toString()), Integer.parseInt(dateInfo.get("pageSize").toString()));
            List<Map<String, Object>> list = sacTestDriveTaskMapper.sacTestDriveTaskFindListInfo(page, dateInfo);
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
            return result;
        } catch (Exception e) {
            log.error("sacTestDriveTaskFindInfo异常", e);
            //抛出RuntimeException，事务才会回滚
            throw new RuntimeException(e);
        }
    }

    @Override
    public OptResult sacTestDriveTaskExport(Map<String, Object> dataInfo, String token, HttpServletResponse response) {
        try {
            String title = "试驾任务列表导出";
            String[][] columns = new String[][]{
                    {"taskTitle", "渠道来源"},
                    {"clSource5", "cl_sp"},
                    {"clSource6", "cl_sr"},
                    {"custName", "客户姓名"},
                    {"genderName", "客户性别"},
                    {"phone", "客户电话"},
                    {"sendDlrName", "发起门店"},
                    {"salesConsultantName", "产品专家名称"},
                    {"smallCarTypeName", "预约试驾车型"},
                    {"appointmentTime", "预约试驾时间"},
                    {"createdDate", "任务创建时间"},
                    {"bussTime", "完成时间"},
                    {"taskPersonDlrName", "完成门店"},
                    {"taskPersonName", "任务完成人"},
                    {"stationName", "任务完成人岗位"},
                    {"taskStateName", "状态"}
            };
            dataInfo.put("pageIndex", 1);
            dataInfo.put("pageSize", -1);
            List<Map<String, Object>> transferRecordsList = this.sacTestDriveTaskFindListInfo(dataInfo, token).getRows();
            ExcelExportUtil.exportExcel(title, columns, transferRecordsList, response);
        } catch (Exception e) {
            //  e.printStackTrace();
            log.error("sacTestDriveTaskExport；错误信息{}", e);
            throw e;
        }
        return null;
    }

    @Override
    public OptResult sacTestDriveTaskAllExport(Map<String, Object> dataInfo, String token, HttpServletResponse response) {
        try {
            String title = "试驾任务列表导出";
            String[][] columns = new String[][]{
                    {"taskTitle", "渠道来源"},
                    {"clSource5", "cl_sp"},
                    {"clSource6", "cl_sr"},
                    {"custName", "客户姓名"},
                    {"genderName", "客户性别"},
                    {"phone", "客户电话"},
                    {"sendDlrName", "发起门店"},
                    {"salesConsultantName", "产品专家名称"},
                    {"smallCarTypeName", "预约试驾车型"},
                    {"appointmentTime", "预约试驾时间"},
                    {"createdDate", "任务创建时间"},
                    {"bussTime", "完成时间"},
                    {"taskPersonDlrName", "完成门店"},
                    {"taskPersonName", "任务完成人"},
                    {"stationName", "任务完成人岗位"},
                    {"taskStateName", "状态"}
            };
            dataInfo.put("pageIndex", 1);
            dataInfo.put("pageSize", -1);
            List<Map<String, Object>> transferRecordsList = this.sacTestDriveTaskFindAllInfo(dataInfo, token).getRows();
            ExcelExportUtil.exportExcel(title, columns, transferRecordsList, response);
        } catch (Exception e) {
            //  e.printStackTrace();
            log.error("sacTestDriveTaskExport；错误信息{}", e);
            throw e;
        }
        return null;
    }

    @Override
    public EntityResult<Map<String, Object>> sacTestDriveTaskFindCount(Map<String, Object> dataInfo, String token) {

        int taskCount = sacTestDriveTaskMapper.sacTestDriveTaskFindCount(dataInfo);
        dataInfo.put("taskCount", taskCount);
        return ResultHandler.updateOk(dataInfo);

    }

    /**
     * 根据主键判断插入或更新
     *
     * @param dataInfo
     * @param token
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EntityResult<Map<String, Object>> sacTestDriveTaskSaveInfo(Map<String, Object> dataInfo, String token) {

        log.info("开始进入" + dataInfo);

        if (StringHelper.IsEmptyOrNull(dataInfo.get("id"))) {
            this.checkSms(dataInfo);
        }
        Map<String, Object> taskMap = new HashMap<>(10);
        if (!StringHelper.IsEmptyOrNull(dataInfo.get("label"))) {
            // 店长分配试驾任务给到产品专家
            List<Map<String, Object>> empInfoList = assembleData(dataInfo);
            Map<String, Object> empInfoMap = empInfoList.get(0);
            taskMap.put("taskStateCode", "0");
            taskMap.put("taskStateName", "未完成");
            taskMap.put("msgTestType", "1");
            taskMap.put("id", dataInfo.get("id"));
            taskMap.put("salesConsultantName", dataInfo.get("salesConsultantName"));
            taskMap.put("taskPersonId", empInfoMap.get("userId"));
            taskMap.put("taskPersonCode", empInfoMap.get("userName"));
            taskMap.put("taskPersonName", empInfoMap.get("empName"));
            taskMap.put("taskPersonDlrCode", empInfoMap.get("dlrCode"));
            taskMap.put("taskPersonDlrName", empInfoMap.get("dlrShortName"));
            log.info("开始更新试驾任务表" + taskMap);
            int sacTestDriveTaskI = sacTestDriveTaskMapper.updateSacTestDriveTask(taskMap);
            if (sacTestDriveTaskI < 1) {
                throw BusicenException.create("试驾任务维护失败！");
            }
            log.info("试驾任务id:{},发送企微提醒", taskMap.get("id"));
            sendWeCom(taskMap, SendMsgToWeComConstant.SEND_DRIVE_TASK_MSG);
            return ResultHandler.updateOk(taskMap);
        }

        if (!StringHelper.IsEmptyOrNull(dataInfo.get("smallCartypeName"))) {
            dataInfo.put("smallCarTypeName", dataInfo.get("smallCartypeName"));
        }
        if (StringHelper.IsEmptyOrNull(dataInfo.get("id"))) {
            // APP 下发新建试驾任务，如果线索是本店线索分配给产品专家，非本店线索分配给试驾门店店长
            Object smallCarTypeName = dataInfo.get("smallCarTypeName");
            if (StringHelper.IsEmptyOrNull(dataInfo.get("smallCarTypeName"))) {
                smallCarTypeName = "HX11";
            }
            String redisLock = smallCarTypeName.toString() + dataInfo.get("phone").toString();
            // try {
            List<Map<String, Object>> empInfoList = assembleData(dataInfo);

            //根据手机号和门店判断线索表有没有线索
            Map<String, Object> map_clue = sacTestDriveTaskMapper.searchClueByPhoneAndDlr(dataInfo);

            if (redisLockUtil.lock(redisLock, redisLock, 10)) {

                taskMap.putAll(dataInfo);
                Map<String, Object> empInfoMap = empInfoList.get(0);
                taskMap.put("taskStateCode", "0");
                taskMap.put("taskStateName", "未完成");
                boolean isCurrentDlrClue = !CollectionUtils.isEmpty(map_clue);
                if (isCurrentDlrClue) {
                    taskMap.put("taskPersonId", map_clue.get("userId"));
                    taskMap.put("taskPersonCode", map_clue.get("userName"));
                    taskMap.put("taskPersonName", map_clue.get("empName"));
                } else {
                    taskMap.put("taskPersonId", empInfoMap.get("userId"));
                    taskMap.put("taskPersonCode", empInfoMap.get("userName"));
                    taskMap.put("taskPersonName", empInfoMap.get("empName"));
                }
                taskMap.put("taskPersonDlrCode", empInfoMap.get("dlrCode"));
                taskMap.put("taskPersonDlrName", empInfoMap.get("dlrShortName"));
                taskMap.put("creator", empInfoMap.get("userId"));
                taskMap.put("modifier", empInfoMap.get("userId"));
                taskMap.put("createdName", empInfoMap.get("empName"));
                taskMap.put("modifyName", empInfoMap.get("empName"));
                taskMap.put("id", UUID.randomUUID().toString());
                int sacTestDriveTaskI = sacTestDriveTaskMapper.createSacTestDriveTask(taskMap);
                if (sacTestDriveTaskI < 1) {
                    throw BusicenException.create("试驾任务维护失败！");
                }

                //   this.sendCsp(dataInfo);
                String dateTz = BaseSpecialDateUtils.sysDateTZ();
                // 试驾发送CDP
                int insert = sacTestDriveTaskMapper.insertCspLeadsEvent(dataInfo, dateTz);
                if (insert < 1) {
                    throw BusicenException.create("写入CDP接口失败！");
                }
                sacTestDriveTaskMapper.insertCspLeads(dataInfo, dateTz);
                // 发送企微消息
                log.info("试驾任务id:{},发送企微提醒", taskMap.get("id"));
                sendWeCom(taskMap, isCurrentDlrClue ? SendMsgToWeComConstant.SEND_DRIVE_TASK_MSG : SendMsgToWeComConstant.SEND_DRIVE_SM_TASK_MSG);
            }
            //       }
//            } finally {
//                redisLockUtil.unlock(redisLock, redisLock);
//            }
        } else {
            taskMap.putAll(dataInfo);
            taskMap.put("taskStateCode", "1");
            taskMap.put("taskStateName", "已完成");
            taskMap.put("bussTime", BaseSpecialDateUtils.sysDate());
            BusicenUtils.invokeUserInfo(taskMap, BusicenUtils.SOU.Save, token);
            int sacTestDriveTaskI = sacTestDriveTaskMapper.updateSacTestDriveTask(taskMap);
            if (sacTestDriveTaskI < 1) {
                throw BusicenException.create("试驾任务维护失败！");
            }
        }
        return ResultHandler.updateOk(taskMap);

    }

    /**
     * 发送企微消息
     * @param taskMap
     * @param noticeMsg 提示内容
     */
    private void sendWeCom(Map<String, Object> taskMap, String noticeMsg) {
        // 异步发送企微
        CompletableFuture.runAsync(() -> {
            try {
                qyApiWeixinUtil.send(getSendWeComParam(String.valueOf(taskMap.get("taskPersonId")), noticeMsg));
            } catch (Exception e) {
                log.error("试驾任务发送企微消息异常", e);
            }
        }, asyncTaskExecutor);
    }

    /**
     * 通过跟进人id获取发送企微消息参数
     *
     * @param reviewPersonId 跟进人id
     * @param noticeMsg 提示内容
     * @return
     */
    private Map<String, Object> getSendWeComParam(String reviewPersonId, String noticeMsg) {
        WeComTextCardRequest sendWeComReq = new WeComTextCardRequest();
        sendWeComReq.setToUser(qyApiWeixinUtil.getWeComUserID(reviewPersonId));
        SendWeComMsgTypeEnum msgTypeEnum = SendWeComMsgTypeEnum.fromCode(msgType);
        sendWeComReq.setMsgType(msgTypeEnum.getCode());
        switch(msgTypeEnum) {
            case textcard:
                sendWeComReq.setTextCard(new WeComTextCardRequest.TextCard(noticeMsg, driveTaskPageUrl));
                return sendWeComReq.sendTextCardMsgToMap();
            case text:
                sendWeComReq.setText(new WeComTextCardRequest.Text(noticeMsg));
                return sendWeComReq.sendTextMsgToMap();
            default:
                // 默认文本消息
                return sendWeComReq.sendTextMsgToMap();
        }
    }

    /**
     * 获取token
     * @param userName
     */
    private String getToken(String userName) {
        Map<String, Object> dataInfo = new HashMap<>();
        dataInfo.put("userName", userName);
        OptResult result = basedataFeignClient.createMpTokenInfo(dataInfo);
        if (!"1".equals(result.getResult())) {
            throw new BusicenException("token获取失败：" + result.getMsg());
        }
        return result.getMsg();
    }

    private void checkSms(Map<String, Object> dataInfo) {
        Object taskTitle = dataInfo.get("taskTitle");
        if (StringUtils.isEmpty(taskTitle)) {
            throw new BusicenException("无效渠道");
        }
        List<LookupValue> lookupValue = sacTestDriveTaskMapper.findLookUpValue("VE1036");
        Map<String, HashMap<String, String>> collect = lookupValue.stream().collect(Collectors.toMap(LookupValue::getLookupValueName, r -> {
            HashMap<String, String> hashMap = Maps.newHashMap();
            hashMap.put(r.getLookupValueName(), r.getLookupValueCode());
            hashMap.put(r.getLookupValueCode(), r.getIsEnable());
            return hashMap;
        }, (k1, k2) -> k1));
        HashMap<String, String> hashMap = collect.get(taskTitle);

        if (CollectionUtils.isEmpty(hashMap)) {
            return;
        }
        String xchannelid = hashMap.get(taskTitle);
        if (!"1".equals(hashMap.get(xchannelid))) {
            return;
        }
        Object smsCode = dataInfo.get("smsCode");
        Object smartId = dataInfo.get("smartId");
        if (StringHelper.IsEmptyOrNull(smartId) && StringHelper.IsEmptyOrNull(smsCode)) {
            throw new BusicenException("认证失败,缺失认证信息");
        }
        // 短信认证
        if (!StringHelper.IsEmptyOrNull(smsCode)) {
            if ("官网试乘试驾预约".equals(taskTitle)) {
                ResponseEntity<String> response = iamCheck(dataInfo, smsCode);
                log.info("请求IAM短信认证接口返回{}", response);
                IamRsp rsp = JSONObject.parseObject(response.getBody(), IamRsp.class);
                if (!"success".equals(rsp.getMessage()) && !"200".equals(rsp.getCode())) {
                    throw new BusicenException("IAM短信认证未通过");
                }
                return;
            }
            ResponseEntity<String> response = smsCodeCheck(dataInfo, xchannelid, smsCode);
            log.info("请求短信认证接口返回{}", response);

            SmsRsp rsp = JSONObject.parseObject(response.getBody(), SmsRsp.class);
            if (!"success".equals(rsp.getCode())) {
                throw new BusicenException(rsp.getMessage());
            }
            return;
        }

        ResponseEntity<String> response = smartIdCheck(dataInfo, smartId);

        SmartIdRsp rsp = JSONObject.parseObject(response.getBody(), SmartIdRsp.class);

        if (!"success".equals(rsp.getCode()) && !"true".equals(rsp.getData())) {
            throw new BusicenException("smartId认证未通过");
        }


    }

    private ResponseEntity<String> smartIdCheck(Map<String, Object> dataInfo, Object smartId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        HashMap<String, Object> smartIdMap = Maps.newHashMap();
        smartIdMap.put("phone", dataInfo.get("phone"));
        smartIdMap.put("smartId", smartId);
        String url = sacTestDriveTaskMapper.findSmartUrl();
        ResponseEntity<String> response;
        HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(smartIdMap), headers);
        try {
            response = RestTemplateUtil.getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            log.info("请求smartId认证接口失败{}", e);
            throw new BusicenException("请求smartId认证失败");
        }
        log.info("请求smartId认证接口返回{}", response);
        return response;
    }

    private ResponseEntity<String> smsCodeCheck(Map<String, Object> dataInfo, String xchannelid, Object smsCode) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        headers.set("X-Channel-Id", xchannelid);
        headers.set("Accept-Language", "zh");
        headers.set("X-Sign-UUID", UUID.randomUUID().toString());
        headers.set("X-Sign-Timestamp", String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        HashMap<String, Object> phoneCodeMap = Maps.newHashMap();
        phoneCodeMap.put("mobilephone", dataInfo.get("phone"));
        phoneCodeMap.put("sms_code", smsCode);
        HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(phoneCodeMap), headers);
        String url = sacTestDriveTaskMapper.findSmsUrl();
        ResponseEntity<String> response;
        try {
            response = RestTemplateUtil.getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            log.info("请求短信认证接口失败{}", e);
            throw new BusicenException("请求短信认证失败");
        }
        return response;
    }

    private ResponseEntity<String> iamCheck(Map<String, Object> dataInfo, Object smsCode) {
        List<LookupValue> lookupValue = sacTestDriveTaskMapper.findLookUpValue("VE1039");
        Map<String, String> collect = lookupValue.stream().collect(Collectors
                .toMap(LookupValue::getLookupValueCode, LookupValue::getLookupValueName, (k1, k2) -> k2));
        HttpHeaders headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        HashMap<String, Object> phoneCodeMap = Maps.newHashMap();
        phoneCodeMap.put("mobile", dataInfo.get("phone"));
        phoneCodeMap.put("smsCode", smsCode);
        //   phoneCodeMap.put("client_id", collect.get("client_id"));
        //  phoneCodeMap.put("client_secret", collect.get("client_secret"));
        HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(phoneCodeMap), headers);
        log.info("请求IAM短信认证参数{}", entity);
        ResponseEntity<String> response;
        try {
            response = RestTemplateUtil.getRestTemplate().exchange(collect.get("url"), HttpMethod.POST, entity, String.class);
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            log.info("请求IAM短信认证接口失败{}", e);
            throw new BusicenException("请求IAM短信认证失败");
        }
        return response;
    }

    private void sendCsp(Map<String, Object> dataInfo) {
        HashMap<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("event", "c_bookdrive");
        // 试驾时间段
        paramMap.put("c_time_period", dataInfo.get("appointmentTestTime"));
        // 试驾日期
        paramMap.put("c_book_date", dataInfo.get("appointmentTestDate"));
        // 试驾车型
        paramMap.put("c_car_model", dataInfo.get("smallCarTypeName"));
        // 试驾类型
        paramMap.put("c_driver_type", dataInfo.get("testType"));
        paramMap.put("bk", dataInfo.get("phone"));
        paramMap.put("date", BaseSpecialDateUtils.sysDateTZ());

        paramMap.put("logs_id", StringHelper.GetGUID());
        paramMap.put("configCode", "ADP_INSERT_CDP_LEADS_EVENT");
        Map<String, Object> param = new HashMap<>(1);
        param.put("mapParam", paramMap);
        accPushFeignService.sendCancleData(paramMap.get("configCode").toString(), param);
    }

    private List<Map<String, Object>> assembleData(Map<String, Object> dataInfo) {
        log.info("进入判断接口" + dataInfo);
        Assert.hasLength((String) dataInfo.get("phone"), "手机号不能为空!");
        Assert.hasLength((String) dataInfo.get("custName"), "客户名称不能为空!");
        Assert.hasLength((String) dataInfo.get("taskTitle"), "任务标题不能为空!!");
        Assert.hasLength((String) dataInfo.get("taskPersonDlrCode"), "完成人员专营店编码不能为空!!");
        Assert.hasLength((String) dataInfo.get("taskPersonDlrName"), "完成人员专营店名称不能为空!!");
        //  int i = sacTestDriveTaskMapper.querySacTestDriveCt(dataInfo);
        List<String> smallCarType = sacTestDriveTaskMapper.findSmallCarType(dataInfo);
        if (smallCarType.contains(dataInfo.get("smallCarTypeName"))) {
            throw BusicenException.create("该手机号已预约该车型,请确认");
        }
        Map<String, Object> empInfoMap = new HashMap<>();
        if (!StringHelper.IsEmptyOrNull(dataInfo.get("taskPersonId"))) {
            empInfoMap.put("userId", dataInfo.get("taskPersonId"));
        } else {
            empInfoMap.put("stationIdIn", this.lookupValueCodeSpell("ADP_CLUE_045"));
            empInfoMap.put("dlrCode", dataInfo.get("taskPersonDlrCode"));
        }

        IPage<Map<String, Object>> page = new Page<>(1, 10);
        List<Map<String, Object>> empInfoList = sacTestDriveTaskMapper.locationEmpInfo(page, empInfoMap);
        if (empInfoList.size() < 1 || StringHelper.IsEmptyOrNull(empInfoList.get(0).get("userId"))) {
            throw BusicenException.create("获取userid失败或该店没有店长，请联系管理员!");
        }
        log.info("判断接口结束");
        return empInfoList;
    }

    /**
     * 总部-分页查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveTaskFindAllInfo(Map<String, Object> dateInfo, String token) {
        ListResult<Map<String, Object>> result;
        try {
            IPage<Map<String, Object>> page = new Page<>(Integer.parseInt(dateInfo.get("pageIndex").toString()), Integer.parseInt(dateInfo.get("pageSize").toString()));
            List<Map<String, Object>> list = sacTestDriveTaskMapper.sacTestDriveTaskFindListInfo(page, dateInfo);
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
            return result;
        } catch (Exception e) {
            log.error("sacTestDriveTaskFindAllInfo", e);
            //抛出RuntimeException，事务才会回滚
            throw new RuntimeException(e);
        }
    }

    public String lookupValueCodeSpell(String lookupTypeCode) {
        MdsLookupValueIn valueEntitIn = new MdsLookupValueIn();
        valueEntitIn.setPageIndex(1);
        valueEntitIn.setPageSize(Integer.MAX_VALUE);
        valueEntitIn.setLookupTypeCode(lookupTypeCode);
        ListResult<MdsLookupValue> valueListResult = basedataFeignClient.mdslookupvaluefindbypageNotDo(valueEntitIn);
        StringBuffer stringBuffer = new StringBuffer();
        for (MdsLookupValue lookupValue : valueListResult.getRows()) {
            stringBuffer.append(lookupValue.getLookupValueName()).append(",");
        }
        return stringBuffer.toString();
    }

    /**
     * 根据userId获取负责的门店编码
     *
     * @param authentication
     * @param userId
     * @return
     */
    private List<String> _getUserDlrCodeList(String authentication, String userId) {
        List<String> dlrCodeList = new ArrayList<>();
        HashMap<String, Object> querymanagedlrMap = new HashMap<>();
        querymanagedlrMap.put("userId", userId);
        querymanagedlrMap.put("pageIndex", -1);
        querymanagedlrMap.put("pageSize", -1);
        ListResult<Map<String, Object>> dlrListResult = basedataFeignClient.findManagedDlrList(querymanagedlrMap);
        if ("1".equals(dlrListResult.getResult())) {
            List<Map<String, Object>> rows = dlrListResult.getRows();
            for (Map<String, Object> map : rows) {
                dlrCodeList.add((String) map.get("dlrCode"));
            }
        }
        if (dlrCodeList.isEmpty()) {
            dlrCodeList.add("-1");
        }
        return dlrCodeList;
    }
}
