package com.ly.adp.drive.entities.in;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;

public class SacTestDriveLongApplyIn extends PageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "申请ID", required = false, example = "")
    private String applyId;

    @ApiModelProperty(value = "创建人", required = false, example = "")
    private String creator;

    @ApiModelProperty(value = "创建人名称", required = false, example = "")
    private String createdName;

    @ApiModelProperty(value = "审核状态", required = false, example = "")
    private String auditStatus;

    @ApiModelProperty(value = "审核状态名称", required = false, example = "")
    private String auditStatusName;

    @ApiModelProperty(value = "车牌号", required = false, example = "")
    private String carLicenceNo;

    @ApiModelProperty(value = "VIN码", required = false, example = "")
    private String vin;

    @ApiModelProperty(value = "车型编码", required = false, example = "")
    private String carTypeCode;

    @ApiModelProperty(value = "车型名称", required = false, example = "")
    private String carTypeName;

    @ApiModelProperty(value = "配置编码", required = false, example = "")
    private String cartypeConfigCode;

    @ApiModelProperty(value = "配置名称", required = false, example = "")
    private String cartypeConfigName;

    @ApiModelProperty(value = "颜色编码", required = false, example = "")
    private String carColorCode;

    @ApiModelProperty(value = "颜色名称", required = false, example = "")
    private String carColorName;

    @ApiModelProperty(value = "内饰编码", required = false, example = "")
    private String carIncolorCode;

    @ApiModelProperty(value = "内饰名称", required = false, example = "")
    private String carIncolorName;

    @ApiModelProperty(value = "可试驾天数", required = false, example = "")
    private String canTestDate;

    @ApiModelProperty(value = "申请门店", required = false, example = "")
    private String applyDlrCode;

    @ApiModelProperty(value = "申请原因", required = false, example = "")
    private String applyReason;

    @ApiModelProperty(value = "申请开始时间", required = false, example = "")
    private String applyTimeBegin;

    @ApiModelProperty(value = "申请结束时间", required = false, example = "")
    private String applyTimeEnd;

    @ApiModelProperty(value = "申请时长", required = false, example = "")
    private String applyTimeLong;

    @ApiModelProperty(value = "审批类型", required = false, example = "")
    private String auditType;

    @ApiModelProperty(value = "试驾车状态编码", required = false, example = "")
    private String carStatusCode;

    @ApiModelProperty(value = "试驾车状态名称", required = false, example = "")
    private String carStatusName;

    @ApiModelProperty(value = "批量审核申请ID参数", required = false, example = "")
    private List<Map<String, Object>> applyListMap;

    public List<Map<String, Object>> getApplyListMap() {
        return applyListMap;
    }

    public void setApplyListMap(List<Map<String, Object>> applyListMap) {
        this.applyListMap = applyListMap;
    }

    public String getCarLicenceNo() {
        return carLicenceNo;
    }

    public void setCarLicenceNo(String carLicenceNo) {
        this.carLicenceNo = carLicenceNo;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getCarTypeCode() {
        return carTypeCode;
    }

    public void setCarTypeCode(String carTypeCode) {
        this.carTypeCode = carTypeCode;
    }

    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    public String getCartypeConfigCode() {
        return cartypeConfigCode;
    }

    public void setCartypeConfigCode(String cartypeConfigCode) {
        this.cartypeConfigCode = cartypeConfigCode;
    }

    public String getCartypeConfigName() {
        return cartypeConfigName;
    }

    public void setCartypeConfigName(String cartypeConfigName) {
        this.cartypeConfigName = cartypeConfigName;
    }

    public String getCarColorCode() {
        return carColorCode;
    }

    public void setCarColorCode(String carColorCode) {
        this.carColorCode = carColorCode;
    }

    public String getCarColorName() {
        return carColorName;
    }

    public void setCarColorName(String carColorName) {
        this.carColorName = carColorName;
    }

    public String getCarIncolorCode() {
        return carIncolorCode;
    }

    public void setCarIncolorCode(String carIncolorCode) {
        this.carIncolorCode = carIncolorCode;
    }

    public String getCarIncolorName() {
        return carIncolorName;
    }

    public void setCarIncolorName(String carIncolorName) {
        this.carIncolorName = carIncolorName;
    }

    public String getCanTestDate() {
        return canTestDate;
    }

    public void setCanTestDate(String canTestDate) {
        this.canTestDate = canTestDate;
    }

    public String getApplyDlrCode() {
        return applyDlrCode;
    }

    public void setApplyDlrCode(String applyDlrCode) {
        this.applyDlrCode = applyDlrCode;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getApplyTimeBegin() {
        return applyTimeBegin;
    }

    public void setApplyTimeBegin(String applyTimeBegin) {
        this.applyTimeBegin = applyTimeBegin;
    }

    public String getApplyTimeEnd() {
        return applyTimeEnd;
    }

    public void setApplyTimeEnd(String applyTimeEnd) {
        this.applyTimeEnd = applyTimeEnd;
    }

    public String getAuditType() {
        return auditType;
    }

    public void setAuditType(String auditType) {
        this.auditType = auditType;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatusName() {
        return auditStatusName;
    }

    public void setAuditStatusName(String auditStatusName) {
        this.auditStatusName = auditStatusName;
    }

    public String getApplyId() {
        return applyId;
    }

    public String getApplyTimeLong() {
        return applyTimeLong;
    }

    public void setApplyTimeLong(String applyTimeLong) {
        this.applyTimeLong = applyTimeLong;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCarStatusCode() {
        return carStatusCode;
    }

    public void setCarStatusCode(String carStatusCode) {
        this.carStatusCode = carStatusCode;
    }

    public String getCarStatusName() {
        return carStatusName;
    }

    public void setCarStatusName(String carStatusName) {
        this.carStatusName = carStatusName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
}
