package com.ly.adp.drive.otherservice;

import com.ly.adp.drive.otherservice.entities.BuTestcarPrepare;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.component.entities.EntityResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/9/6
 */
@Service
public class CscSysBaseService implements ICscSysBaseService{

    @Autowired
    IBasedataFeignClient basedataFeignClient;
    @Override
    public EntityResult<Map<String, Object>> inset(String authentication, ParamPage<BuTestcarPrepare> param) {
        //转换参数类型
        ParamPage<BuTestcarPrepare> newParam = new ParamPage<>();
        newParam.setPageIndex(param.getPageIndex());
        newParam.setPageSize(param.getPageSize());
        //BuTestcarPrepare prepare = new BuTestcarPrepare();
    //    BeanUtils.copyProperties(param.getParam(), prepare);
        //BuTestcarPrepare buTestcarPrepare=BusicenUtils.map2Object(param.getParam(), BuTestcarPrepare.class);
        newParam.setParam(param.getParam());
        EntityResult<BuTestcarPrepare> result = basedataFeignClient.inset(authentication, newParam);
        //转换结果类型
        EntityResult<Map<String, Object>> newResult = new EntityResult<>();
        newResult.setMsg(result.getMsg());
        newResult.setExtInfo(result.getExtInfo());
        newResult.setResult(result.getResult());
        newResult.setRows(BusicenUtils.entityToMap(result.getRows()));
        return newResult;
    }
}
