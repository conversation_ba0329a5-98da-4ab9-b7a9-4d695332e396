package com.ly.adp.drive.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.drive.entities.SacTestDriveSheet;
import com.ly.adp.drive.entities.in.ProductSpecialistDTO;
import com.ly.adp.drive.entities.in.TestDriveTransferDTO;
import com.ly.adp.drive.entities.out.ProductSpecialistVO;
import com.ly.adp.drive.entities.req.GetBiVehicleDataJobReq;
import com.ly.adp.drive.entities.vo.GetBiVehicleDataResultVO;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 * 试乘试驾单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface ISacTestDriveSheetService extends IService<SacTestDriveSheet> {

    /**
     * 试乘试驾单查询
     *
     * @param mapParam
     * @return
     */
    ListResult<Map<String, Object>> sacTestDriveSheetQueryList(ParamPage<Map<String, Object>> mapParam);
    /**
     * 试乘试驾单查询，性能优化
     *
     * @param mapParam
     * @return
     */
    ListResult<Map<String, Object>> sacTestDriveSheetQueryList_performance(ParamPage<Map<String, Object>> mapParam);
    /**
     * 个人试乘试驾单查询
     *
     * @param mapParam
     * @return
     */
    ListResult<Map<String, Object>> sacTestDriveSheetSingleList(ParamPage<Map<String, Object>> mapParam);

    /**
     * PC门店全部试驾单查询导出
     *
     * @param dataInfo
     * @param token
     * @param response
     * @return
     */
    OptResult sacTestDriveSheetSingleListExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

    /**
     * 个人试乘试驾单查询
     *
     * @param mapParam
     * @return
     */
    ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp(ParamPage<Map<String, Object>> mapParam);

    /**
     * 个人试乘试驾单查询多线程
     *
     * @param mapParam
     * @return
     */
    ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp_p(ParamPage<Map<String, Object>> mapParam) ;

    EntityResult<Map<String, Object>> sacTestDriveSheetQueryDetail(Map<String, Object> mapParam);

    /**
     * 试乘试驾单开始/结束
     *
     * @param mapParam
     * @return
     */
    OptResult sacTestDriveSheetStatus(Map<String, Object> mapParam);

    /**
     * 试乘试驾单保存
     *
     * @param mapParam
     * @return
     */
    OptResult sacTestDriveSheetSave(Map<String, Object> mapParam);
    /**
     * 试乘试驾单保存，性能优化
     *
     * @param mapParam
     * @return
     */
    OptResult sacTestDriveSheetSave_performance(Map<String, Object> mapParam);

    /**
     * 生成单号
     * @param mapParam
     */
    void setOrderCode(Map<String, Object> mapParam);
    /**
     * 试乘试驾单保存(包含预约单)
     *
     * @param mapParam
     * @return
     */
    EntityResult<Map<String, Object>> sacTestDriveSheetAllSave(Map<String, Object> mapParam);
    /**
     * 试乘试驾单保存(包含预约单)
     *
     * @param mapParam
     * @return
     */
    EntityResult<Map<String, Object>> sacTestDriveSheetAllSave_performance(Map<String, Object> mapParam);

    /**
     * 试乘试驾协议保存
     *
     * @param mapParam
     * @return
     */
    OptResult sacTestDriveAgreementSave(Map<String, Object> mapParam);

    Result sactestdriveImport(String authentication, MultipartFile uploadfile);

    OptResult sactestdrivedelete(Map<String, Object> mapParam);

    OptResult sacTestDriveSendMessage(List<Map<String, Object>> param);

    OptResult sacTestDriveReplenishExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

    ListResult<ProductSpecialistVO> findProductSpecialist(ProductSpecialistDTO productSpecialist);

    OptResult testDriveTransfer(TestDriveTransferDTO testDriveTransferDTO);

    EntityResult<VehicleDataVO> queryVehicleData(String testDriveOrderNo);

    ListResult<GetBiVehicleDataResultVO> getBiVehicleData(GetBiVehicleDataJobReq req, String token);

    /**
     * 试驾开始、结束 加分布式锁
     * @param param
     * @return
     */
    OptResult sacTestDriveSheetStatusRedisLock(Map<String, Object> param);

    /**
     * 根据条件和字段查询试驾表
     * @param entity
     * @param selectField
     * @return
     */
    List<SacTestDriveSheet> queryTestDrivSheet(SacTestDriveSheet entity, SFunction<SacTestDriveSheet, Object>... selectField);
}
