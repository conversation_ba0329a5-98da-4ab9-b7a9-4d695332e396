package com.ly.adp.drive.entities.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("试驾签到入参")
public class TestDriveCheckInReq implements Serializable {

    private static final long serialVersionUID = 4207006312873703488L;

    @ApiModelProperty(value = "试驾单号")
    private String testDriveOrderNo;

    @ApiModelProperty(value = "顾客身份证附件")
    private String customerIdNumberAgreement;

    @ApiModelProperty(value = "试驾协议附件")
    private String testDriveAgreement;

    @ApiModelProperty(value = "驾驶证")
    private String drivingLicencePhoto;

    @ApiModelProperty(value = "其他附件")
    private String otherAgreement;

    @ApiModelProperty(value = "顾客签名附件")
    private String customerSignatureAgreement;

    @ApiModelProperty(value = "试驾类型 0：试乘，1：试驾，2：深度试驾")
    private String testType;
    @ApiModelProperty(value = "OCR识别身份证号")
    @JsonProperty("IDNumber")
    private String IDNumber;
    @ApiModelProperty(value = "OCR识别姓名")
    private String realName;
    @ApiModelProperty(value = "身份证加密", hidden = true)
    private String IDNumberEncryption;


    public TestDriveCheckInReq() {
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public String getCustomerIdNumberAgreement() {
        return customerIdNumberAgreement;
    }

    public void setCustomerIdNumberAgreement(String customerIdNumberAgreement) {
        this.customerIdNumberAgreement = customerIdNumberAgreement;
    }

    public String getTestDriveAgreement() {
        return testDriveAgreement;
    }

    public void setTestDriveAgreement(String testDriveAgreement) {
        this.testDriveAgreement = testDriveAgreement;
    }

    public String getOtherAgreement() {
        return otherAgreement;
    }

    public void setOtherAgreement(String otherAgreement) {
        this.otherAgreement = otherAgreement;
    }

    public String getCustomerSignatureAgreement() {
        return customerSignatureAgreement;
    }

    public void setCustomerSignatureAgreement(String customerSignatureAgreement) {
        this.customerSignatureAgreement = customerSignatureAgreement;
    }

    public String getDrivingLicencePhoto() {
        return drivingLicencePhoto;
    }

    public void setDrivingLicencePhoto(String drivingLicencePhoto) {
        this.drivingLicencePhoto = drivingLicencePhoto;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public String getIDNumber() {
        return IDNumber;
    }

    public void setIDNumber(String IDNumber) {
        this.IDNumber = IDNumber;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIDNumberEncryption() {
        return IDNumberEncryption;
    }

    public void setIDNumberEncryption(String IDNumberEncryption) {
        this.IDNumberEncryption = IDNumberEncryption;
    }

    @Override
    public String toString() {
        return "TestDriveCheckInReq{" +
                "testDriveOrderNo='" + testDriveOrderNo + '\'' +
                ", customerIdNumberAgreement='" + customerIdNumberAgreement + '\'' +
                ", testDriveAgreement='" + testDriveAgreement + '\'' +
                ", drivingLicencePhoto='" + drivingLicencePhoto + '\'' +
                ", otherAgreement='" + otherAgreement + '\'' +
                ", customerSignatureAgreement='" + customerSignatureAgreement + '\'' +
                ", testType='" + testType + '\'' +
                '}';
    }
}
