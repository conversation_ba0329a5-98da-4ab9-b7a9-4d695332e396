package com.ly.adp.drive.idal.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.adp.drive.entities.SacTestDriveLongApply;

/**
 * <p>
 * 超长试驾申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
public interface SacTestDriveLongApplyMapper extends BaseMapper<SacTestDriveLongApply> {
	
	/**
	 * 超长试驾申请查询
	 * @param page
	 * @param paramMap
	 * @return
	 */
	List <Map<String, Object>> sacTestDriveLongApplyFindAll(IPage<Map<String, Object>> page, @Param("param")Map <String, Object> paramMap);
	
	/**
	 * 超长试驾申请维护
	 * @param paramMap
	 * @return
	 */
    int sacTestDriveLongApplySaveOne(@Param("param") Map <String, Object> paramMap);
    
	/**
	 * 根据申请结束时间查询某个VIN对应的超长试驾申请单
	 * @param page
	 * @param paramMap
	 * @return
	 */
	List <Map<String, Object>> sacTestDriveLongApplyFindByApplyTime(@Param("param")Map <String, Object> paramMap);
	
	/**
	 * 根据申请结束时间查询某个VIN对应的超长试驾申请单
	 * @param page
	 * @param paramMap
	 * @return
	 */
	List <Map<String, Object>> sacTestDriveAppointmentFindByTime(@Param("param")Map <String, Object> paramMap);
	
	/**
	 * 超长试驾申请更新
	 * @param paramMap
	 * @return
	 */
    int sacTestDriveLongApplyUpdateById(@Param("param") Map <String, Object> paramMap);
}
