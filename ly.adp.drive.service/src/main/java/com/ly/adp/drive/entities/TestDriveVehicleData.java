package com.ly.adp.drive.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.adp.drive.entities.enums.IsValidTestDriveEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.component.identityUtils.UUIDUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@TableName("t_sac_test_drive_vehicle_data")
public class TestDriveVehicleData {

    /**
     * 车机数据id
     */
    @TableId("vehicle_data_id")
    private String vehicleDataId;

    /**
     * 试驾单号
     */
    @TableField("test_drive_order_no")
    private String testDriveOrderNo;

    /**
     * 是否有效试驾 1-是，0-否
     */
    @TableField("is_valid_test_drive")
    private int isValidTestDrive;

    /**
     * 开始试驾电量（车机）
     */
    @TableField("veh_start_voc")
    private String vehStartVoc;

    /**
     * 试驾开始里程 (车机)
     */
    @TableField("veh_start_mileage")
    private String vehStartMileage;

    /**
     * 试驾结束里程 (车机)
     */
    @TableField("veh_end_mileage")
    private String vehEndMileage;

    /**
     * 试驾行驶里程（车机）
     */
    @TableField("veh_total_mileage")
    private String vehTotalMileage;

    /**
     * 试驾行驶时长（车机）
     */
    @TableField("veh_total_dur")
    private String vehTotalDur;

    /**
     * 试驾最高时速（车机）
     */
    @TableField("veh_max_speed")
    private String vehMaxSpeed;

    /**
     * 试驾平均时速（车机）
     */
    @TableField("test_drive_avg_speed")
    private String testDriveAvgSpeed;

    /**
     * 是否可用 1-可用、0-禁用
     */
    @TableField("is_enable")
    private int isEnable;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("last_updated_time")
    private LocalDateTime lastUpdatedTime;

    /**
     * 查询BI次数
     */
    @TableField("query_bi_times")
    private int queryBiTimes;

    /**
     * 是否优秀试驾：0=否；1=是
     */
    @JsonProperty("is_good_test_drive")
    private Integer isGoodTestDrive;

    /**
     * 是否匹配试驾录音：0=否；1=是
     */
    @JsonProperty("is_match_record")
    private Integer isMatchRecord;

    /**
     * 试驾录音时间
     */
    @JsonProperty("record_time")
    private LocalDateTime recordTime;

    /**
     * 试驾录音时长，单位秒
     */
    @JsonProperty("record_duration")
    private BigDecimal recordDuration;

    /**
     * 试驾得分
     */
    @JsonProperty("record_score")
    private BigDecimal recordScore;

    public String getVehicleDataId() {
        return vehicleDataId;
    }

    public void setVehicleDataId(String vehicleDataId) {
        this.vehicleDataId = vehicleDataId;
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public int getIsValidTestDrive() {
        return isValidTestDrive;
    }

    public void setIsValidTestDrive(int isValidTestDrive) {
        this.isValidTestDrive = isValidTestDrive;
    }

    public String getVehStartVoc() {
        return vehStartVoc;
    }

    public void setVehStartVoc(String vehStartVoc) {
        this.vehStartVoc = vehStartVoc;
    }

    public String getVehStartMileage() {
        return vehStartMileage;
    }

    public void setVehStartMileage(String vehStartMileage) {
        this.vehStartMileage = vehStartMileage;
    }

    public String getVehEndMileage() {
        return vehEndMileage;
    }

    public void setVehEndMileage(String vehEndMileage) {
        this.vehEndMileage = vehEndMileage;
    }

    public String getVehTotalMileage() {
        return vehTotalMileage;
    }

    public void setVehTotalMileage(String vehTotalMileage) {
        this.vehTotalMileage = vehTotalMileage;
    }

    public String getVehTotalDur() {
        return vehTotalDur;
    }

    public void setVehTotalDur(String vehTotalDur) {
        this.vehTotalDur = vehTotalDur;
    }

    public String getVehMaxSpeed() {
        return vehMaxSpeed;
    }

    public void setVehMaxSpeed(String vehMaxSpeed) {
        this.vehMaxSpeed = vehMaxSpeed;
    }

    public String getTestDriveAvgSpeed() {
        return testDriveAvgSpeed;
    }

    public void setTestDriveAvgSpeed(String testDriveAvgSpeed) {
        this.testDriveAvgSpeed = testDriveAvgSpeed;
    }

    public int getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(int isEnable) {
        this.isEnable = isEnable;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(LocalDateTime lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public int getQueryBiTimes() {
        return queryBiTimes;
    }

    public void setQueryBiTimes(int queryBiTimes) {
        this.queryBiTimes = queryBiTimes;
    }

    public Integer getIsGoodTestDrive() {
        return isGoodTestDrive;
    }

    public void setIsGoodTestDrive(Integer isGoodTestDrive) {
        this.isGoodTestDrive = isGoodTestDrive;
    }

    public Integer getIsMatchRecord() {
        return isMatchRecord;
    }

    public void setIsMatchRecord(Integer isMatchRecord) {
        this.isMatchRecord = isMatchRecord;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
    }

    public BigDecimal getRecordDuration() {
        return recordDuration;
    }

    public void setRecordDuration(BigDecimal recordDuration) {
        this.recordDuration = recordDuration;
    }

    public BigDecimal getRecordScore() {
        return recordScore;
    }

    public void setRecordScore(BigDecimal recordScore) {
        this.recordScore = recordScore;
    }

    @Override
    public String toString() {
        return "TestDriveVehicleData{" +
                "vehicleDataId='" + vehicleDataId + '\'' +
                ", testDriveOrderNo='" + testDriveOrderNo + '\'' +
                ", isValidTestDrive=" + isValidTestDrive +
                ", vehStartVoc='" + vehStartVoc + '\'' +
                ", vehStartMileage='" + vehStartMileage + '\'' +
                ", vehEndMileage='" + vehEndMileage + '\'' +
                ", vehTotalMileage='" + vehTotalMileage + '\'' +
                ", vehTotalDur='" + vehTotalDur + '\'' +
                ", vehMaxSpeed='" + vehMaxSpeed + '\'' +
                ", testDriveAvgSpeed='" + testDriveAvgSpeed + '\'' +
                ", isEnable=" + isEnable +
                ", creator='" + creator + '\'' +
                ", createdTime=" + createdTime +
                ", modifier='" + modifier + '\'' +
                ", lastUpdatedTime=" + lastUpdatedTime +
                ", queryBiTimes=" + queryBiTimes +
                ", isGoodTestDrive=" + isGoodTestDrive +
                ", isMatchRecord=" + isMatchRecord +
                ", recordTime=" + recordTime +
                ", recordDuration=" + recordDuration +
                ", recordScore=" + recordScore +
                '}';
    }

    /**
     * 设置公共属性值
     *
     * @param isInsert 是否insert操作
     * @param manual 是否手工维护
     * @param token
     */
    public void setCommonFields(boolean isInsert, boolean manual, String token) {
        String empName = BusicenContext.getCurrentUserBusiInfo(token).getEmpName();
        LocalDateTime now = LocalDateTime.now();
        if (isInsert) {
            setIsValidTestDrive(IsValidTestDriveEnum.CALCULATING.getCode());
            setCreatedTime(now);
            setCreator(empName);
        }
        if (!manual) {
            // 正常执行累计查询次数
            setQueryBiTimes(getQueryBiTimes() + 1);
        }
        setLastUpdatedTime(now);
        setModifier(empName);
    }

    public void saveInsertDO(String token, boolean manual, int isEnable) {
        setVehicleDataId(UUIDUtils.getUUIDString());
        setCommonFields(true, manual, token);
        setIsEnable(isEnable);
    }

    public VehicleDataVO entityToVO() {
        VehicleDataVO target = new VehicleDataVO();
        BeanUtils.copyProperties(this, target);
        target.setIsGoodTestDrive(Optional.ofNullable(this.getIsGoodTestDrive()).map(String::valueOf).orElse(null));
        target.setIsMatchRecord(Optional.ofNullable(this.getIsMatchRecord()).map(String::valueOf).orElse(null));
        target.setRecordTime(Optional.ofNullable(this.getRecordTime())
                .map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .orElse(null));
        target.setRecordDuration(Optional.ofNullable(this.getRecordDuration()).map(String::valueOf).orElse(null));
        target.setRecordScore(Optional.ofNullable(this.getRecordScore()).map(String::valueOf).orElse(null));
        return target;
    }
}