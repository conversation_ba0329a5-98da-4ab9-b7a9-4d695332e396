package com.ly.adp.drive.entities.in;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;

public class SacBasisLogIn  extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	//定义参与排序字段
	String columnArr="CREATED_DATE,LAST_UPDATED_DATE";
	@ApiModelProperty(value = "排序列", required = false, example = "")
	private String column;
	@ApiModelProperty(value = "排序方式", required = false, example = "")
	private String sorting;
	@ApiModelProperty(value = "日志ID", required = false, example = "")
	private String logId;
	@ApiModelProperty(value = "日志类型 1：短信发送", required = false, example = "")
	private String logType;
	@ApiModelProperty(value = "日志标识", required = false, example = "")
	private String logFlag;
	@ApiModelProperty(value = "日志内容", required = false, example = "")
	private String logDesc;
	@ApiModelProperty(value = "开始时间", required = false, example = "")
	private LocalDateTime begTime;
	@ApiModelProperty(value = "结束时间", required = false, example = "")
	private LocalDateTime endTime;
	public String getColumnArr() {
		return columnArr;
	}
	public void setColumnArr(String columnArr) {
		this.columnArr = columnArr;
	}
	public String getColumn() {
		return column;
	}
	public void setColumn(String column) {
		this.column = column;
	}
	public String getSorting() {
		return sorting;
	}
	public void setSorting(String sorting) {
		this.sorting = sorting;
	}
	public String getLogId() {
		return logId;
	}
	public void setLogId(String logId) {
		this.logId = logId;
	}
	public String getLogType() {
		return logType;
	}
	public void setLogType(String logType) {
		this.logType = logType;
	}
	public String getLogFlag() {
		return logFlag;
	}
	public void setLogFlag(String logFlag) {
		this.logFlag = logFlag;
	}
	public String getLogDesc() {
		return logDesc;
	}
	public void setLogDesc(String logDesc) {
		this.logDesc = logDesc;
	}
	public LocalDateTime getBegTime() {
		return begTime;
	}
	public void setBegTime(LocalDateTime begTime) {
		this.begTime = begTime;
	}
	public LocalDateTime getEndTime() {
		return endTime;
	}
	public void setEndTime(LocalDateTime endTime) {
		this.endTime = endTime;
	}
}
