package com.ly.adp.drive.entities.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("job获取bi车机数据入参")
public class GetBiVehicleDataJobReq {

    @ApiModelProperty(value = "试驾单号", required = true)
    private String testDriveOrderNo;

    @ApiModelProperty(value = "是否手工标识", required = true)
    private boolean isManual;

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public boolean isManual() {
        return isManual;
    }

    public void setManual(boolean manual) {
        isManual = manual;
    }
}
