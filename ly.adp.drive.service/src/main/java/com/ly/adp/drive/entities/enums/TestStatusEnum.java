package com.ly.adp.drive.entities.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 试乘试驾状态枚举
 */
public enum TestStatusEnum {

    CANCELLED("-1", "已取消"),
    NOT_STARTED("0", "未开始"),
    IN_PROGRESS("1", "试乘试驾中"),
    COMPLETED("2", "已结束"),
    EXPIRED("3", "已过期");

    private final String code;
    private final String desc;

    TestStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取对应的描述
     * @param code 状态码
     * @return 状态描述，如果code不存在则返回空字符串
     */
    public static String getDescByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        for (TestStatusEnum status : TestStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        return "";
    }

    /**
     * 通过code获取对应的枚举
     * @param code 状态码
     * @return 状态描述，如果code不存在则返回已取消
     */
    public static TestStatusEnum getTestStatusEnumByCode(String code) {
        for (TestStatusEnum status : TestStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return CANCELLED;
    }

    /**
     * 判断状态码是否有效
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        for (TestStatusEnum status : TestStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有有效的状态码
     * @return 状态码列表
     */
    public static List<String> getAllCodes() {
        return Arrays.stream(TestStatusEnum.values())
                .map(TestStatusEnum::getCode)
                .collect(Collectors.toList());
    }

    // getters
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}