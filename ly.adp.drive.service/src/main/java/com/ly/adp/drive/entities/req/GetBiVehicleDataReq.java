package com.ly.adp.drive.entities.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("获取bi车机数据入参")
public class GetBiVehicleDataReq implements Serializable {

    private static final long serialVersionUID = 2922874097513955090L;

    @ApiModelProperty(value = "试驾单号", required = true)
    private String testDriveOrderNo;

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }
}
