package com.ly.adp.drive.controller;


import java.util.Map;

import com.ly.adp.drive.service.ISacTestDriveTaskService;

import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;


import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.net.HttpHeaders;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 试驾任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@RestController
@Api(value = "试驾任务服务", tags = "试驾任务服务")
@RequestMapping(value = "/ly/tdm/sacTestDriveTask", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SacTestDriveTaskController {

    final ISacTestDriveTaskService sacTestDriveTaskService;

    public SacTestDriveTaskController(ISacTestDriveTaskService sacTestDriveTaskService) {

        this.sacTestDriveTaskService = sacTestDriveTaskService;

    }

    @ApiOperation(value = "试驾任务查询", notes = "试驾任务查询")
    @PostMapping(value = "/sacTestDriveTaskquery.do")
    public ListResult<Map<String, Object>> sacTestDriveTaskFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacTestDriveTaskService.sacTestDriveTaskFindInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "试驾任务查询-PC", notes = "试驾任务查询")
    @PostMapping(value = "/sacTestDriveTaskListquery.do")
    public ListResult<Map<String, Object>> sacTestDriveTaskFindListInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacTestDriveTaskService.sacTestDriveTaskFindListInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "个人试乘试驾单列表查询导出", notes = "个人试乘试驾单列表查询导出")
    @PostMapping(value = "/sacTestDriveTaskExport.do")
    public OptResult sactestdriveexport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacTestDriveTaskService.sacTestDriveTaskExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "试驾任务查询-总部", notes = "试驾任务查询-总部")
    @PostMapping(value = "/sacTestDriveTaskAllquery.do")
    public ListResult<Map<String, Object>> sacTestDriveTaskFindAllInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doList(() -> sacTestDriveTaskService.sacTestDriveTaskFindAllInfo(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "试驾任务查询-总部导出", notes = "试驾任务查询-总部导出")
    @PostMapping(value = "/sacTestDriveTaskAllExport.do")
    public OptResult sacTestDriveTaskAllExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacTestDriveTaskService.sacTestDriveTaskAllExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "试驾任务数", notes = "试驾任务数")
    @PostMapping(value = "/sacTestDriveTaskCount.do")
    public EntityResult<Map<String, Object>> sacTestDriveTaskFindCount(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacTestDriveTaskService.sacTestDriveTaskFindCount(dateInfo, authentication)).result();
    }

    @ApiOperation(value = "试驾任务维护", notes = "试驾任务维护")
    @PostMapping(value = "/sacTestDriveTasksave.todo")
    public EntityResult<Map<String, Object>> sacTestDriveTaskSaveInfo(@RequestBody(required = false) Map<String, Object> dateInfo) {
        return BusicenInvoker.doEntity(() -> sacTestDriveTaskService.sacTestDriveTaskSaveInfo(dateInfo, "")).result();
    }
}
