package com.ly.adp.drive.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.component.helper.StringHelper;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 公共工具类
 * <AUTHOR>
 * @Version 1.0.0
 **/
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    public static final String XTADMIN = "xtadmin";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 单号生成器
     * @param dlrId 专营店ID
     * @return 生成的单号
     */
    public static String generateOrderNumber(String prefix, String dlrId) {
        // 如果dlrId为空或者长度为0，使用默认值"000000"
        if (dlrId == null || dlrId.isEmpty()) {
            dlrId = generateRandomNumber(7);
        }

        // 获取当前时间并格式化为8位数字字符串（yyyyMMddHHmmss）
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDate = now.format(formatter);

        // 拼接"HV"前缀、dlrId和当前时间字符串
        String orderNumber = prefix + dlrId + formattedDate;

        // 如果拼接后的单号长度超过16位，则截取前16位
        if (orderNumber.length() > 16) {
            orderNumber = orderNumber.substring(0, 16);
        }

        // 如果拼接后的单号长度小于16位，需要在前面补0
        while (orderNumber.length() < 16) {
            orderNumber += "0"; // 在后面补0
        }

        // 返回生成的单号
        return orderNumber;
    }

    /**
     * 生成随机的n位数字字符串
     * @param n 需要生成的随机数的位数
     * @return 随机的n位数字字符串
     */
    public static String generateRandomNumber(int n) {
        Random random = new Random();
        int randomNumber = random.nextInt((int) Math.pow(10, n) - 1) + (int) Math.pow(10, n-1);
        return String.format("%0" + n + "d", randomNumber);
    }

    /**
     * 是否系统管理员
     * @param token
     * @return
     */
    public static boolean isXtAdmin(String token) {
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        return XTADMIN.equals(userBusiEntity.getEmpName());
    }

    /**
     * 获取当前日期的字符串，格式为yyyyMMdd
     * @return 当前日期字符串
     */
    public static String getCurrentDateString() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate currentDate = LocalDate.now();
        return currentDate.format(formatter);
    }

    /**
     * 将给定的年月格式化为 yyyy-MM 的格式
     * @param yyyymm 年月字符串，格式为 yyyyMM
     * @return 格式化后的年月字符串，格式为 yyyy-MM
     */
    public static String formatYearMonth(String yyyymm) {
        // 检查输入的年月字符串是否合法
        if (yyyymm == null || yyyymm.length() != 6) {
            throw new IllegalArgumentException("Invalid input format. Expected yyyyMM.");
        }

        // 解析年月字符串
        String year = yyyymm.substring(0, 4);
        String month = yyyymm.substring(4, 6);

        // 构建 LocalDate 对象
        LocalDate date = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);

        // 格式化为 yyyy-MM 字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return date.format(formatter);
    }

    /**
     * 实体list信息转为 json 字符串
     * @param list 实体列表
     * @return JSON字符串
     */
    public static <E> String listToJsonString(List<E> list) {
        try {
            // 将 List 转换为 JSON 字符串
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "[]";
        }
    }

    /**
     * 实体信息转为 json 字符串
     * @param obj 实体对象
     * @return JSON字符串
     */
    public static <T> String objToJsonString(T obj) {
        try {
            // 将对象转换为 JSON 字符串
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("实体转换json失败", e);
            return "{}";
        }
    }
    /**
     * JSON字符串转换为实体对象
     * @param <T> 实体对象类型
     * @param json JSON字符串
     * @param clazz 实体对象的Class
     * @return 实体对象
     */
    public static <T> T jsonStringToObj(String json, Class<T> clazz) {
        try {
            // 将 JSON 字符串转换为对象
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("JSON转换实体失败", e);
            return null;
        }
    }

    /**
     * JSON字符串转换为实体列表
     * @param <E> 实体列表元素类型
     * @param json JSON字符串
     * @param clazz 实体列表元素的Class
     * @return 实体列表
     */
    public static <E> List<E> jsonStringToList(String json, Class<E> clazz) {
        if (StringHelper.IsEmptyOrNull(json)) {
            return Lists.newArrayList();
        }
        try {
            // 将 JSON 字符串转换为对象列表
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            log.error("JSON转换实体列表失败", e);
            return null;
        }
    }

    /**
     * 将13位时间戳转换为年月日时分秒格式的字符串
     * @param timestamp 13位时间戳（毫秒级）
     * @return 格式化的日期时间字符串
     */
    public static String timestampToDateTimeString(long timestamp) {
        // 创建日期对象
        Date date = new Date(timestamp);
        // 定义日期时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将日期对象格式化为字符串
        return dateFormat.format(date);
    }

    /**
     * 转换为整数
     * @param value
     * @return
     */
    public static int convertToInt(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    public static BigDecimal convertToBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                ret = new BigDecimal((String) value);
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = BigDecimal.valueOf(((Number) value).doubleValue());
            } else {
                throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
            }
        }
        return ret;
    }

}
