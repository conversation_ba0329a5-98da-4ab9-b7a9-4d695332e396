package com.ly.adp.drive.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/21
 */
@Component
public class CacheUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public static final String WX_APP_TOKEN = "wxAppToken";

    public static final String WX_TOKEN = "wxToken";

    public static final String WX_LOAD_CONFIG = "loadConfig";

    /**
     * 旁路缓存 读
     *
     * @param key key
     * @param typeReference class
     * @param supplier action
     * @param duration ttl
     * @return T result
     */
    public <T> T getAndCache(String key, TypeReference<T> typeReference, Supplier<T> supplier, Duration duration) {
        String cache = redisTemplate.opsForValue().get(key);
        if (Objects.nonNull(cache)) {
            return JSONObject.parseObject(cache, typeReference);
        }

        T res = supplier.get();
        redisTemplate.opsForValue().set(key, JSONObject.toJSONString(res), duration);
        return res;
    }
}
