package com.ly.adp.drive.entities.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


@ApiModel("获取bi车机数据返回结果VO")
public class GetBiVehicleDataResultVO implements Serializable {

    private static final long serialVersionUID = -956163285852035140L;

    @ApiModelProperty(value = "试驾单号")
    private String testDriveOrderNo;

    @ApiModelProperty(value = "处理结果编码 0-获取成功，1-获取失败即没获取到数据")
    private String code;

    @ApiModelProperty(value = "处理结果")
    private String msg;

    @ApiModelProperty(value = "查询bi次数")
    private Integer queryBiTimes;

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public Integer getQueryBiTimes() {
        return queryBiTimes;
    }

    public void setQueryBiTimes(Integer queryBiTimes) {
        this.queryBiTimes = queryBiTimes;
    }

    public enum Msg {
        SUCCESS("0", "获取成功"),
        FAIL("1", "获取失败");
        private final String code;
        private final String description;

        Msg(String code, String description) {
            this.code = code;
            this.description = description;
        }
        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
