package com.ly.adp.drive.otherservice;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import ly.adp.drive.otherservice.ICscSysClueService;

@Service
public class CscSysClueService implements ICscSysClueService {
	
	@Autowired
	ICscClueFeignClient cscClueFeignClient;

	@Override
	public EntityResult<Map<String, Object>> clueDlrCheckRepeat(String authentication,
			ParamBase<Map<String, Object>> queryCondition) {
		return cscClueFeignClient.clueDlrCheckRepeat(authentication, queryCondition);
	}

	@Override
	public EntityResult<Map<String, Object>> clueDlrSave(String authentication, ParamBase<Map<String, Object>> queryCondition) {
		// TODO Auto-generated method stub
		return cscClueFeignClient.clueDlrSave(authentication, queryCondition);
	}
	@Override
	public ListResult<Map<String, Object>> queryConfigList(String authentication,
			ParamPage<Map<String, Object>> dataInfo) {
		ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
		result= cscClueFeignClient.queryConfigList(authentication, dataInfo);
		return result;
	}

	@Override
	public ListResult<Map<String, Object>> queryListSysteConfigValueByCode(String token,
			ParamPage<Map<String, Object>> dataInfo) {
		return cscClueFeignClient.queryListSysteConfigValueByCode(token, dataInfo);
	}



}
