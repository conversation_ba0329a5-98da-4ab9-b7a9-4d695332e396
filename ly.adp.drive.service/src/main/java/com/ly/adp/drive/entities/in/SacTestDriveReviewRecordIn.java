package com.ly.adp.drive.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class SacTestDriveReviewRecordIn extends PageInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "试乘试驾单ID", required = false, example = "")
	private String testDriveSheetId;
	@ApiModelProperty(value = "试乘试驾单号", required = false, example = "")
	private String testDriveOrderNo;
	@ApiModelProperty(value = "试乘试驾单跟进内容", required = false, example = "")
	private String testDriveDesc;

	public String getTestDriveDesc() {
		return testDriveDesc;
	}

	public void setTestDriveDesc(String testDriveDesc) {
		this.testDriveDesc = testDriveDesc;
	}

	public String getTestDriveSheetId() {
		return testDriveSheetId;
	}

	public void setTestDriveSheetId(String testDriveSheetId) {
		this.testDriveSheetId = testDriveSheetId;
	}

	public String getTestDriveOrderNo() {
		return testDriveOrderNo;
	}

	public void setTestDriveOrderNo(String testDriveOrderNo) {
		this.testDriveOrderNo = testDriveOrderNo;
	}
}
