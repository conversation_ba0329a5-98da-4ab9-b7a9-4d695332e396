package com.ly.adp.drive.entities;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 试乘试驾单表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@TableName("t_sac_test_drive_sheet")
public class SacTestDriveSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试乘试驾单ID
     */
    @TableId("TEST_DRIVE_SHEET_ID")
    private String testDriveSheetId;

    /**
     * 试乘试驾单号
     */
    @TableField("TEST_DRIVE_ORDER_NO")
    private String testDriveOrderNo;

    /**
     * 试乘试驾预约单ID
     */
    @TableField("APPOINTMENT_ID")
    private String appointmentId;

    /**
     * 原试乘试驾预约单ID
     */
    @TableField("OLD_TEST_DRIVE_SHEET_ID")
    private String oldTestDriveSheetId;

    /**
     * 试乘试驾状态
     */
    @TableField("TEST_STATUS")
    private String testStatus;

    /**
     * 所属专营店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 所属专营店名称
     */
    @TableField("DLR_NAME")
    private String dlrName;

    /**
     * 销售顾问姓名
     */
    @TableField("SALES_CONSULTANT_NAME")
    private String salesConsultantName;

    /**
     * 销售顾问ID
     */
    @TableField("SALES_CONSULTANT_ID")
    private String salesConsultantId;

    /**
     * 意向级别编码
     */
    @TableField("INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @TableField("INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 驾驶人与客户关系
     */
    @TableField("DRIVER_CUSTOMER_RELATION")
    private String driverCustomerRelation;

    /**
     * 驾驶人姓名
     */
    @TableField("DRIVER_NAME")
    private String driverName;

    /**
     * 驾驶人联系电话
     */
    @TableField("DRIVER_PHONE")
    private String driverPhone;

    /**
     * 驾驶证类型
     */
    @TableField("DRIVING_LICENCE_TYPE")
    private String drivingLicenceType;

    /**
     * 驾驶证号码
     */
    @TableField("DRIVING_LICENCE_NUMBER")
    private String drivingLicenceNumber;

    /**
     * 驾驶人地址
     */
    @TableField("ADDRESS")
    private String address;

    /**
     * 驾驶证附件
     */
    @TableField("DRIVING_LICENCE_PHOTO")
    private String drivingLicencePhoto;

    /**
     * 试乘试驾行驶里程
     */
    @TableField("TEST_ROAD_HAUL")
    private double testRoadHaul;

    /**
     * 试乘试驾开始里程
     */
    @TableField("TEST_START_ROAD_HAUL")
    private double testStartRoadHaul;

    /**
     * 试乘试驾结束里程
     */
    @TableField("TEST_END_ROAD_HAUL")
    private double testEndRoadHaul;

    /**
     * 试驾押金
     */
    @TableField("DEPOSIT")
    private double deposit;

    /**
     * 线索单号
     */
    @TableField("DLR_CLUE_ORDER_NO")
    private String dlrClueOrderNo;

    /**
     * 客户姓名
     */
    @TableField("CUSTOMER_NAME")
    private String customerName;

    /**
     * 客户ID
     */
    @TableField("CUSTOMER_ID")
    private String customerId;

    /**
     * 客户电话
     */
    @TableField("CUSTOMER_PHONE")
    private String customerPhone;

    /**
     * 性别
     */
    @TableField("CUSTOMER_SEX")
    private String customerSex;

    /**
     * 试乘试驾车型编码
     */
    @TableField("SMALL_CAR_TYPE_CODE")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @TableField("SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @TableField("PLATE_NUMBER")
    private String plateNumber;

    /**
     * VIN(车架号)
     */
    @TableField("CAR_VIN")
    private String carVin;

    /**
     * 试乘试驾类型(0：试乘，1：试驾)
     */
    @TableField("TEST_TYPE")
    private String testType;

    /**
     * 预约渠道(0：门店自建，1：线上预约)
     */
    @TableField("APPOINTMENT_CHANNEL")
    private String appointmentChannel;

    /**
     * 开始时间
     */
    @TableField("START_TIME")
    private String startTime;

    /**
     * 结束时间
     */
    @TableField("END_TIME")
    private String endTime;

    /**
     * 协议附件
     */
    @TableField("TEST_DRIVE_AGREEMENT")
    private String testDriveAgreement;

    /**
     * 顾客身份证附件
     */
    @TableField("CUSTOMER_ID_NUMBER_AGREEMENT")
    private String customerIdNumberAgreement;

    /**
     * 其他附件
     */
    @TableField("OTHER_AGREEMENT")
    private String otherAgreement;

    /**
     * 顾客签名附件
     */
    @TableField("CUSTOMER_SIGNATURE_AGREEMENT")
    private String customerSignatureAgreement;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 是否可用
     */
    @TableField("IS_CAN_CHANGE")
    private String isCanChange;

    /**
     * 是否可以更换门店
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 试乘试驾单号
     */
    @TableField(exist = false)
    private List<String> testDriveOrderNoList;

    public String getTestDriveSheetId() {
        return testDriveSheetId;
    }

    public void setTestDriveSheetId(String testDriveSheetId) {
        this.testDriveSheetId = testDriveSheetId;
    }

    public String getOldTestDriveSheetId() {
        return oldTestDriveSheetId;
    }

    public void setOldTestDriveSheetId(String oldTestDriveSheetId) {
        this.oldTestDriveSheetId = oldTestDriveSheetId;
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public String getAppointmentId() {
        return appointmentId;
    }

    public void setAppointmentId(String appointmentId) {
        this.appointmentId = appointmentId;
    }

    public double getDeposit() {
        return deposit;
    }

    public void setDeposit(double deposit) {
        this.deposit = deposit;
    }

    public String getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(String testStatus) {
        this.testStatus = testStatus;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }

    public String getSalesConsultantName() {
        return salesConsultantName;
    }

    public void setSalesConsultantName(String salesConsultantName) {
        this.salesConsultantName = salesConsultantName;
    }

    public String getSalesConsultantId() {
        return salesConsultantId;
    }

    public void setSalesConsultantId(String salesConsultantId) {
        this.salesConsultantId = salesConsultantId;
    }

    public String getIntenLevelName() {
        return intenLevelName;
    }

    public void setIntenLevelName(String intenLevelName) {
        this.intenLevelName = intenLevelName;
    }

    public String getIntenLevelCode() {
        return intenLevelCode;
    }

    public void setIntenLevelCode(String intenLevelCode) {
        this.intenLevelCode = intenLevelCode;
    }

    public String getDriverCustomerRelation() {
        return driverCustomerRelation;
    }

    public void setDriverCustomerRelation(String driverCustomerRelation) {
        this.driverCustomerRelation = driverCustomerRelation;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getDrivingLicenceType() {
        return drivingLicenceType;
    }

    public void setDrivingLicenceType(String drivingLicenceType) {
        this.drivingLicenceType = drivingLicenceType;
    }

    public String getDrivingLicenceNumber() {
        return drivingLicenceNumber;
    }

    public void setDrivingLicenceNumber(String drivingLicenceNumber) {
        this.drivingLicenceNumber = drivingLicenceNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDrivingLicencePhoto() {
        return drivingLicencePhoto;
    }

    public void setDrivingLicencePhoto(String drivingLicencePhoto) {
        this.drivingLicencePhoto = drivingLicencePhoto;
    }

    public double getTestRoadHaul() {
        return testRoadHaul;
    }

    public void setTestRoadHaul(double testRoadHaul) {
        this.testRoadHaul = testRoadHaul;
    }

    public double getTestStartRoadHaul() {
        return testStartRoadHaul;
    }

    public void setTestStartRoadHaul(double testStartRoadHaul) {
        this.testStartRoadHaul = testStartRoadHaul;
    }

    public double getTestEndRoadHaul() {
        return testEndRoadHaul;
    }

    public void setTestEndRoadHaul(double testEndRoadHaul) {
        this.testEndRoadHaul = testEndRoadHaul;
    }

    public String getDlrClueOrderNo() {
        return dlrClueOrderNo;
    }

    public void setDlrClueOrderNo(String dlrClueOrderNo) {
        this.dlrClueOrderNo = dlrClueOrderNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getCustomerSex() {
        return customerSex;
    }

    public void setCustomerSex(String customerSex) {
        this.customerSex = customerSex;
    }

    public String getSmallCarTypeCode() {
        return smallCarTypeCode;
    }

    public void setSmallCarTypeCode(String smallCarTypeCode) {
        this.smallCarTypeCode = smallCarTypeCode;
    }

    public String getSmallCarTypeName() {
        return smallCarTypeName;
    }

    public void setSmallCarTypeName(String smallCarTypeName) {
        this.smallCarTypeName = smallCarTypeName;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getCarVin() {
        return carVin;
    }

    public void setCarVin(String carVin) {
        this.carVin = carVin;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public String getAppointmentChannel() {
        return appointmentChannel;
    }

    public void setAppointmentChannel(String appointmentChannel) {
        this.appointmentChannel = appointmentChannel;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTestDriveAgreement() {
        return testDriveAgreement;
    }

    public void setTestDriveAgreement(String testDriveAgreement) {
        this.testDriveAgreement = testDriveAgreement;
    }

    public String getOtherAgreement() {
        return otherAgreement;
    }

    public void setOtherAgreement(String otherAgreement) {
        this.otherAgreement = otherAgreement;
    }

    public String getCustomerIdNumberAgreement() {
        return customerIdNumberAgreement;
    }

    public void setCustomerIdNumberAgreement(String customerIdNumberAgreement) {
        this.customerIdNumberAgreement = customerIdNumberAgreement;
    }

    public String getCustomerSignatureAgreement() {
        return customerSignatureAgreement;
    }

    public void setCustomerSignatureAgreement(String customerSignatureAgreement) {
        this.customerSignatureAgreement = customerSignatureAgreement;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getIsCanChange() {
        return isCanChange;
    }

    public void setIsCanChange(String isCanChange) {
        this.isCanChange = isCanChange;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public List<String> getTestDriveOrderNoList() {
        return testDriveOrderNoList;
    }

    public void setTestDriveOrderNoList(List<String> testDriveOrderNoList) {
        this.testDriveOrderNoList = testDriveOrderNoList;
    }

    @Override
    public String toString() {
        return "SacTestDriveSheet{" +
                "testDriveSheetId=" + testDriveSheetId +
                ", testDriveOrderNo=" + testDriveOrderNo +
                ", appointmentId=" + appointmentId +
                ", testStatus=" + testStatus +
                ", dlrCode=" + dlrCode +
                ", dlrName=" + dlrName +
                ", salesConsultantName=" + salesConsultantName +
                ", salesConsultantId=" + salesConsultantId +
                ", intenLevelName=" + intenLevelName +
                ", intenLevelCode=" + intenLevelCode +
                ", driverCustomerRelation=" + driverCustomerRelation +
                ", driverName=" + driverName +
                ", driverPhone=" + driverPhone +
                ", drivingLicenceType=" + drivingLicenceType +
                ", drivingLicenceNumber=" + drivingLicenceNumber +
                ", address=" + address +
                ", drivingLicencePhoto=" + drivingLicencePhoto +
                ", testRoadHaul=" + testRoadHaul +
                ", testStartRoadHaul=" + testStartRoadHaul +
                ", testEndRoadHaul=" + testEndRoadHaul +
                ", dlrClueOrderNo=" + dlrClueOrderNo +
                ", customerName=" + customerName +
                ", customerId=" + customerId +
                ", customerPhone=" + customerPhone +
                ", customerSex=" + customerSex +
                ", smallCarTypeCode=" + smallCarTypeCode +
                ", smallCarTypeName=" + smallCarTypeName +
                ", plateNumber=" + plateNumber +
                ", carVin=" + carVin +
                ", testType=" + testType +
                ", appointmentChannel=" + appointmentChannel +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", testDriveAgreement=" + testDriveAgreement +
                ", customerIdNumberAgreement=" + customerIdNumberAgreement +
                ", customerSignatureAgreement=" + customerSignatureAgreement +
                ", oemId=" + oemId +
                ", groupId=" + groupId +
                ", creator=" + creator +
                ", createdName=" + createdName +
                ", createdDate=" + createdDate +
                ", modifier=" + modifier +
                ", modifyName=" + modifyName +
                ", lastUpdatedDate=" + lastUpdatedDate +
                ", updateControlId=" + updateControlId +
                ", isEnable=" + isEnable +
                ", oldTestDriveSheetId=" + oldTestDriveSheetId +
                ", deposit=" + deposit +
                ", isCanChange=" + isCanChange +
                ",otherAgreement=" + otherAgreement +
                ", column1=" + column1 +
                ", column2=" + column2 +
                ", column3=" + column3 +
                ", column4=" + column4 +
                ", column5=" + column5 +
                ", column6=" + column6 +
                ", column7=" + column7 +
                ", column8=" + column8 +
                ", column9=" + column9 +
                ", column10=" + column10 +
                "}";
    }

    public LambdaQueryWrapper<SacTestDriveSheet> buildQueryWrapper(SFunction<SacTestDriveSheet, ?>... columns) {
        LambdaQueryWrapper<SacTestDriveSheet> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(this.testDriveOrderNo)) {
            queryWrapper.eq(SacTestDriveSheet::getTestDriveOrderNo, this.testDriveOrderNo);
        }
        if (CollectionUtil.isNotEmpty(this.testDriveOrderNoList)) {
            queryWrapper.in(SacTestDriveSheet::getTestDriveOrderNo, this.testDriveOrderNoList);
        }
        if (StringUtils.isNotBlank(this.customerName)) {
            queryWrapper.eq(SacTestDriveSheet::getCustomerName, this.customerName);
        }
        if (StringUtils.isNotBlank(this.customerPhone)) {
            queryWrapper.eq(SacTestDriveSheet::getCustomerPhone, this.customerPhone);
        }
        if (StringUtils.isNotBlank(this.plateNumber)) {
            queryWrapper.eq(SacTestDriveSheet::getPlateNumber, this.plateNumber);
        }
        if (StringUtils.isNotBlank(this.carVin)) {
            queryWrapper.eq(SacTestDriveSheet::getCarVin, this.carVin);
        }
        if (Objects.nonNull(columns)) {
            queryWrapper.select(columns);
        }
        return queryWrapper;
    }
}
