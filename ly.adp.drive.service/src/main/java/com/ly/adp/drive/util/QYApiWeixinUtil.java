package com.ly.adp.drive.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.busicen.common.helper.SpringContextHolder;
import com.ly.mp.busicen.common.util.HttpUtil;
import com.ly.mp.dal.comm.jdbc.PagedJdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class QYApiWeixinUtil {
    Logger log = LoggerFactory.getLogger(QYApiWeixinUtil.class);

    private String wxHost;

    private String corpid;

    private String corpsecret;

    private String appSecret;

    private int appid;

    @Autowired
    protected PagedJdbcTemplate jdbcTemplate;

    @Autowired
    private CacheUtil cacheUtil;

    public void getPswList() {
        Map<String, Object> map = cacheUtil.getAndCache(CacheUtil.WX_LOAD_CONFIG, new TypeReference<Map<String, Object>>() {},
                this::queryConfigFromDB, Duration.ofHours(1L));

        // 更新类的成员变量
        wxHost = map.get("wxHost").toString();
        corpid = map.get("corpid").toString();
        corpsecret = map.get("addresssecret").toString();
        appSecret = map.get("appSecret").toString();
        appid = Integer.parseInt(map.get("appid").toString());
    }

    private Map<String, Object> queryConfigFromDB() {
        Map<String, Object> map = new HashMap<>();
        String sql = " select LOOKUP_VALUE_NAME,ATTRIBUTE2 from t_prc_mds_lookup_value where LOOKUP_TYPE_CODE='SYS_PSW_001' ";
        PagedJdbcTemplate template = SpringContextHolder.getBean(PagedJdbcTemplate.class);
        List<Map<String, Object>> list = template.queryForList(sql.toString(), map);
        list.stream().forEach(e -> {
            map.put(e.get("LOOKUP_VALUE_NAME").toString(), e.get("ATTRIBUTE2"));
        });
        return map;
    }

    /**
     * 通过ADP的userID 获取 企微userID
     * @param userID ADP userID
     * @return
     */
    public String getWeComUserID(String userID) {
        Map<String, Object> map = new HashMap<>();
        String sql = String.format("select mobile from mp.t_usc_mdm_org_employee where user_id = '%s'", userID);
        Map<String, Object> adpUserMap = SwitchDbInvoke.invokeTidb(() -> SpringContextHolder.getBean(PagedJdbcTemplate.class).queryForMap(sql, map));
        if (CollectionUtils.isEmpty(adpUserMap)) {
            throw new BusicenException(String.format("userID为:%s的员工在adp系统里不存在", userID));
        }
        // 通过mobile 获取 企微的userID
        return getUserID(String.valueOf(adpUserMap.get("mobile")));
    }

    /*
     * 企微获取通讯录acctoken
     * */
    public Map<String, Object> getToken() {
        // 加载配置
        this.getPswList();
        return cacheUtil.getAndCache(CacheUtil.WX_TOKEN, new TypeReference<Map<String, Object>>() {
        }, () -> {
            log.info("[企微]getToken");
            Map<String, Object> map = new HashMap<>();
            map.put("corpid", corpid);
            map.put("corpsecret", corpsecret);
            String resp = HttpUtil.get(urlJoin(wxHost, "/cgi-bin/gettoken"), map, 3000, 3000, "UTF-8");
            Map<String, Object> respMap = JSON.parseObject(resp);
            if (!"ok".equals(respMap.get("errmsg"))) {
                throw new BusicenException("获取企业微信token失败" + resp);
            }
            return respMap;
        }, Duration.ofHours(1L));
    }

    /*
     * 企微获取应用acctoken
     * */
    public Map<String, Object> getAppToken() {
        // 加载配置
        this.getPswList();
        return cacheUtil.getAndCache(CacheUtil.WX_APP_TOKEN, new TypeReference<Map<String, Object>>() {
        }, () -> {
            log.info("[企微]getAppToken");
            Map<String, Object> map = new HashMap<>();
            map.put("corpid", corpid);
            map.put("corpsecret", appSecret);
            String resp = HttpUtil.get(urlJoin(wxHost, "/cgi-bin/gettoken"), map, 3000, 3000, "UTF-8");

            Map<String, Object> respMap = JSON.parseObject(resp);
            if (!"ok".equals(respMap.get("errmsg"))) {
                throw new BusicenException("获取企业微信token失败" + resp);
            }
            return respMap;
        }, Duration.ofHours(1L));
    }

    /*
     * 企微获取部门
     * */
    public Map<String, Object> queryDepartmentList(Map<String, Object> headers) {
        Map<String, Object> token = this.getToken();

        Map<String, Object> param = new HashMap<>();
        param.put("access_token", token.get("access_token"));
        param.putAll(headers);

        String resp = HttpUtil.get(urlJoin(wxHost, "/cgi-bin/department/list"), param, 3000, 3000, "UTF-8");
        Map<String, Object> respMap = JSON.parseObject(resp);
        if (!"ok".equals(respMap.get("errmsg"))) {
            throw new BusicenException("获取企业微信部门失败" + resp);
        }

        return respMap;
    }

    /*
     * 企微创建部门
     * */
    public Map<String, Object> createDepartment(Map<String, Object> param) {
        Map<String, Object> token = this.getToken();

        HashMap<String, String> headers = new HashMap<>();

        headers.put("content-type", "application/json");

        String resp = HttpUtil.post(wxHost + "/cgi-bin/department/create?access_token=" + token.get("access_token").toString(), param, headers, 3000, 3000, "UTF-8");
        Map<String, Object> respMap = JSON.parseObject(resp);
        if (!"created".equals(respMap.get("errmsg"))) {
            throw new BusicenException("创建企业微信部门失败：" + param.get("name") + resp);
        }

        return respMap;
    }

    /*
     * 企微创建人员
     * */
    public Map<String, Object> createUser(Map<String, Object> param) {
        Map<String, Object> token = this.getToken();

        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        String resp = HttpUtil.post(wxHost + "/cgi-bin/user/create?access_token=" + token.get("access_token").toString(), param, headers, 3000, 3000, "UTF-8");
        log.info("创建企业微信人员返回{}：" + resp);
        Map<String, Object> respMap = JSON.parseObject(resp);
        if (!"created".equals(respMap.get("errmsg"))) {
            throw new BusicenException("创建企业微信人员失败：" + param.get("name") + resp);
        }

        return respMap;
    }

    /*
     * 企微删除人员
     * */
    public Map<String, Object> deleteUser(Map<String, Object> param) {
        Map<String, Object> token = this.getToken();
        param.put("access_token", token.get("access_token"));

        String resp = HttpUtil.get(wxHost + "/cgi-bin/user/delete", param, 3000, 3000, "UTF-8");
        Map<String, Object> respMap = JSON.parseObject(resp);
        if (!"deleted".equals(respMap.get("errmsg"))) {
            throw new BusicenException("删除企业微信人员失败" + resp);
        }

        return respMap;
    }

    /*
     * 发送企微消息
     * */
    public void send(Map<String, Object> param) {
        Map<String, Object> token = this.getAppToken();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        param.put("agentid", appid);
        log.info("发送企微消息入参：{}", JSONObject.toJSONString(param));
        String resp = HttpUtil.post(wxHost + "/cgi-bin/message/send?access_token=" + token.get("access_token").toString(), param, headers, 3000, 3000, "UTF-8");
        Map<String, Object> respMap = JSON.parseObject(resp);
        log.info("发送企微消息结果：{}", JSONObject.toJSONString(respMap));
        if (!"ok".equals(respMap.get("errmsg"))) {
            throw new BusicenException("发送企微消息失败：" + resp);
        }
    }

    /**
     * 通过手机号获取userid
     * @param mobile
     */
    public String getUserID(String mobile) {
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> token = this.getAppToken();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("content-type", "application/json");
        param.put("mobile", mobile);
        String resp = HttpUtil.post(wxHost + "/cgi-bin/user/getuserid?access_token=" + token.get("access_token").toString(), param, headers, 3000, 3000, "UTF-8");
        Map<String, Object> respMap = JSON.parseObject(resp);
        if (!"ok".equals(respMap.get("errmsg"))) {
            throw new BusicenException("通过手机号获取userid失败：" + resp);
        }
        return String.valueOf(respMap.get("userid"));
    }

    public String urlJoin(String host, String... uri) {
        if (StringUtils.isEmpty(host)) {
            host = "";
        } else if (host.endsWith("/")) {
            host = host.substring(0, host.length() - 1);
        }
        List<String> uris = new ArrayList<>();
        uris.add(host);
        if (uri != null && uri.length > 0) {
            for (String s : uri) {
                if (s.startsWith("/")) {
                    s = s.substring(1);
                }
                if (s.endsWith("/")) {
                    s = s.substring(0, s.length() - 1);
                }
                uris.add(s);
            }
        }
        return uris.stream().collect(Collectors.joining("/"));
    }

}
