package com.ly.adp.drive.entities.out;

import java.io.Serializable;
import java.util.Map;

public class FieldMappingOut implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 转换后的映射字段
     */
    private Map<String,Object> targetFields;
    
    /**
     * 转换后的扩展字段 json字段
     */
    private String targetJson;

	public Map<String, Object> getTargetFields() {
		return targetFields;
	}

	public void setTargetFields(Map<String, Object> targetFields) {
		this.targetFields = targetFields;
	}

	public String getTargetJson() {
		return targetJson;
	}

	public void setTargetJson(String targetJson) {
		this.targetJson = targetJson;
	}

	@Override
	public String toString() {
		return "FieldMappingOut [targetFields=" + targetFields + ", targetJson=" + targetJson + "]";
	}
    
    
    
}
