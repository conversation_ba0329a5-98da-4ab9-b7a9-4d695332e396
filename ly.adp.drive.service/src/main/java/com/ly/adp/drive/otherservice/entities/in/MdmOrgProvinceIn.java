package com.ly.adp.drive.otherservice.entities.in;

import java.io.Serializable;

import com.ly.mp.component.entities.PageInfo;

import io.swagger.annotations.ApiModelProperty;

public class MdmOrgProvinceIn extends PageInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "省份ID")
	private String provinceId;
	@ApiModelProperty(value = "省份编码")
	private String provinceCode;

	@ApiModelProperty(value = "省份名称")
	private String provinceName;

	@ApiModelProperty(value = "老省份主键ID  老E3SV省份信息表主键ID")
	private String oldProvinceId;

	@ApiModelProperty(value = "DPS省份ID")
	private String oldDpsProvinceId;

	@ApiModelProperty(value = "厂商标识ID")
	private String oemId;

	@ApiModelProperty(value = "集团标识ID")
	private String groupId;

	@ApiModelProperty(value = "厂商标识")
	private String oemCode;

	@ApiModelProperty(value = "集团标识")
	private String groupCode;

	@ApiModelProperty(value = "创建人")
	private String creator;

	@ApiModelProperty(value = "创建人姓名")
	private String createdName;

	@ApiModelProperty(value = "最后更新人员")
	private String modifier;

	@ApiModelProperty(value = "修改人姓名")
	private String modifyName;

	@ApiModelProperty(value = "是否可用")
	private String isEnable;

	@ApiModelProperty(value = "SDP用户ID")
	private String sdpUserId;

	@ApiModelProperty(value = "SDP组织ID")
	private String sdpOrgId;

	@ApiModelProperty(value = "并发控制字段")
	private String updateControlId;

	@ApiModelProperty(value = "扩展字段1")
	private String column1;

	@ApiModelProperty(value = "扩展字段2")
	private String column2;

	@ApiModelProperty(value = "扩展字段3")
	private String column3;

	@ApiModelProperty(value = "扩展字段4")
	private String column4;

	@ApiModelProperty(value = "扩展字段5")
	private String column5;

	@ApiModelProperty(value = "扩展字段6")
	private String column6;

	@ApiModelProperty(value = "扩展字段7")
	private String column7;

	@ApiModelProperty(value = "扩展字段8")
	private String column8;

	@ApiModelProperty(value = "扩展字段9")
	private String column9;

	@ApiModelProperty(value = "扩展字段10")
	private String column10;

	public String getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getOldProvinceId() {
		return oldProvinceId;
	}

	public void setOldProvinceId(String oldProvinceId) {
		this.oldProvinceId = oldProvinceId;
	}

	public String getOldDpsProvinceId() {
		return oldDpsProvinceId;
	}

	public void setOldDpsProvinceId(String oldDpsProvinceId) {
		this.oldDpsProvinceId = oldDpsProvinceId;
	}

	public String getOemId() {
		return oemId;
	}

	public void setOemId(String oemId) {
		this.oemId = oemId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getOemCode() {
		return oemCode;
	}

	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifyName() {
		return modifyName;
	}

	public void setModifyName(String modifyName) {
		this.modifyName = modifyName;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getSdpUserId() {
		return sdpUserId;
	}

	public void setSdpUserId(String sdpUserId) {
		this.sdpUserId = sdpUserId;
	}

	public String getSdpOrgId() {
		return sdpOrgId;
	}

	public void setSdpOrgId(String sdpOrgId) {
		this.sdpOrgId = sdpOrgId;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

	public String getColumn1() {
		return column1;
	}

	public void setColumn1(String column1) {
		this.column1 = column1;
	}

	public String getColumn2() {
		return column2;
	}

	public void setColumn2(String column2) {
		this.column2 = column2;
	}

	public String getColumn3() {
		return column3;
	}

	public void setColumn3(String column3) {
		this.column3 = column3;
	}

	public String getColumn4() {
		return column4;
	}

	public void setColumn4(String column4) {
		this.column4 = column4;
	}

	public String getColumn5() {
		return column5;
	}

	public void setColumn5(String column5) {
		this.column5 = column5;
	}

	public String getColumn6() {
		return column6;
	}

	public void setColumn6(String column6) {
		this.column6 = column6;
	}

	public String getColumn7() {
		return column7;
	}

	public void setColumn7(String column7) {
		this.column7 = column7;
	}

	public String getColumn8() {
		return column8;
	}

	public void setColumn8(String column8) {
		this.column8 = column8;
	}

	public String getColumn9() {
		return column9;
	}

	public void setColumn9(String column9) {
		this.column9 = column9;
	}

	public String getColumn10() {
		return column10;
	}

	public void setColumn10(String column10) {
		this.column10 = column10;
	}

}
