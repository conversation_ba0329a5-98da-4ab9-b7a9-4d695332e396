package com.ly.adp.drive.idal.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.adp.drive.entities.SacTestDriveReviewRecord;

/**
 * <p>
 * 试乘试驾跟进记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
public interface SacTestDriveReviewRecordMapper extends BaseMapper<SacTestDriveReviewRecord> {
	
	/**
	 * 试乘试驾跟进记录查询
	 * @param page
	 * @param paramMap
	 * @return
	 */
	List <Map<String, Object>> sacTestDriveReviewRecordFindAll(IPage<Map<String, Object>> page, @Param("param")Map <String, Object> paramMap);
	
	/**
	 * 试乘试驾跟进记录新增
	 * @param paramMap
	 * @return
	 */
    int createTestDriveReviewRecordInfo(@Param("param") Map <String, Object> paramMap);
}
