package com.ly.adp.drive.service;

import com.ly.adp.drive.entities.TestDriveVehicleData;
import com.ly.adp.drive.entities.req.GetBiVehicleDataJobReq;
import com.ly.adp.drive.entities.vo.GetBiVehicleDataResultVO;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import java.util.List;
import java.util.Map;

/**
 * 车机数据Service
 * <AUTHOR>
 * @Version 1.0.0
 **/
public interface IVehicleDataService {
    ListResult<GetBiVehicleDataResultVO> getBiVehicleData(GetBiVehicleDataJobReq req, String token);

    EntityResult<VehicleDataVO> queryVehicleData(String testDriveOrderNo);

    VehicleDataVO convertToVehicleDataVO(TestDriveVehicleData data);

    Map<String, VehicleDataVO> queryVehicleDataMap(List<String> testDriveOrderNoList);
}
