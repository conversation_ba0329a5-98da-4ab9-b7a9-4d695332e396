package com.ly.adp.drive.entities.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;


@ApiModel("试驾单车机数据VO")
public class VehicleDataVO {
    @ApiModelProperty(value = "试驾单号")
    private String testDriveOrderNo;

    @ApiModelProperty(value = "是否有效试驾 1-是，0-否")
    private Integer isValidTestDrive;

    @ApiModelProperty(value = "开始试驾电量(车机)")
    private String vehStartVoc;

    @ApiModelProperty(value = "试驾开始里程 (车机)")
    private String vehStartMileage;

    @ApiModelProperty(value = "试驾结束里程 (车机)")
    private String vehEndMileage;

    @ApiModelProperty(value = "试驾行驶里程(车机)")
    private String vehTotalMileage;

    @ApiModelProperty(value = "试驾行驶时长(车机)")
    private String vehTotalDur;

    @ApiModelProperty(value = "试驾最高时速(车机)")
    private String vehMaxSpeed;

    @ApiModelProperty(value = "试驾平均时速(车机)")
    private String testDriveAvgSpeed;

    @ApiModelProperty(value = "是否有效试驾名称 1-是，0-否")
    private String isValidTestDriveCn;

    /**
     * 是否优秀试驾：0=否；1=是
     */
    @JsonProperty("is_good_test_drive")
    private String isGoodTestDrive;

    /**
     * 是否匹配试驾录音：0=否；1=是
     */
    @JsonProperty("is_match_record")
    private String isMatchRecord;

    /**
     * 是否优秀试驾：0=否；1=是
     */
    private String isGoodTestDriveCn;

    /**
     * 是否匹配试驾录音：0=否；1=是
     */
    private String isMatchRecordCn;

    /**
     * 试驾录音时间
     */
    @JsonProperty("record_time")
    private String recordTime;

    /**
     * 试驾录音时长，单位秒
     */
    @JsonProperty("record_duration")
    private String recordDuration;

    /**
     * 试驾得分
     */
    @JsonProperty("record_score")
    private String recordScore;

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public Integer getIsValidTestDrive() {
        return isValidTestDrive;
    }

    public void setIsValidTestDrive(Integer isValidTestDrive) {
        this.isValidTestDrive = isValidTestDrive;
    }

    public String getVehStartVoc() {
        return vehStartVoc;
    }

    public void setVehStartVoc(String vehStartVoc) {
        this.vehStartVoc = vehStartVoc;
    }

    public String getVehStartMileage() {
        return vehStartMileage;
    }

    public void setVehStartMileage(String vehStartMileage) {
        this.vehStartMileage = vehStartMileage;
    }

    public String getVehEndMileage() {
        return vehEndMileage;
    }

    public void setVehEndMileage(String vehEndMileage) {
        this.vehEndMileage = vehEndMileage;
    }

    public String getVehTotalMileage() {
        return vehTotalMileage;
    }

    public void setVehTotalMileage(String vehTotalMileage) {
        this.vehTotalMileage = vehTotalMileage;
    }

    public String getVehTotalDur() {
        return vehTotalDur;
    }

    public void setVehTotalDur(String vehTotalDur) {
        this.vehTotalDur = vehTotalDur;
    }

    public String getVehMaxSpeed() {
        return vehMaxSpeed;
    }

    public void setVehMaxSpeed(String vehMaxSpeed) {
        this.vehMaxSpeed = vehMaxSpeed;
    }

    public String getTestDriveAvgSpeed() {
        return testDriveAvgSpeed;
    }

    public void setTestDriveAvgSpeed(String testDriveAvgSpeed) {
        this.testDriveAvgSpeed = testDriveAvgSpeed;
    }

    public String getIsValidTestDriveCn() {
        return isValidTestDriveCn;
    }

    public void setIsValidTestDriveCn(String isValidTestDriveCn) {
        this.isValidTestDriveCn = isValidTestDriveCn;
    }

    public String getIsGoodTestDriveCn() {
        return isGoodTestDriveCn;
    }

    public void setIsGoodTestDriveCn(String isGoodTestDriveCn) {
        this.isGoodTestDriveCn = isGoodTestDriveCn;
    }

    public String getIsMatchRecordCn() {
        return isMatchRecordCn;
    }

    public void setIsMatchRecordCn(String isMatchRecordCn) {
        this.isMatchRecordCn = isMatchRecordCn;
    }

    public String getIsGoodTestDrive() {
        return isGoodTestDrive;
    }

    public void setIsGoodTestDrive(String isGoodTestDrive) {
        this.isGoodTestDrive = isGoodTestDrive;
    }

    public String getIsMatchRecord() {
        return isMatchRecord;
    }

    public void setIsMatchRecord(String isMatchRecord) {
        this.isMatchRecord = isMatchRecord;
    }

    public String getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }

    public String getRecordDuration() {
        return recordDuration;
    }

    public void setRecordDuration(String recordDuration) {
        this.recordDuration = recordDuration;
    }

    public String getRecordScore() {
        return recordScore;
    }

    public void setRecordScore(String recordScore) {
        this.recordScore = recordScore;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("testDriveOrderNo", testDriveOrderNo);
        map.put("isValidTestDrive", isValidTestDrive);
        map.put("isValidTestDriveCn", isValidTestDriveCn);
        map.put("vehStartVoc", vehStartVoc);
        map.put("vehStartMileage", vehStartMileage);
        map.put("vehEndMileage", vehEndMileage);
        map.put("vehTotalMileage", vehTotalMileage);
        map.put("vehTotalDur", vehTotalDur);
        map.put("vehMaxSpeed", vehMaxSpeed);
        map.put("testDriveAvgSpeed", testDriveAvgSpeed);
        map.put("isGoodTestDrive", isGoodTestDrive);
        map.put("isMatchRecord", isMatchRecord);
        map.put("isGoodTestDriveCn", isGoodTestDriveCn);
        map.put("isMatchRecordCn", isMatchRecordCn);
        map.put("recordTime", recordTime);
        map.put("recordDuration", recordDuration);
        map.put("recordScore", recordScore);
        return map;
    }

    public static VehicleDataVO getDefaultInstance(String testDriveOrderNo) {
        VehicleDataVO vo = new VehicleDataVO();
        vo.setTestDriveOrderNo(testDriveOrderNo);
        vo.setIsValidTestDriveCn("计算中");
        vo.setVehStartVoc("计算中");
        vo.setVehStartMileage("计算中");
        vo.setVehEndMileage("计算中");
        vo.setVehTotalMileage("计算中");
        vo.setVehTotalDur("计算中");
        vo.setVehMaxSpeed("计算中");
        vo.setTestDriveAvgSpeed("计算中");
        vo.setIsGoodTestDriveCn("计算中");
        vo.setIsMatchRecordCn("计算中");
        vo.setRecordTime("计算中");
        vo.setRecordDuration("计算中");
        vo.setRecordScore("计算中");
        return vo;
    }

    @Override
    public String toString() {
        return "VehicleDataVO{" +
                "testDriveOrderNo='" + testDriveOrderNo + '\'' +
                ", isValidTestDrive=" + isValidTestDrive +
                ", vehStartVoc='" + vehStartVoc + '\'' +
                ", vehStartMileage='" + vehStartMileage + '\'' +
                ", vehEndMileage='" + vehEndMileage + '\'' +
                ", vehTotalMileage='" + vehTotalMileage + '\'' +
                ", vehTotalDur='" + vehTotalDur + '\'' +
                ", vehMaxSpeed='" + vehMaxSpeed + '\'' +
                ", testDriveAvgSpeed='" + testDriveAvgSpeed + '\'' +
                ", isValidTestDriveCn='" + isValidTestDriveCn + '\'' +
                ", isGoodTestDrive='" + isGoodTestDrive + '\'' +
                ", isMatchRecord='" + isMatchRecord + '\'' +
                ", isGoodTestDriveCn='" + isGoodTestDriveCn + '\'' +
                ", isMatchRecordCn='" + isMatchRecordCn + '\'' +
                ", recordTime='" + recordTime + '\'' +
                ", recordDuration='" + recordDuration + '\'' +
                ", recordScore='" + recordScore + '\'' +
                '}';
    }
}
