package com.ly.adp.drive.entities.out;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/16
 */
public class ProductSpecialistVO implements Serializable {

    private static final long serialVersionUID = 3418577417749783728L;

    @ApiModelProperty("产品专家ID")
    private String userId;

    @ApiModelProperty("产品专家")
    private String empName;

    @ApiModelProperty("产品专家电话")
    private String mobile;

    @ApiModelProperty("产品专家岗位")
    private String stationName;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    @Override
    public String toString() {
        return "ProductSpecialistVO{" +
                "userId='" + userId + '\'' +
                ", empName='" + empName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", stationName='" + stationName + '\'' +
                '}';
    }
}
