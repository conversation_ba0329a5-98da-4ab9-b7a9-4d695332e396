package com.ly.adp.drive.entities;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/5/12
 */
public class LookupValue implements Serializable {

    private static final long serialVersionUID = 5763688742485593385L;

    @ApiModelProperty("值编码")
    private String lookupValueCode;

    @ApiModelProperty("值名称")
    private String lookupValueName;

    @ApiModelProperty("是否启用")
    private String isEnable;

    public String getLookupValueCode() {
        return lookupValueCode;
    }

    public void setLookupValueCode(String lookupValueCode) {
        this.lookupValueCode = lookupValueCode;
    }

    public String getLookupValueName() {
        return lookupValueName;
    }

    public void setLookupValueName(String lookupValueName) {
        this.lookupValueName = lookupValueName;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
}
