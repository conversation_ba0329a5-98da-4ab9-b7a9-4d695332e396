package com.ly.adp.drive.otherservice;

import java.util.Map;

import com.ly.mp.component.entities.OptResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

@FeignClient(name = "${refer.name.adp.csc:ly.adp.clue}", url = "${refer.url.adp.clue}")
public interface ICscClueFeignClient {
    @PostMapping("/ly/sac/cluedlr/cluedlrcheckrepeat.do")
    EntityResult<Map<String, Object>> clueDlrCheckRepeat(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    @PostMapping("/ly/sac/cluedlr/cluedlrsave.do")
    EntityResult<Map<String, Object>> clueDlrSave(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> queryCondition);

    // 内置表配置查询初始化代理商
    @PostMapping(value = "/ly/sac/innerconfig/queryConfigList.do")
    ListResult<Map<String, Object>> queryConfigList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo);

    @PostMapping(value = "/ly/sac/systemconfigvalue/querybycode.do")
    ListResult<Map<String, Object>> queryListSysteConfigValueByCode(
            @RequestHeader(name = "authorization", required = false) String token,
            @RequestBody(required = false) ParamPage<Map<String, Object>> dataInfo);

    @PostMapping(value = "/ly/adp/csc/msgrecord/msgrecordsave.do")
    OptResult sacMsgRecordSaveInfo(
            @RequestHeader(com.google.common.net.HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> dateInfo);
}
