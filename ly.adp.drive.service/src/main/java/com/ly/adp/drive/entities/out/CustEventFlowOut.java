package com.ly.adp.drive.entities.out;

import com.alibaba.fastjson.JSONObject;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 客户事件流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
public class CustEventFlowOut {

    /**
     * 客户 ID
     */
    private String custId;

    /**
     * 事件类型
     */
    private Integer type;

    /**
     * 业务单据 ID
     */
    private String businessId;

    /**
     * 事件时间
     */
    private String eventTime;

    /**
     * 扩展信息
     */
    private JSONObject extendJson;

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getEventTime() {
        return eventTime;
    }

    public void setEventTime(String eventTime) {
        this.eventTime = eventTime;
    }

    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                                          .format(eventTime);;
    }

    public JSONObject getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(JSONObject extendJson) {
        this.extendJson = extendJson;
    }
}
