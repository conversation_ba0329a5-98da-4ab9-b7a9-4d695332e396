package com.ly.adp.drive.otherservice.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.ly.mp.busi.base.handler.MapUtil;
import com.ly.mp.component.entities.ListResult;

public class ListResultUtil {

	/**
	 * ListResult中的对象转换成Map对象
	 * @param <T>
	 * @param listResult
	 * @param rows
	 * @return
	 */
	public static<T> ListResult<Map<String, Object>> ListResultOfMap(ListResult<T> listResult) {
		List<Map<String, Object>> newList = new ArrayList<>();
		listResult.getRows().forEach(item->{
			newList.add(MapUtil.convertToMap(item));
		});
		ListResult<Map<String, Object>> newListResult = new ListResult<Map<String,Object>>();
		newListResult.setMsg(listResult.getMsg());
		newListResult.setPageindex(listResult.getPageindex());
		newListResult.setPages(listResult.getPages());
		newListResult.setRecords(listResult.getRecords());
		newListResult.setResult(listResult.getResult());
		newListResult.setRows(newList);
		return newListResult;
	}
	
}
