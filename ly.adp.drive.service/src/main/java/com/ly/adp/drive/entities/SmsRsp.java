package com.ly.adp.drive.entities;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/5/15
 */
public class SmsRsp implements Serializable {

    private static final long serialVersionUID = -6158028844606207788L;

    String code;
    String message;
    String requestId;
    Map<String, String> data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Map<String, String> getData() {
        return data;
    }

    public void setData(Map<String, String> data) {
        this.data = data;
    }
}
