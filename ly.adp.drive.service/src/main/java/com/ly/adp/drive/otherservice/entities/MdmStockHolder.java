package com.ly.adp.drive.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 股东信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@TableName("t_usc_mdm_stock_holder")
public class MdmStockHolder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("STOCK_HOLDER_ID")
    private String stockHolderId;

    /**
     * 股东名称
     */
    @TableField("STOCK_HOLDER")
    private String stockHolder;

    /**
     * 占比率
     */
    @TableField("STOCK_HOLDER_RATE")
    private String stockHolderRate;

    /**
     * 城市ID/代理/门店 id
     */
    @TableField("INFO_ID")
    private String infoId;

    /**
     * 排序字段
     */
    @TableField("SORTING")
    private String sorting;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getStockHolderId() {
        return stockHolderId;
    }

    public void setStockHolderId(String stockHolderId) {
        this.stockHolderId = stockHolderId;
    }

    public String getStockHolder() {
        return stockHolder;
    }

    public void setStockHolder(String stockHolder) {
        this.stockHolder = stockHolder;
    }

    public String getStockHolderRate() {
        return stockHolderRate;
    }

    public void setStockHolderRate(String stockHolderRate) {
        this.stockHolderRate = stockHolderRate;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public String getSorting() {
        return sorting;
    }

    public void setSorting(String sorting) {
        this.sorting = sorting;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "MdmStockHolder{" +
        "stockHolderId=" + stockHolderId +
        ", stockHolder=" + stockHolder +
        ", stockHolderRate=" + stockHolderRate +
        ", infoId=" + infoId +
        ", sorting=" + sorting +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
