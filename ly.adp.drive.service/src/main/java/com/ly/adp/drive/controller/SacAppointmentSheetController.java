package com.ly.adp.drive.controller;


import com.ly.adp.drive.entities.req.TestDriveCheckInReq;
import com.ly.adp.drive.service.ISacAppointmentSheetService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busi.base.context.BusicenInvoker;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 试乘试驾预约单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Api(value = "试乘试驾预约单管理", tags = {"试乘试驾预约单管理"})
@RestController
@RequestMapping("/ly/tdm/sacappointmentsheet")
public class SacAppointmentSheetController {

    @Autowired
    ISacAppointmentSheetService sacAppointmentSheetService;

    @ApiOperation(value = "试乘试驾预约单查询", notes = "试乘试驾预约单查询")
    @RequestMapping(value = "/appointmentsheetquerylist.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> appointmentSheetQueryList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacAppointmentSheetService.appointmentSheetQueryList(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾预约单保存", notes = "试乘试驾预约单保存")
    @RequestMapping(value = "/appointmentsheetsave.do", method = RequestMethod.POST)
    public EntityResult<Map<String, Object>> appointmentSheetSave(@RequestHeader(name = "authorization", required = false) String authentication,
                                                                  @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doEntity(() -> sacAppointmentSheetService.appointmentSheetSave(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试乘试驾预约单取消", notes = "试乘试驾预约单取消")
    @RequestMapping(value = "/appointmentsheetcancel.do", method = RequestMethod.POST)
    public OptResult appointmentSheetCance(@RequestHeader(name = "authorization", required = false) String authentication,
                                           @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doOpt(() -> sacAppointmentSheetService.appointmentSheetCancel(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试乘试驾预约单取消APP", notes = "试乘试驾预约单取消APP")
    @RequestMapping(value = "/appointmentsheetcancel.todo", method = RequestMethod.POST)
    public OptResult appointmentSheetCanceApp(
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        return BusicenInvoker.doOpt(() -> sacAppointmentSheetService.appointmentSheetCancelApp(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试驾任务取消", notes = "试驾任务取消")
    @RequestMapping(value = "/cancellationOfTestDriveTask.do", method = RequestMethod.POST)
    public OptResult cancellationOfTestDriveTask(
            @RequestBody(required = false) Map<String, Object> mapParam) {
        return sacAppointmentSheetService.cancellationOfTestDriveTask(mapParam);
    }

    @ApiOperation(value = "试乘试驾容量查询", notes = "试乘试驾容量查询")
    @RequestMapping(value = "/sactestdrivecapacityquerylist.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveCapacityQueryList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacAppointmentSheetService.sacTestDriveCapacityQueryList(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾容量查询", notes = "试乘试驾容量查询")
    @RequestMapping(value = "/queryTestdriveCapacityList.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveCapacityQueryListNew(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacAppointmentSheetService.queryTestdriveCapacityList(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾容量查询APP", notes = "试乘试驾容量查询APP")
    @RequestMapping(value = "/sactestdrivecapacityquerylist.todo", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveCapacityQueryListApp(
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        return BusicenInvoker.doList(() -> sacAppointmentSheetService.sacTestDriveCapacityQueryListApp(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾预约跟进发送短信", notes = "试乘试驾预约跟进发送短信")
    @RequestMapping(value = "/sactestdrivecapacitysendmessage.do", method = RequestMethod.POST)
    public OptResult sactestdrivecapacitysendmessage(@RequestHeader(name = "authorization", required = false) String authentication,
                                                     @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doOpt(() -> sacAppointmentSheetService.sactestdrivecapacitysendmessage(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试驾签到", notes = "试驾签到")
    @RequestMapping(value = "/testDriveCheckIn.do", method = RequestMethod.POST)
    public OptResult testDriveCheckIn(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) TestDriveCheckInReq testDriveCheckInReq) {
        return sacAppointmentSheetService.testDriveCheckIn(authentication, testDriveCheckInReq);
    }
}

