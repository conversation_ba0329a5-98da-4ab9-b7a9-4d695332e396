package com.ly.adp.drive.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.adp.drive.entities.TSwitch;
import com.ly.adp.drive.idal.mapper.SwitchMapper;
import com.ly.adp.drive.service.ITSwitchService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 开关
 * @Author: rik.ren
 * @Date: 2025/1/13 17:30
 **/
@Service
public class SwitchService extends ServiceImpl<SwitchMapper, TSwitch> implements ITSwitchService {
    private Logger log = LoggerFactory.getLogger(SwitchService.class);

    @Resource
    private SwitchMapper switchMapper;

    @Override
    public Boolean saveSwitch(TSwitch param) {
        QueryWrapper<TSwitch> queryWrapper = param.buildQueryWrapper();
        List<TSwitch> tSwitchesResult = switchMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(tSwitchesResult)) {
            LambdaUpdateWrapper<TSwitch> updateWrapper = param.buildUpdate();
            return switchMapper.update(null, updateWrapper) > 0;
        } else {
            return switchMapper.insert(param) > 0;
        }
    }

    @Override
    public TSwitch querySwitch(TSwitch param) {
        QueryWrapper<TSwitch> queryWrapper = param.buildQueryWrapper();
        List<TSwitch> tSwitchesResult = switchMapper.selectList(queryWrapper);
        return CollectionUtils.isNotEmpty(tSwitchesResult) ? tSwitchesResult.get(0) : null;
    }
}
