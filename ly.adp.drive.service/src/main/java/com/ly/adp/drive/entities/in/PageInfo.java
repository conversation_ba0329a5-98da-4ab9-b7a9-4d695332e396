package com.ly.adp.drive.entities.in;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class PageInfo implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "当前页数", required = false, example = "")
	private int pageIndex;

	@ApiModelProperty(value = "每页大小", required = false, example = "")
	private int pageSize;

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

}
