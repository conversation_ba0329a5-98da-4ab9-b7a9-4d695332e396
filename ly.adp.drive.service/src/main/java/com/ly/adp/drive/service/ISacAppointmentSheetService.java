package com.ly.adp.drive.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.drive.entities.SacAppointmentSheet;
import com.ly.adp.drive.entities.req.TestDriveCheckInReq;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import java.util.Map;

/**
 * <p>
 * 试乘试驾预约单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface ISacAppointmentSheetService extends IService<SacAppointmentSheet> {
	
	/**
	 * 试乘试驾预约单查询
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String, Object>> appointmentSheetQueryList(ParamPage<Map<String, Object>>mapParam);
	
	/**
	 * 试乘试驾预约单保存
	 * @param mapParam
	 * @return
	 */
	public EntityResult<Map<String, Object>> appointmentSheetSave(Map<String, Object> mapParam);
	/**
	 * 试乘试驾预约单保存，优化
	 * @param mapParam
	 * @return
	 */
	public void appointmentSheetSave_performance(Map<String, Object> mapParam);

	/**
	 * 生成单号
	 * @param mapParam
	 */
	void setOrderCode(Map<String, Object> mapParam);
	
	/**
	 * 试乘试驾单取消
	 * @param mapParam
	 * @return
	 */
	public OptResult appointmentSheetCancel(Map<String, Object> mapParam);
	/**
	 * 试乘试驾单取消
	 * @param mapParam
	 * @return
	 */
	public OptResult appointmentSheetCancelApp(Map<String, Object> mapParam);
	
	/**
	 * 试驾车容量查询
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String, Object>> sacTestDriveCapacityQueryList(ParamPage<Map<String, Object>>mapParam);

	/**
	 * 试驾车容量查询
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String, Object>> queryTestdriveCapacityList(ParamPage<Map<String, Object>>mapParam);
	
	/**
	 * 试驾车容量查询
	 * @param mapParam
	 * @return
	 */
	public ListResult<Map<String, Object>> sacTestDriveCapacityQueryListApp(ParamPage<Map<String, Object>>mapParam);

	/**
	 * 试乘试驾预约跟进发送短信
	 * @param mapParam
	 * @return
	 */
	public OptResult sactestdrivecapacitysendmessage(Map<String, Object> mapParam);

    OptResult cancellationOfTestDriveTask(Map<String, Object> mapParam);

	OptResult testDriveCheckIn(String authentication, TestDriveCheckInReq testDriveCheckInReq);

	void checkCustomerClueNew(Map<String, Object> mapParam);
}
