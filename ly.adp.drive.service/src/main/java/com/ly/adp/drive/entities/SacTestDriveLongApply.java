package com.ly.adp.drive.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 超长试驾申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@TableName("t_sac_test_drive_long_apply")
public class SacTestDriveLongApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @TableId("APPLY_ID")
    private String applyId;
    
    /**
     * 申请时长
     */
    @TableId("APPLY_TIEM_LONG")
    private String applyTimeLong;
    
    /**
     * 车牌号
     */
    @TableField("CAR_LICENCE_NO")
    private String carLicenceNo;

    /**
     * VIN码
     */
    @TableField("VIN")
    private String vin;

    /**
     * 车型编码
     */
    @TableField("CAR_TYPE_CODE")
    private String carTypeCode;

    /**
     * 车型名称
     */
    @TableField("CAR_TYPE_NAME")
    private String carTypeName;

    /**
     * 配置编码
     */
    @TableField("CARTYPE_CONFIG_CODE")
    private String cartypeConfigCode;

    /**
     * 配置名称
     */
    @TableField("CARTYPE_CONFIG_NAME")
    private String cartypeConfigName;

    /**
     * 颜色编码
     */
    @TableField("CAR_COLOR_CODE")
    private String carColorCode;

    /**
     * 颜色名称
     */
    @TableField("CAR_COLOR_NAME")
    private String carColorName;

    /**
     * 内饰编码
     */
    @TableField("CAR_INCOLOR_CODE")
    private String carIncolorCode;

    /**
     * 内饰名称
     */
    @TableField("CAR_INCOLOR_NAME")
    private String carIncolorName;

    /**
     * 可试驾天数
     */
    @TableField("CAN_TEST_DATE")
    private String canTestDate;

    /**
     * 申请门店
     */
    @TableField("APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 申请原因
     */
    @TableField("APPLY_REASON")
    private String applyReason;

    /**
     * 申请开始时间
     */
    @TableField("APPLY_TIME_BEGIN")
    private LocalDateTime applyTimeBegin;

    /**
     * 申请结束时间
     */
    @TableField("APPLY_TIME_END")
    private LocalDateTime applyTimeEnd;

    /**
     * 审批人编码
     */
    @TableField("AUDIT_USER_ID")
    private String auditUserId;

    /**
     * 审批人名称
     */
    @TableField("AUDIT_EMP_NAME")
    private String auditEmpName;

    /**
     * 审批类型(1店长，2区域经理)
     */
    @TableField("AUDIT_TYPE")
    private String auditType;

    /**
     * 审批状态(通过/驳回)
     */
    @TableField("AUDIT_STATUS")
    private String auditStatus;
    
    /**
     * 审批状态(通过/驳回)
     */
    @TableField("AUDIT_STATUS_NAME")
    private String auditStatusName;

    /**
     * 审批意见
     */
    @TableField("AUDIT_REASON")
    private String auditReason;

    /**
     * 审批日期
     */
    @TableField("AUDIT_DATE")
    private LocalDateTime auditDate;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }
    public String getCarLicenceNo() {
        return carLicenceNo;
    }

    public void setCarLicenceNo(String carLicenceNo) {
        this.carLicenceNo = carLicenceNo;
    }
    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }
    public String getCarTypeCode() {
        return carTypeCode;
    }

    public void setCarTypeCode(String carTypeCode) {
        this.carTypeCode = carTypeCode;
    }
    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }
    public String getCartypeConfigCode() {
        return cartypeConfigCode;
    }

    public void setCartypeConfigCode(String cartypeConfigCode) {
        this.cartypeConfigCode = cartypeConfigCode;
    }
    public String getCartypeConfigName() {
        return cartypeConfigName;
    }

    public void setCartypeConfigName(String cartypeConfigName) {
        this.cartypeConfigName = cartypeConfigName;
    }
    public String getCarColorCode() {
        return carColorCode;
    }

    public void setCarColorCode(String carColorCode) {
        this.carColorCode = carColorCode;
    }
    public String getCarColorName() {
        return carColorName;
    }

    public void setCarColorName(String carColorName) {
        this.carColorName = carColorName;
    }
    public String getCarIncolorCode() {
        return carIncolorCode;
    }

    public void setCarIncolorCode(String carIncolorCode) {
        this.carIncolorCode = carIncolorCode;
    }
    public String getCarIncolorName() {
        return carIncolorName;
    }

    public void setCarIncolorName(String carIncolorName) {
        this.carIncolorName = carIncolorName;
    }
    public String getCanTestDate() {
        return canTestDate;
    }

    public void setCanTestDate(String canTestDate) {
        this.canTestDate = canTestDate;
    }
    public String getApplyDlrCode() {
        return applyDlrCode;
    }

    public void setApplyDlrCode(String applyDlrCode) {
        this.applyDlrCode = applyDlrCode;
    }
    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }
    public LocalDateTime getApplyTimeBegin() {
        return applyTimeBegin;
    }

    public void setApplyTimeBegin(LocalDateTime applyTimeBegin) {
        this.applyTimeBegin = applyTimeBegin;
    }
    public LocalDateTime getApplyTimeEnd() {
        return applyTimeEnd;
    }

    public void setApplyTimeEnd(LocalDateTime applyTimeEnd) {
        this.applyTimeEnd = applyTimeEnd;
    }
    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }
    public String getAuditEmpName() {
        return auditEmpName;
    }

    public void setAuditEmpName(String auditEmpName) {
        this.auditEmpName = auditEmpName;
    }
    public String getAuditType() {
        return auditType;
    }

    public void setAuditType(String auditType) {
        this.auditType = auditType;
    }
    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }
    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }
    public LocalDateTime getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(LocalDateTime auditDate) {
        this.auditDate = auditDate;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }
    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }
    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }
    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }
    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SlTSacTestDriveLongApply{" +
        "applyId=" + applyId +
        ", carLicenceNo=" + carLicenceNo +
        ", vin=" + vin +
        ", carTypeCode=" + carTypeCode +
        ", carTypeName=" + carTypeName +
        ", cartypeConfigCode=" + cartypeConfigCode +
        ", cartypeConfigName=" + cartypeConfigName +
        ", carColorCode=" + carColorCode +
        ", carColorName=" + carColorName +
        ", carIncolorCode=" + carIncolorCode +
        ", carIncolorName=" + carIncolorName +
        ", canTestDate=" + canTestDate +
        ", applyDlrCode=" + applyDlrCode +
        ", applyReason=" + applyReason +
        ", applyTimeBegin=" + applyTimeBegin +
        ", applyTimeEnd=" + applyTimeEnd +
        ", auditUserId=" + auditUserId +
        ", auditEmpName=" + auditEmpName +
        ", auditType=" + auditType +
        ", auditStatus=" + auditStatus +
        ", auditReason=" + auditReason +
        ", auditDate=" + auditDate +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", column6=" + column6 +
        ", column7=" + column7 +
        ", column8=" + column8 +
        ", column9=" + column9 +
        ", column10=" + column10 +
        ", mycatOpTime=" + mycatOpTime +
        ", oemId=" + oemId +
        ", groupId=" + groupId +
        ", oemCode=" + oemCode +
        ", groupCode=" + groupCode +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", sdpUserId=" + sdpUserId +
        ", sdpOrgId=" + sdpOrgId +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
