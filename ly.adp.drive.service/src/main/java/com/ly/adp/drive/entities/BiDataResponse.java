package com.ly.adp.drive.entities;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class BiDataResponse {
    private String requestId;
    private String errCode;
    private String errMsg;

    @JsonProperty("data")
    private List<TestDriveBiData> data;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public List<TestDriveBiData> getData() {
        return data;
    }

    public void setData(List<TestDriveBiData> data) {
        this.data = data;
    }

    public static class TestDriveBiData {
        @JsonProperty("the_date")
        private String theDate;

        @JsonProperty("veh_total_dur")
        private String vehTotalDur;

        @JsonProperty("veh_total_mileage")
        private String vehTotalMileage;

        @JsonProperty("agent_name")
        private String agentName;

        @JsonProperty("test_drive_method")
        private String testDriveMethod;

        @JsonProperty("end_time")
        private String endTime;

        @JsonProperty("is_valid_test_drive_code")
        private int isValidTestDriveCode;

        @JsonProperty("dlr_code")
        private String dlrCode;

        @JsonProperty("is_valid_test_drive")
        private String isValidTestDrive;

        @JsonProperty("dlr_name")
        private String dlrName;

        @JsonProperty("veh_max_speed")
        private String vehMaxSpeed;

        @JsonProperty("test_type")
        private String testType;

        @JsonProperty("veh_start_mileage")
        private String vehStartMileage;

        @JsonProperty("start_time")
        private String startTime;

        @JsonProperty("car_vin")
        private String carVin;

        @JsonProperty("city_name")
        private String cityName;

        @JsonProperty("veh_end_mileage")
        private String vehEndMileage;

        @JsonProperty("sales_consultant_name")
        private String salesConsultantName;

        @JsonProperty("veh_start_voc")
        private String vehStartVoc;

        @JsonProperty("big_area_name")
        private String bigAreaName;

        @JsonProperty("customer_name")
        private String customerName;

        @JsonProperty("test_drive_avg_speed")
        private String testDriveAvgSpeed;

        @JsonProperty("test_drive_order_no")
        private String testDriveOrderNo;

        /**
         * 是否优秀试驾：0=否；1=是
         */
        @JsonProperty("is_good_test_drive")
        private Integer isGoodTestDrive;

        /**
         * 是否匹配试驾录音：0=否；1=是
         */
        @JsonProperty("is_match_record")
        private Integer isMatchRecord;

        /**
         * 试驾录音时间
         */
        @JsonProperty("record_time")
        private LocalDateTime recordTime;

        /**
         * 试驾录音时长，单位秒
         */
        @JsonProperty("record_duration")
        private BigDecimal recordDuration;

        /**
         * 试驾得分
         */
        @JsonProperty("record_score")
        private BigDecimal recordScore;

        public String getTheDate() {
            return theDate;
        }

        public void setTheDate(String theDate) {
            this.theDate = theDate;
        }

        public String getVehTotalDur() {
            return vehTotalDur;
        }

        public void setVehTotalDur(String vehTotalDur) {
            this.vehTotalDur = vehTotalDur;
        }

        public String getVehTotalMileage() {
            return vehTotalMileage;
        }

        public void setVehTotalMileage(String vehTotalMileage) {
            this.vehTotalMileage = vehTotalMileage;
        }

        public String getAgentName() {
            return agentName;
        }

        public void setAgentName(String agentName) {
            this.agentName = agentName;
        }

        public String getTestDriveMethod() {
            return testDriveMethod;
        }

        public void setTestDriveMethod(String testDriveMethod) {
            this.testDriveMethod = testDriveMethod;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public int getIsValidTestDriveCode() {
            return isValidTestDriveCode;
        }

        public void setIsValidTestDriveCode(int isValidTestDriveCode) {
            this.isValidTestDriveCode = isValidTestDriveCode;
        }

        public String getDlrCode() {
            return dlrCode;
        }

        public void setDlrCode(String dlrCode) {
            this.dlrCode = dlrCode;
        }

        public String getIsValidTestDrive() {
            return isValidTestDrive;
        }

        public void setIsValidTestDrive(String isValidTestDrive) {
            this.isValidTestDrive = isValidTestDrive;
        }

        public String getDlrName() {
            return dlrName;
        }

        public void setDlrName(String dlrName) {
            this.dlrName = dlrName;
        }

        public String getVehMaxSpeed() {
            return vehMaxSpeed;
        }

        public void setVehMaxSpeed(String vehMaxSpeed) {
            this.vehMaxSpeed = vehMaxSpeed;
        }

        public String getTestType() {
            return testType;
        }

        public void setTestType(String testType) {
            this.testType = testType;
        }

        public String getVehStartMileage() {
            return vehStartMileage;
        }

        public void setVehStartMileage(String vehStartMileage) {
            this.vehStartMileage = vehStartMileage;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getCarVin() {
            return carVin;
        }

        public void setCarVin(String carVin) {
            this.carVin = carVin;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public String getVehEndMileage() {
            return vehEndMileage;
        }

        public void setVehEndMileage(String vehEndMileage) {
            this.vehEndMileage = vehEndMileage;
        }

        public String getSalesConsultantName() {
            return salesConsultantName;
        }

        public void setSalesConsultantName(String salesConsultantName) {
            this.salesConsultantName = salesConsultantName;
        }

        public String getVehStartVoc() {
            return vehStartVoc;
        }

        public void setVehStartVoc(String vehStartVoc) {
            this.vehStartVoc = vehStartVoc;
        }

        public String getBigAreaName() {
            return bigAreaName;
        }

        public void setBigAreaName(String bigAreaName) {
            this.bigAreaName = bigAreaName;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getTestDriveAvgSpeed() {
            return testDriveAvgSpeed;
        }

        public void setTestDriveAvgSpeed(String testDriveAvgSpeed) {
            this.testDriveAvgSpeed = testDriveAvgSpeed;
        }

        public String getTestDriveOrderNo() {
            return testDriveOrderNo;
        }

        public void setTestDriveOrderNo(String testDriveOrderNo) {
            this.testDriveOrderNo = testDriveOrderNo;
        }

        public Integer getIsGoodTestDrive() {
            return isGoodTestDrive;
        }

        public void setIsGoodTestDrive(Integer isGoodTestDrive) {
            this.isGoodTestDrive = isGoodTestDrive;
        }

        public Integer getIsMatchRecord() {
            return isMatchRecord;
        }

        public void setIsMatchRecord(Integer isMatchRecord) {
            this.isMatchRecord = isMatchRecord;
        }

        public LocalDateTime getRecordTime() {
            return recordTime;
        }

        public void setRecordTime(LocalDateTime recordTime) {
            this.recordTime = recordTime;
        }

        public BigDecimal getRecordDuration() {
            return recordDuration;
        }

        public void setRecordDuration(BigDecimal recordDuration) {
            this.recordDuration = recordDuration;
        }

        public BigDecimal getRecordScore() {
            return recordScore;
        }

        public void setRecordScore(BigDecimal recordScore) {
            this.recordScore = recordScore;
        }

    }
}