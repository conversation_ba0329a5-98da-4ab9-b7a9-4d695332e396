package com.ly.adp.drive.helper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ly.adp.drive.otherservice.IBasedataFeignClient;
import com.ly.adp.drive.otherservice.ICscClueFeignClient;
import com.ly.mp.bucn.pack.entity.ParamPage;


/**
 * 缓存数据
 * <AUTHOR>
 *
 */
@Component
public class CacheDataFactory {
	@Autowired
	ICscClueFeignClient sacSystemConfigValueService;
	
	
	/**
	 * 获取系统配置信息
	 * @param configCode 配置项编码
	 * @param orgCode 组织编码
	 * @param token
	 * @return
	 */
	public String querySysConfigValue(String configCode,String token){
		try{
			ParamPage<Map<String,Object>> map = new ParamPage<>();
			map.setPageIndex(-1);
			map.setPageSize(-1);
			map.setParam(new HashMap<String,Object>());
			map.getParam().put("configCode", configCode);
			List<Map<String, Object>> list = sacSystemConfigValueService.queryListSysteConfigValueByCode(token, map).getRows();	
	    	if(list!=null && list.size()>0){
	    		return list.get(0).get("valueCode").toString();
	    	}
		}catch (Exception e) {
			return null;
		}
		return null;
    	
    }
	

}
