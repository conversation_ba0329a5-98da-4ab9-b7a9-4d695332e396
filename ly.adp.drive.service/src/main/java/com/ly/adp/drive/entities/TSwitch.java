package com.ly.adp.drive.entities;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * 职员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@TableName("mp.t_switch")
public class TSwitch implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 职员ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 开关对应的方法
     */
    @TableField("method_name")
    private String methodName;
    /**
     * 开关名称
     */
    @TableField("switch_name")
    private String switchName;

    @TableField("time_line")
    private String timeLine;

    @TableField(exist = false)
    private TimeLineEffective timeLineEffective;

    public TSwitch(String methodName) {
        this.methodName = methodName;
    }

    public TSwitch(String methodName, String switchName) {
        this.methodName = methodName;
        this.switchName = switchName;
    }

    public TSwitch(Integer id, String methodName, String switchName) {
        this.id = id;
        this.methodName = methodName;
        this.switchName = switchName;
    }

    public TSwitch(String methodName, String switchName, String timeLine) {
        this.methodName = methodName;
        this.switchName = switchName;
        this.timeLine = timeLine;
    }

    public TSwitch(Integer id, String methodName, String switchName, String timeLine) {
        this.id = id;
        this.methodName = methodName;
        this.switchName = switchName;
        this.timeLine = timeLine;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getSwitchName() {
        return switchName;
    }

    public void setSwitchName(String switchName) {
        this.switchName = switchName;
    }

    public String getTimeLine() {
        return timeLine;
    }

    public void setTimeLine(String timeLine) {
        this.timeLine = timeLine;
    }

    public TimeLineEffective getTimeLineEffective() {
        if (StringUtils.isNotEmpty(this.timeLine)) {
            return JSONObject.parseObject(this.timeLine, TimeLineEffective.class);
        }
        return null;
    }

    public void setTimeLineEffective(TimeLineEffective timeLineEffective) {
        this.timeLine = JSONObject.toJSONString(timeLineEffective);
        this.timeLineEffective = timeLineEffective;
    }

    public QueryWrapper<TSwitch> buildQueryWrapper(SFunction<TSwitch, ?>... columns) {
        QueryWrapper<TSwitch> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(methodName)) {
            queryWrapper.lambda().eq(TSwitch::getMethodName, this.methodName);
        }
        return queryWrapper;
    }

    public LambdaUpdateWrapper<TSwitch> buildUpdate() {
        // 创建 UpdateWrapper 对象
        LambdaUpdateWrapper<TSwitch> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNotEmpty(this.methodName)) {
            updateWrapper.eq(TSwitch::getMethodName, this.methodName);
        }
        // 设置需要更新的字段和新值
        if (StringUtils.isNotEmpty(this.switchName)) {
            updateWrapper.set(TSwitch::getSwitchName, this.switchName);
        }
        // 设置需要更新的字段和新值
        updateWrapper.set(TSwitch::getTimeLine, this.timeLine);
        return updateWrapper;
    }

    /**
     * 判断是执行原接口还是执行优化的接口
     *
     * @return
     */
    public Boolean oldOrPerformance() {
        String switchNameFromTimeLine = null;
        if (StringUtils.isNotEmpty(this.getTimeLine()) && CollectionUtils.isNotEmpty(this.getTimeLineEffective().getOld())) {
            // 如果设置了不生效时间，那么就要判断当前时间是否在时间范围内
            switchNameFromTimeLine = this.getTimeLineEffective().getSwitchNameFromTimeLine();
        }
        if ((
                (StringUtils.isNotEmpty(switchNameFromTimeLine) && StringUtils.equals("old", switchNameFromTimeLine))
                        || (StringUtils.isNotEmpty(this.getSwitchName()) && StringUtils.equals("old", this.getSwitchName()))
        )) {
            // 原接口
            return Boolean.FALSE;
        }
        // 优化的接口
        return Boolean.TRUE;
    }
}
