package com.ly.adp.drive.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ly.adp.common.entity.constants.CommonConstants;
import com.ly.adp.common.entity.enums.ResultCodeEnum;
import com.ly.adp.common.util.BaseSpecialDateUtils;
import com.ly.adp.common.util.CommonResultUitl;
import com.ly.adp.drive.entities.CdpRsp;
import com.ly.adp.drive.entities.LookupValue;
import com.ly.adp.drive.entities.SacAppointmentSheet;
import com.ly.adp.drive.entities.SacTestDriveSheet;
import com.ly.adp.drive.entities.enums.TestStatusEnum;
import com.ly.adp.drive.entities.req.TestDriveCheckInReq;
import com.ly.adp.drive.helper.CacheDataFactory;
import com.ly.adp.drive.idal.mapper.SacAppointmentSheetMapper;
import com.ly.adp.drive.idal.mapper.SacTestDriveSheetMapper;
import com.ly.adp.drive.otherservice.IBasedataFeignClient;
import com.ly.adp.drive.otherservice.ICscClueFeignClient;
import com.ly.adp.drive.otherservice.IXapiPushFeignService;
import com.ly.adp.drive.service.ISacAppointmentSheetService;
import com.ly.adp.drive.util.AESUtil;
import com.ly.bucn.component.interceptor.InterceptorWrapperRegist;
import com.ly.bucn.component.interceptor.InterceptorWrapperRegistor;
import com.ly.bucn.component.interceptor.annotation.Interceptor;
import com.ly.bucn.component.message.Message;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busi.base.constant.UserBusiEntity;
import com.ly.mp.busi.base.context.BusicenContext;
import com.ly.mp.busi.base.handler.BusicenUtils;
import com.ly.mp.busi.base.handler.BusicenUtils.SOU;
import com.ly.mp.busi.base.handler.MapUtil;
import com.ly.mp.busi.base.handler.OptResultBuilder;
import com.ly.mp.busi.base.handler.ResultHandler;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.busicen.rule.field.IFireFieldRule;
import com.ly.mp.busicen.rule.field.execution.ValidResultCtn;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;
import com.szlanyou.common.redis.util.RedisUtil;
import io.seata.spring.annotation.GlobalTransactional;
import ly.adp.drive.otherservice.ICscSysBaseDataService;
import ly.adp.drive.otherservice.ICscSysClueService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <p>
 * 试乘试驾预约单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Service
public class SacAppointmentSheetService extends ServiceImpl<SacAppointmentSheetMapper, SacAppointmentSheet>
        implements ISacAppointmentSheetService, InterceptorWrapperRegist {

    private static final String HEADER_TOKEN = "Authorization";

    public static final String CDP_ACCESS_TOKEN_REDIS_KEY = "CdpAccessTokenRedisKey";

    private Logger log = LoggerFactory.getLogger(SacAppointmentSheetService.class);

    @Autowired
    SacAppointmentSheetMapper sacAppointmentSheetMapper;
    @Autowired
    IBasedataFeignClient basedataFeignClient;
    @Autowired
    SacTestDriveSheetMapper sacTestDriveSheetMapper;

    @Autowired
    ICscSysClueService sacClueInfoDlrService;
    @Autowired
    CacheDataFactory cacheDataFactory;
    @Autowired
    ICscSysBaseDataService baseDataService;
    @Autowired
    IFireFieldRule fireFieldRule;
    @Autowired
    Message message;
    @Autowired
    IXapiPushFeignService accPushFeignService;
    @Autowired
    ICscClueFeignClient cscClueFeignClientL;

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    private AESUtil aesUtil;

    /**
     * 试乘试驾预约单查询
     */
    @Override
    public ListResult<Map<String, Object>> appointmentSheetQueryList(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        try {
            // 去掉空字符串
            MapUtil.removeNullValue(mapParam.getParam());
            //字段校验
            ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam.getParam(), "csc-clue-appointment-sheet-check1",
                    "maindata");
            String resMsg = fireRule.getNotValidMessage();
            if (!fireRule.isValid()) {
                throw new BusicenException(resMsg);
            }
            // 默认查询没有被取消的试乘试驾单
            if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("isEnable"))) {
                mapParam.getParam().put("isEnable", "1");
            }
            String token = mapParam.getParam().get("token").toString();
            // 根据token获取当前用户信息
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
            Page<Map<String, Object>> page = new Page<Map<String, Object>>(mapParam.getPageIndex(),
                    mapParam.getPageSize());

            List<Map<String, Object>> list = sacAppointmentSheetMapper.selectSacAppointmentSheet(mapParam.getParam(),
                    page);
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("appointmentSheetQueryList", e);
            throw e;
        }
        return result;

    }

    /**
     * 试乘试驾预约单保存
     */
    @Override
    @Interceptor("csc_clue_appointment_sheet")
    @Transactional(rollbackFor = Exception.class)
    public EntityResult<Map<String, Object>> appointmentSheetSave(Map<String, Object> mapParam) {

        String token = String.valueOf(mapParam.get("token"));
        try {
            // 判断新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("updateFlag"))) {
                updateFlag = (Boolean) mapParam.get("updateFlag");
            }
            // 新增
            if (!updateFlag) {
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
                    mapParam.put("appointmentId", StringHelper.GetGUID());
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("customerSex"))) {
                    mapParam.put("customerSex", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isEnable"))) {
                    mapParam.put("isEnable", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentChannel"))) {
                    mapParam.put("appointmentChannel", "0");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isTestDrive"))) {
                    mapParam.put("isTestDrive", "0");
                }
                // 生成预约时间
                String appointmentTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
                mapParam.put("appointmentTime", appointmentTime);
                // 单号生成
                setOrderCode(mapParam);

                BusicenUtils.invokeUserInfo(mapParam, SOU.Save, token);
                sacAppointmentSheetMapper.insertSacAppointmentSheet(mapParam);
                mapParam.put("isSendMessage", "1");
            } else {
                BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
                sacAppointmentSheetMapper.updateSacAppointmentSheet(mapParam);
            }
            Page<Map<String, Object>> page = new Page<Map<String, Object>>(1, -1);
            List<Map<String, Object>> list = sacAppointmentSheetMapper.selectSacAppointmentSheet(mapParam, page);
            return ResultHandler.updateOk(list.get(0));
        } catch (Exception e) {
            log.error("appointmentSheetSave", e);
            throw e;
        }
    }

    /**
     * 试乘试驾预约单保存
     */
    @Override
//    @Interceptor("csc_clue_appointment_sheet_performance")
//    @Transactional(rollbackFor = Exception.class)
    public void appointmentSheetSave_performance(Map<String, Object> mapParam) {
        log.info("开始 appointmentSheetSave_performance");
        checkValidate(mapParam);
        log.info("结束 checkValidate");
        checkTime_performance(mapParam);
        log.info("结束 checkTime_performance");
        checkRepeat_performance(mapParam);
        log.info("结束 checkRepeat_performance");
        checkCustomerClue_performance(mapParam);
        log.info("结束 checkCustomerClue_performance");
        checkThresholdValue_performance(mapParam);
        log.info("结束 checkThresholdValue_performance");
        String token = String.valueOf(mapParam.get("token"));
        try {
            // 判断新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("updateFlag"))) {
                updateFlag = (Boolean) mapParam.get("updateFlag");
            }
            // 新增
            if (!updateFlag) {
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
                    mapParam.put("appointmentId", StringHelper.GetGUID());
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("customerSex"))) {
                    mapParam.put("customerSex", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isEnable"))) {
                    mapParam.put("isEnable", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentChannel"))) {
                    mapParam.put("appointmentChannel", "0");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isTestDrive"))) {
                    mapParam.put("isTestDrive", "0");
                }
                // 生成预约时间
                String appointmentTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
                mapParam.put("appointmentTime", appointmentTime);
                // 单号生成
                //setOrderCode(mapParam);
                mapParam.put("appointmentOrderNo","appointmentOrderNo");
                BusicenUtils.invokeUserInfo(mapParam, SOU.Save, token);
                sacAppointmentSheetMapper.insertSacAppointmentSheet(mapParam);
                log.info("结束 insertSacAppointmentSheet");
                mapParam.put("isSendMessage", "1");
            } else {
                BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
                sacAppointmentSheetMapper.updateSacAppointmentSheet(mapParam);
                log.info("结束 updateSacAppointmentSheet");
            }
        } catch (Exception e) {
            log.error("appointmentSheetSave_performance异常", e);
            throw e;
        }
    }

    @Override
    public void setOrderCode(Map<String, Object> mapParam) {
        try {
            // 试乘试驾预约单单号生成
            log.info("试乘试驾预约单单号生成");
            String billTypeId = "bucn_yy_no";
            String dlrId = mapParam.get("dlrId").toString();
            String token = String.valueOf(mapParam.get("token"));
            log.info("试乘试驾预约单单号生成 {} -{} -{}",billTypeId,dlrId,token);
            ListResult<Map<String, Object>> generateOrderCode = baseDataService.generateOrderCode(dlrId, billTypeId, token);
            // 调用服务成功则直接获取单号，不成功则调用自定义单号生成方法
            if ("1".equals(generateOrderCode.getResult())) {
                mapParam.put("appointmentOrderNo", generateOrderCode.getMsg());
                log.info("试乘试驾预约单单号生成");
            } else {
                log.info("试乘试驾预约单单号生成异常");
                throw BusicenException.create("调用生成【试驾预约单单号】出错！[result=" + generateOrderCode.getResult() + ", msg="
                        + generateOrderCode.getMsg() + "]");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 试驾车容量查询
     */
    @Override
    @Interceptor("csc_clue_drive_capacity")
    public ListResult<Map<String, Object>> sacTestDriveCapacityQueryList(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();

        try {
            result = (ListResult<Map<String, Object>>) mapParam.getParam().get("result");
        } catch (Exception e) {
            log.error("sacTestDriveCapacityQueryList", e);
            throw e;
        }
        return result;
    }

    /**
     * 试驾车容量查询
     */
    @Override
    @Interceptor("csc_clue_drive_capacity_new")
    public ListResult<Map<String, Object>> queryTestdriveCapacityList(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();

        try {
            result = (ListResult<Map<String, Object>>) mapParam.getParam().get("result");
        } catch (Exception e) {
            log.error("queryTestdriveCapacityList", e);
            throw e;
        }
        return result;
    }

    /**
     * 试乘试驾单取消
     */
    @Override
    @Interceptor("csc_clue_appointment_sheet_cancel")
    @GlobalTransactional//(rollbackFor = Exception.class)
    public OptResult appointmentSheetCancel(Map<String, Object> mapParam) {
        try {
            if ("1".equals(mapParam.get("type"))) {
                //校验带邀约码的试驾单不可以取消
                Boolean flag = sacAppointmentSheetMapper.checkIsHaveColumn1(mapParam);
                if (flag) {
                    OptResult optResult = new OptResult();
                    optResult.setResult("0");
                    optResult.setMsg("当前试驾单为邀约试驾单，需要用户自行取消!");
                    return optResult;
                }
            }

            String token = mapParam.get("token").toString();
            BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
            mapParam.put("isEnable", "0");
            int result = sacAppointmentSheetMapper.updateSacAppointmentSheet(mapParam);
            if (result == 0) {
                return OptResultBuilder.createFail().build();
            }
        } catch (Exception e) {
            log.error("appointmentSheetCancel", e);
            throw e;
        }
        return OptResultBuilder.createOk().build();
    }

    @Override
    @Interceptor("csc_clue_appointment_sheet_cancel_app")
    @Transactional(rollbackFor = Exception.class)
    public OptResult appointmentSheetCancelApp(Map<String, Object> mapParam) {

        String token = mapParam.get("token").toString();
        BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
        mapParam.put("isEnable", "0");
        if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
            int result = sacAppointmentSheetMapper.updateSacAppointmentSheet(mapParam);
            if (result == 0) {
                return OptResultBuilder.createFail().build();
            }
        }
        if (!StringHelper.IsEmptyOrNull(mapParam.get("id"))) {
            mapParam.remove("updateControlId");
            Map<String, Object> taskMap = sacAppointmentSheetMapper.findDriveTask(mapParam);
            if (StringHelper.IsEmptyOrNull(taskMap)) {
                throw new BusicenException("试驾任务不存在或已完成");
            }

            int result = sacAppointmentSheetMapper.updateDriveTask(mapParam);
            if (result == 0) {
                return OptResultBuilder.createFail().build();
            }
            HashMap<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("event", "c_cancel_drive");
            paramMap.put("bk", taskMap.get("phone"));
            paramMap.put("date", BaseSpecialDateUtils.sysDateTZ());
            paramMap.put("logs_id", StringHelper.GetGUID());
            paramMap.put("configCode", "ADP_INSERT_CDP_LEADS_EVENT");
            Map<String, Object> param = new HashMap<>(1);
            param.put("mapParam", paramMap);
            accPushFeignService.sendCancleData(paramMap.get("configCode").toString(), param);
        }
        return OptResultBuilder.createOk().build();
    }


    /**
     * 前后置方法
     *
     * @param registor
     */
    @SuppressWarnings("unchecked")
    @Override
    public void regist(InterceptorWrapperRegistor registor) {
        // 校验字段
        registor.before("csc_clue_appointment_sheet_valid", (context, model) -> {
            checkValidate((Map<String, Object>) context.data().getP()[0]);
        });
        // 检查时间
        registor.before("csc_clue_appointment_sheet_check_time", (context, model) -> {
            checkTime((Map<String, Object>) context.data().getP()[0]);
        });
        // 检查时间
        registor.before("csc_clue_appointment_sheet_check_time_performance", (context, model) -> {
            checkTime_performance((Map<String, Object>) context.data().getP()[0]);
        });
        // 新增主键是否已存在查重
        registor.before("csc_clue_appointment_sheet_repeat", (context, model) -> {
            checkRepeat((Map<String, Object>) context.data().getP()[0]);
        });
        // 新增主键是否已存在查重
        registor.before("csc_clue_appointment_sheet_repeat_performance", (context, model) -> {
            checkRepeat_performance((Map<String, Object>) context.data().getP()[0]);
        });
        // 预约人数阈值检验,已达到阈值则不能再预约
        registor.before("csc_clue_appointment_sheet_threshold", (context, model) -> {
            checkThresholdValue((Map<String, Object>) context.data().getP()[0]);
        });
        // 预约人数阈值检验,已达到阈值则不能再预约
        registor.before("csc_clue_appointment_sheet_threshold_performance", (context, model) -> {
            checkThresholdValue_performance((Map<String, Object>) context.data().getP()[0]);
        });
        // 判断客户是否存在，不存在则新增一条线索
        registor.before("csc_clue_appointment_sheet_customer", (context, model) -> {
            checkCustomerClue((Map<String, Object>) context.data().getP()[0]);
        });
        // 判断客户是否存在，不存在则新增一条线索
        registor.before("csc_clue_appointment_sheet_customer_performance", (context, model) -> {
            checkCustomerClue_performance((Map<String, Object>) context.data().getP()[0]);
        });
        //app预约单取消token获取
        registor.before("csc_clue_appointment_sheet_cancel_token", (context, model) -> {
            getToken((Map<String, Object>) context.data().getP()[0]);
        });
        // 预约单取消字段校验
        registor.before("csc_clue_appointment_sheet_cancel_valid", (context, model) -> {
            checkAppointmentCancelValidate((Map<String, Object>) context.data().getP()[0]);
        });
        // 预约单校验是否存在、是否可以取消
        registor.before("csc_clue_appointment_sheet_cancel_exits", (context, model) -> {
            checkAppointmentIdExits((Map<String, Object>) context.data().getP()[0]);
        });
        // 取消预约单后，删除试乘试驾单信息
        registor.after("csc_clue_appointment_sheet_cancel_sheet", (context, model) -> {
            deleteTestDriveSheet((Map<String, Object>) context.data().getP()[0]);
        });
        //app容量查询取消token获取
        registor.before("csc_clue_drive_capacity_token", (context, model) -> {
            getTokenByParamBase((ParamPage<Map<String, Object>>) context.data().getP()[0]);
        });
        // 容量查询前后置方法
        registor.before("csc_clue_drive_capacity_check", (context, model) -> {
            checkCapacityValidate((ParamPage<Map<String, Object>>) context.data().getP()[0]);
        });
        // 容量查询实现
        registor.before("csc_clue_drive_capacity_query", (context, model) -> {
            ParamPage<Map<String, Object>> mapParam = (ParamPage<Map<String, Object>>) context.data().getP()[0];
            if (mapParam.getParam().get("lxl") != null && "1".equals(mapParam.getParam().get("lxl").toString())) {
                checkCapacityquery((ParamPage<Map<String, Object>>) context.data().getP()[0]);
            } else {
                checkCapacityquery2((ParamPage<Map<String, Object>>) context.data().getP()[0]);
            }

        });

        // 容量查询实现
        registor.before("csc_clue_drive_capacity_query_new", (context, model) -> {
            ParamPage<Map<String, Object>> mapParam = (ParamPage<Map<String, Object>>) context.data().getP()[0];
            if (mapParam.getParam().get("lxl") != null && "1".equals(mapParam.getParam().get("lxl").toString())) {
                checkCapacityquery((ParamPage<Map<String, Object>>) context.data().getP()[0]);
            } else {
                checkCapacityquery3((ParamPage<Map<String, Object>>) context.data().getP()[0]);
            }

        });
        // 取消预约单后，发通知
        registor.after("csc_clue_appointment_sheet_cancel_msg", (context, model) -> {
            createdMsg((Map<String, Object>) context.data().getP()[0]);
        });
        // 试驾预约发送短信
        registor.after("csc_clue_send_message", (context, model) -> {
            sendMessage((Map<String, Object>) context.data().getP()[0]);
        });
    }

    private void createdMsg(Map<String, Object> map) {
        //调用csc
        if (!StringHelper.IsEmptyOrNull(map.get("appointmentId"))) {
            try {
                if ("1".equals(map.get("type"))) {
                    //校验带邀约码的试驾单不可以取消
                    Boolean flag = sacAppointmentSheetMapper.checkIsHaveColumn1(map);
                    if (flag) {
                        return;
                    }
                }
                List<Map<String, Object>> list = SwitchDbInvoke.invokeTidb(() -> sacAppointmentSheetMapper.selectSacAppointmentSheetById(map));
                if (list.size() != 0) {
                    map.putAll(list.get(0));
//				map.put("custName",list.get(0).get("custName"));
//				map.put("phone",list.get(0).get("phone"));
                    map.put("scenario", "13");
                    map.put("messageType", "13");
                    cscClueFeignClientL.sacMsgRecordSaveInfo((String) map.get("token"), map);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("sacMsgRecordSaveInfo", e);
            }
        }
    }

    public void checkCapacityValidate(ParamPage<Map<String, Object>> mapParam) {
        // 必填字段校验
        ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam.getParam(), "csc-clue-capacity-check", "maindata");
        String resMsg = fireRule.getNotValidMessage();
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
    }

    public void checkCapacityquery(ParamPage<Map<String, Object>> mapParam) {
        Supplier<ListResult<Map<String, Object>>> supplier = () -> {
            ListResult<Map<String, Object>> result = new ListResult<>();
            try {
                String token = mapParam.getParam().get("token").toString();
                UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
                if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("dlrCode"))) {
                    mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
                }
                // 查询系统配置中的试乘试驾预约时间开关，APPOINTMENT_SWITCH
                String configValue = cacheDataFactory.querySysConfigValue("APPOINTMENT_SWITCH", token);
                // 默认可以预约当前时间段
                boolean isCan = true;
                if (!StringHelper.IsEmptyOrNull(configValue)) {
                    // valueCode=0，不能预约当前时间段
                    if ("0".equals(configValue)) {
                        isCan = false;
                    }
                }
                mapParam.getParam().put("isCan", isCan);
                // 首先先查询时间段
                List<Map<String, Object>> timeRangeList = sacAppointmentSheetMapper
                        .selectCarCapacityTimeRange(mapParam.getParam());
                // 拼接查询条件
                String condition = "";
                for (Map<String, Object> map : timeRangeList) {
                    condition = condition + ",MAX(IF(TT.CONFIG_VALUE_CODE = '" + map.get("configValueCode").toString()
                            + "', TT.CAPACITY_STATUS, NULL)) AS '" + map.get("configValueCode").toString() + "'";
                }
                mapParam.getParam().put("condition", condition);
                // 先查询
                Page<Map<String, Object>> page = new Page<Map<String, Object>>(mapParam.getPageIndex(),
                        mapParam.getPageSize());
                List<Map<String, Object>> list = sacAppointmentSheetMapper.selectCarCapacity(mapParam.getParam(), page);
                page.setRecords(list);
                result = BusicenUtils.page2ListResult(page);
                // 将结果添加到mapParam
                mapParam.getParam().put("result", result);
                return result;
            } catch (Exception e) {
                log.error("sacTestDriveCapacityQueryListBefore", e);
                throw e;
            }
        };
        SwitchDbInvoke.invokeTidb(supplier);

    }

    public void checkCapacityquery2(ParamPage<Map<String, Object>> mapParam) {

        ListResult<Map<String, Object>> result = new ListResult<>();

        String token = mapParam.getParam().get("token").toString();
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("dlrCode"))) {
            if (userBusiEntity.getDlrCode() != null && !"".equals(userBusiEntity.getDlrCode())) {
                mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
            } else {
                throw new BusicenException("所在门店为必传");
            }
        }
        // 查询系统配置中的试乘试驾预约时间开关，APPOINTMENT_SWITCH
        String configValue = cacheDataFactory.querySysConfigValue("APPOINTMENT_SWITCH", token);
        // 默认可以预约当前时间段
        boolean isCan = true;
        if (!StringHelper.IsEmptyOrNull(configValue)) {
            // valueCode=0，不能预约当前时间段
            if ("0".equals(configValue)) {
                isCan = false;
            }
        }
        mapParam.getParam().put("isCan", isCan);
        // 首先先查询时间段
        List<Map<String, Object>> timeRangeList = sacAppointmentSheetMapper
                .selectCarCapacityTimeRange(mapParam.getParam());
        //查询每个店有多少台车
        List<Map<String, Object>> carList = sacAppointmentSheetMapper.querCarList(mapParam.getParam());
        log.info("处理前的carList{}", carList);
        if (carList != null && carList.size() > 0) {
            //查询每个时间段预约的超长试驾
            List<Map<String, Object>> driveLongList = sacAppointmentSheetMapper.querDriveLong(mapParam.getParam());
            //查询每个时间段预约的试驾
            List<Map<String, Object>> testDriverList = sacAppointmentSheetMapper.querTestDriver(mapParam.getParam());
            for (Map<String, Object> map : carList) {
                map.put("tjDate", mapParam.getParam().get("appointmentDateMin").toString());
                //将每个时间段先put进去
                for (Map<String, Object> objectMap : timeRangeList) {
                    //获取当前时间跟时间段比较 看能否约当前时间
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    Date startTime = null;
                    try {
                        startTime = format.parse(mapParam.getParam().get("appointmentDateMin").toString() + " " + objectMap.get("column1"));
                    } catch (ParseException e) {
                        log.info("转换时间异常{}", e);
                    }
                    Date endTime = null;
                    try {
                        endTime = format.parse(mapParam.getParam().get("appointmentDateMin").toString() + " " + objectMap.get("column2"));
                    } catch (ParseException e) {
                        log.info("转换时间异常{}", e);
                    }
                    //现在时间
                    Date nowTime = new Date();
                    //比较两个时间,如果返回1说明futureTime>nowTime   -1说明小于  0说明等于
                    int sign1 = startTime.compareTo(nowTime);
                    int sign2 = endTime.compareTo(nowTime);

                    if (isCan) {
                        if (sign2 < 0) {
                            map.put(objectMap.get("configValueName").toString(), "不可约(已过时段)");
                        } else {
                            map.put(objectMap.get("configValueName").toString(), "可约");
                        }
                    } else {
                        if (sign1 < 0) {
                            map.put(objectMap.get("configValueName").toString(), "不可约(已过时段)");
                        } else {
                            map.put(objectMap.get("configValueName").toString(), "可约");
                        }
                    }

                }
                if (driveLongList != null && driveLongList.size() > 0) {
                    for (Map<String, Object> stringObjectMap : driveLongList) {
                        if (map.get("plateNumber").equals(stringObjectMap.get("plateNumber"))) {
                            map.put(stringObjectMap.get("configValueName").toString(), "超长试驾");
                        }
                    }
                }
                if (testDriverList != null && testDriverList.size() > 0) {
                    for (Map<String, Object> stringObjectMap : testDriverList) {
                        if (map.get("plateNumber").equals(stringObjectMap.get("plateNumber"))) {
                            map.put(stringObjectMap.get("configValueName").toString(), stringObjectMap.get("testStatus1"));
                        }
                    }
                }
            }
        } else {
            return;
        }
        log.info("处理后的carList{}", carList);
        result.setRows(carList);
        result.setRecords(0);
        result.setPages(0);
        result.setPageindex(0);
        result.setResult("1");
        result.setMsg("查询成功");
        log.info("处理后的result{}", result);

        // 将结果添加到mapParam
        mapParam.getParam().put("result", result);

    }

    public void checkCapacityquery3(ParamPage<Map<String, Object>> mapParam) {

        ListResult<Map<String, Object>> result = new ListResult<>();

        String token = mapParam.getParam().get("token").toString();
//        token = "692395fa17964944a8595052268f358a";
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("dlrCode"))) {
            if (userBusiEntity.getDlrCode() != null && !"".equals(userBusiEntity.getDlrCode())) {
                mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
            } else {
                throw new BusicenException("所在门店为必传");
            }
        }
//        mapParam.getParam().put("dlrCode", "SB57101");
        // 查询系统配置中的试乘试驾预约时间开关，APPOINTMENT_SWITCH
        String configValue = cacheDataFactory.querySysConfigValue("APPOINTMENT_SWITCH", token);
        // 默认可以预约当前时间段
        boolean isCan = true;
        if (!StringHelper.IsEmptyOrNull(configValue)) {
            // valueCode=0，不能预约当前时间段
            if ("0".equals(configValue)) {
                isCan = false;
            }
        }
        mapParam.getParam().put("isCan", isCan);
        // 首先先查询时间段
        List<Map<String, Object>> timeRangeList = sacAppointmentSheetMapper
                .selectCarCapacityTimeRange(mapParam.getParam());
        //查询每个店有多少台车
        List<Map<String, Object>> carList = sacAppointmentSheetMapper.querCarList(mapParam.getParam());
        log.info("处理前的carList{}", carList);
        if (carList != null && carList.size() > 0) {
            //查询每个时间段预约的超长试驾
            List<Map<String, Object>> driveLongList = sacAppointmentSheetMapper.querDriveLong(mapParam.getParam());
            //查询每个时间段预约的试驾
            List<Map<String, Object>> testDriverList = sacAppointmentSheetMapper.querTestDriverNew(mapParam.getParam());
            for (Map<String, Object> map : carList) {
                map.put("tjDate", mapParam.getParam().get("appointmentDateMin").toString());
                //将每个时间段先put进去
                for (Map<String, Object> objectMap : timeRangeList) {
                    //获取当前时间跟时间段比较 看能否约当前时间
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    Date startTime = null;
                    try {
                        startTime = format.parse(mapParam.getParam().get("appointmentDateMin").toString() + " " + objectMap.get("column1"));
                    } catch (ParseException e) {
                        log.info("转换时间异常{}", e);
                    }
                    Date endTime = null;
                    try {
                        endTime = format.parse(mapParam.getParam().get("appointmentDateMin").toString() + " " + objectMap.get("column2"));
                    } catch (ParseException e) {
                        log.info("转换时间异常{}", e);
                    }
                    //现在时间
                    Date nowTime = new Date();
                    //比较两个时间,如果返回1说明futureTime>nowTime   -1说明小于  0说明等于
                    int sign1 = startTime.compareTo(nowTime);
                    int sign2 = endTime.compareTo(nowTime);

                    if (isCan) {
                        if (sign2 < 0) {
                            map.put(objectMap.get("configValueName").toString(), "不可约(已过时段)");
                        } else {
                            map.put(objectMap.get("configValueName").toString(), "可约");
                        }
                    } else {
                        if (sign1 < 0) {
                            map.put(objectMap.get("configValueName").toString(), "不可约(已过时段)");
                        } else {
                            map.put(objectMap.get("configValueName").toString(), "可约");
                        }
                    }
                }
                if (driveLongList != null && driveLongList.size() > 0) {
                    for (Map<String, Object> stringObjectMap : driveLongList) {
                        if (map.get("plateNumber").equals(stringObjectMap.get("plateNumber"))) {
                            map.put(stringObjectMap.get("configValueName").toString(), "超长试驾");
                        }
                    }
                }
                if (testDriverList != null && testDriverList.size() > 0) {
                    for (Map<String, Object> stringObjectMap : testDriverList) {
                        if (map.get("plateNumber").equals(stringObjectMap.get("plateNumber"))) {
                            String timeName = map.get(stringObjectMap.get("configValueName").toString()).toString();
                            if (!timeName.contains("不可约")) {
                                if (timeName.contains("可约")) {
                                    if (Objects.nonNull(stringObjectMap.get("testStatus1")) && Objects.nonNull(stringObjectMap.get("reviewPersonName"))) {
                                        map.put(stringObjectMap.get("configValueName").toString(),
                                                (new StringJoiner(":")
                                                        .add(stringObjectMap.get("testStatus1").toString())
                                                        .add(stringObjectMap.get("reviewPersonName").toString())).toString());
                                    }
                                } else {
                                    // 说明是有用户，就要拼接
                                    if (Objects.nonNull(stringObjectMap.get("testStatus1")) && Objects.nonNull(stringObjectMap.get("reviewPersonName"))) {
                                        map.put(stringObjectMap.get("configValueName").toString(),
                                                (new StringJoiner(";")
                                                        .add(timeName)
                                                        .add(new StringJoiner(":")
                                                                .add(stringObjectMap.get("testStatus1").toString())
                                                                .add(stringObjectMap.get("reviewPersonName").toString())
                                                                .toString())).toString()
                                        );
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            return;
        }
        log.info("处理后的carList{}", carList);
        result.setRows(carList);
        result.setRecords(0);
        result.setPages(0);
        result.setPageindex(0);
        result.setResult("1");
        result.setMsg("查询成功");
        log.info("处理后的result{}", result);

        // 将结果添加到mapParam
        mapParam.getParam().put("result", result);

    }


    /**
     * 字段校验
     *
     * @param mapParam
     */
    public void checkValidate(Map<String, Object> mapParam) {
        // 通用字段校验
        ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-appointment-sheet-check", "maindata");
        String resMsg = fireRule.getNotValidMessage();
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
        // 普通试乘试驾时，字段校验
        if ("0".equals(mapParam.get("testType")) || "1".equals(mapParam.get("testType"))) {
            fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-appointment-check-nomal", "maindata");
            resMsg = fireRule.getNotValidMessage();
        } else {
            // 深度试驾时，字段校验
            fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-appointment-check-deep", "maindata");
            resMsg = fireRule.getNotValidMessage();
        }
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
    }

    /**
     * 检查预约时间是否合法
     *
     * @param mapParam
     * @throws Exception
     */
    public void checkTime(Map<String, Object> mapParam) {
        try {
            Calendar date = Calendar.getInstance();
            // 普通试乘试驾时间校验
            if ("0".equals(mapParam.get("testType")) || "1".equals(mapParam.get("testType"))) {
                // 判断时间是否与内置表的时间一致，若是不符合则抛异常(appointmentTestTime)
//				Map<String, Object> param = new HashMap<String, Object>();
//				param.put("configValueCode", mapParam.get("appointmentTestTime"));
//				param.put("configCode", "DRIVE_TIME");
//				ParamPage<Map<String, Object>> paramPage = new ParamPage<Map<String, Object>>();
//				paramPage.setPageIndex(1);
//				paramPage.setPageSize(-1);
//				paramPage.setParam(param);
//				List<Map<String, Object>> result = sacDbInnerConfigService
//						.queryConfigList(paramPage, mapParam.get("token").toString()).getRows();
//
//				if (result.size() == 0) {
//					throw new BusicenException(message.get("APPOINTMENT-SHEET-05"));
//				}
                // 新增试乘试驾预约单的时候，验证预约时间是否合理
                // 判断日期是否是今天开始往后的
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = simpleDateFormat.format(date.getTime());
                if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(mapParam.get("appointmentTestDate").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-06"));
                }
                // 如果选择今天的日期，那时间段的开始时间只能大于当前的时间了
                if (simpleDateFormat.parse(dateString).equals(simpleDateFormat.parse(mapParam.get("appointmentTestDate").toString()))) {
                    // 查询系统配置中的试乘试驾预约时间开关，APPOINTMENT_SWITCH
                    String configValue = cacheDataFactory.querySysConfigValue("APPOINTMENT_SWITCH", String.valueOf(mapParam.get("token")));

                    // 默认可以预约当前时间段
                    boolean isCan = true;
                    if (!StringHelper.IsEmptyOrNull(configValue)) {
                        // valueCode=0，不能预约当前时间段
                        if ("0".equals(configValue)) {
                            isCan = false;
                        }
                    }
                    //格式化
                    DateFormat df = new SimpleDateFormat("HH:mm:ss");
                    //获取当前时分
                    int currentHour = date.get(Calendar.HOUR_OF_DAY);
                    int currentMinute = date.get(Calendar.MINUTE);
                    //当前时间
                    Date currentTime = df.parse(currentHour + ":" + currentMinute + ":00");
                    //预约时间段开始时间
                    String startTime = mapParam.get("appointmentTestTime").toString().split("-")[0] + ":00";
                    Date appointStartTime = df.parse(startTime);
                    //预约时间段结束时间
                    String endTime = mapParam.get("appointmentTestTime").toString().split("-")[1] + ":00";
                    Date appointEndTime = df.parse(endTime);
                    //当前时间不能大于等于时间段的开始时间
                    if (currentTime.getTime() >= appointEndTime.getTime()) {
                        throw new BusicenException("预约时间段的结束时间不能小于当前时间");
                    }
                    //如果不可以预约当前时间段,则当前时间要不在时间段内
                    if (!isCan && currentTime.getTime() >= appointStartTime.getTime() && currentTime.getTime() <= appointEndTime.getTime()) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-17"));
                    }
                }
            } else {
                // 深度试驾时间判断
                // 当前时间大于预约开始时间，抛异常
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = simpleDateFormat.format(date.getTime());
                if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-11"));
                }
                // 开始时间大于等于结束时间，抛异常
                if (simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()).after(simpleDateFormat.parse(mapParam.get("appointmentEndTime").toString()))
                        || simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()).equals(simpleDateFormat.parse(mapParam.get("appointmentEndTime").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-12"));
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_appointment_sheet_check_time", e);
            throw new BusicenException(e.getMessage());
        }
    }
    /**
     * 检查预约时间是否合法
     *
     * @param mapParam
     * @throws Exception
     */
    public void checkTime_performance(Map<String, Object> mapParam) {
        try {
            Calendar date = Calendar.getInstance();
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(String.valueOf(mapParam.get("token")));
            mapParam.put("dlrCode", userBusiEntity.getDlrCode());
            mapParam.put("dlrName", userBusiEntity.getDlrName());
            mapParam.put("dlrId", userBusiEntity.getDlrID());
            mapParam.put("reviewPersonId", userBusiEntity.getUserID());
            mapParam.put("reviewPersonName", userBusiEntity.getUserName());
            mapParam.put("userBusiEntity", userBusiEntity);
            // 普通试乘试驾时间校验
            if ("0".equals(mapParam.get("testType")) || "1".equals(mapParam.get("testType"))) {
                // 新增试乘试驾预约单的时候，验证预约时间是否合理
                // 判断日期是否是今天开始往后的
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = simpleDateFormat.format(date.getTime());
                if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(mapParam.get("appointmentTestDate").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-06"));
                }
                // 如果选择今天的日期，那时间段的开始时间只能大于当前的时间了
                if (simpleDateFormat.parse(dateString).equals(simpleDateFormat.parse(mapParam.get("appointmentTestDate").toString()))) {
                    // 查询系统配置中的试乘试驾预约时间开关，APPOINTMENT_SWITCH
                    Object appointmentSwitch = redisUtil.get(String.format("AppointmentSwitch-%s", userBusiEntity.getDlrCode()));
                    String configValue = "1";
                    if(Objects.isNull(appointmentSwitch)) {
                        configValue = cacheDataFactory.querySysConfigValue("APPOINTMENT_SWITCH", String.valueOf(mapParam.get("token")));
                        redisUtil.set(String.format("AppointmentSwitch-%s", userBusiEntity.getDlrCode()), configValue,1, TimeUnit.HOURS);
                    }
                    else {
                        configValue = (String) appointmentSwitch;
                    }
                    // 默认可以预约当前时间段
                    boolean isCan = true;
                    if (!StringHelper.IsEmptyOrNull(configValue)) {
                        // valueCode=0，不能预约当前时间段
                        if ("0".equals(configValue)) {
                            isCan = false;
                        }
                    }
                    //格式化
                    DateFormat df = new SimpleDateFormat("HH:mm:ss");
                    //获取当前时分
                    int currentHour = date.get(Calendar.HOUR_OF_DAY);
                    int currentMinute = date.get(Calendar.MINUTE);
                    //当前时间
                    Date currentTime = df.parse(currentHour + ":" + currentMinute + ":00");
                    //预约时间段开始时间
                    String startTime = mapParam.get("appointmentTestTime").toString().split("-")[0] + ":00";
                    Date appointStartTime = df.parse(startTime);
                    //预约时间段结束时间
                    String endTime = mapParam.get("appointmentTestTime").toString().split("-")[1] + ":00";
                    Date appointEndTime = df.parse(endTime);
                    //当前时间不能大于等于时间段的开始时间
                    if (currentTime.getTime() >= appointEndTime.getTime()) {
                        throw new BusicenException("预约时间段的结束时间不能小于当前时间");
                    }
                    //如果不可以预约当前时间段,则当前时间要不在时间段内
                    if (!isCan && currentTime.getTime() >= appointStartTime.getTime() && currentTime.getTime() <= appointEndTime.getTime()) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-17"));
                    }
                }
            } else {
                // 深度试驾时间判断
                // 当前时间大于预约开始时间，抛异常
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = simpleDateFormat.format(date.getTime());
                if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-11"));
                }
                // 开始时间大于等于结束时间，抛异常
                if (simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()).after(simpleDateFormat.parse(mapParam.get("appointmentEndTime").toString()))
                        || simpleDateFormat.parse(mapParam.get("appointmentStartTime").toString()).equals(simpleDateFormat.parse(mapParam.get("appointmentEndTime").toString()))) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-12"));
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_appointment_sheet_check_time_performance异常", e);
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 查重
     *
     * @param mapParam
     */
    public void checkRepeat(Map<String, Object> mapParam) {
        try {
            // 普通试乘试驾拼接开始时间与结束时间
            if ("0".equals(mapParam.get("testType")) || "1".equals(mapParam.get("testType"))) {
                String[] timeStrings = String.valueOf(mapParam.get("appointmentTestTime")).split("-");
                String appointmentStartTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[0]
                        + ":00";
                String appointmentEndTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[1]
                        + ":00";
                mapParam.put("appointmentStartTime", appointmentStartTime);
                mapParam.put("appointmentEndTime", appointmentEndTime);
            } else {
                // 深度试驾处理
                parseAppointmentTime(mapParam);
            }
            // 判断是新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
                // 数据库查询是否存在此id
                QueryWrapper<SacAppointmentSheet> queryWrapper = new QueryWrapper<SacAppointmentSheet>();
                queryWrapper.eq("APPOINTMENT_ID", mapParam.get("appointmentId"));
                queryWrapper.lambda().select(SacAppointmentSheet::getIsTestDrive, SacAppointmentSheet::getAppointmentStartTime);
                List<SacAppointmentSheet> list = baseMapper.selectList(queryWrapper);
                if (!list.isEmpty()) {
                    updateFlag = true;
                    // 当该试乘试驾单已经开始试乘试驾了就不能修改了
                    if ("1".equals(list.get(0).getIsTestDrive())) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-13"));
                    }
                    // 已经过期的试乘试驾预约单不能修改(跟进时不限制)
                    Calendar date = Calendar.getInstance();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateString = simpleDateFormat.format(date.getTime());
                    if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(list.get(0).getAppointmentStartTime()))
                            && "0".equals(mapParam.get("isFollow"))) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-14"));
                    }
                }
            }

            //查询用户所属专营店信息
            String token = String.valueOf(mapParam.get("token"));
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            mapParam.put("dlrCode", userBusiEntity.getDlrCode());
            mapParam.put("dlrName", userBusiEntity.getDlrName());
            mapParam.put("dlrId", userBusiEntity.getDlrID());
            mapParam.put("reviewPersonId", userBusiEntity.getUserID());
            mapParam.put("reviewPersonName", userBusiEntity.getUserName());
            // 客户预约单查重:根据电话、预约试乘试驾日期、预约试乘试驾时间段、专营店编码查重
            // 普通试乘试驾查重
            int plateNumberCount = 0;
            mapParam.put("isCheckRepeak", true);
            mapParam.put("updateFlag", updateFlag);
            plateNumberCount = sacAppointmentSheetMapper.checkRepeat(mapParam);
            if (plateNumberCount > 0) {
                throw new BusicenException(message.get("APPOINTMENT-SHEET-01"));
            }
        } catch (Exception e) {
            log.error("csc_clue_appointment_sheet_repeat", e);
            throw new BusicenException(e.getMessage());
        }
    }
    /**
     * 查重
     *
     * @param mapParam
     */
    public void checkRepeat_performance(Map<String, Object> mapParam) {
        try {
            // 普通试乘试驾拼接开始时间与结束时间
            if ("0".equals(mapParam.get("testType")) || "1".equals(mapParam.get("testType"))) {
                String[] timeStrings = String.valueOf(mapParam.get("appointmentTestTime")).split("-");
                String appointmentStartTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[0]
                        + ":00";
                String appointmentEndTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[1]
                        + ":00";
                mapParam.put("appointmentStartTime", appointmentStartTime);
                mapParam.put("appointmentEndTime", appointmentEndTime);
            } else {
                // 深度试驾处理
                parseAppointmentTime(mapParam);
            }
            // 判断是新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
                // 数据库查询是否存在此id
                QueryWrapper<SacAppointmentSheet> queryWrapper = new QueryWrapper<SacAppointmentSheet>();
                queryWrapper.eq("APPOINTMENT_ID", mapParam.get("appointmentId"));
                queryWrapper.lambda().select(SacAppointmentSheet::getIsTestDrive, SacAppointmentSheet::getAppointmentStartTime);
                List<SacAppointmentSheet> list = baseMapper.selectList(queryWrapper);
                if (!list.isEmpty()) {
                    updateFlag = true;
                    // 当该试乘试驾单已经开始试乘试驾了就不能修改了
                    if ("1".equals(list.get(0).getIsTestDrive())) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-13"));
                    }
                    // 已经过期的试乘试驾预约单不能修改(跟进时不限制)
                    Calendar date = Calendar.getInstance();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateString = simpleDateFormat.format(date.getTime());
                    if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(list.get(0).getAppointmentStartTime()))
                            && "0".equals(mapParam.get("isFollow"))) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-14"));
                    }
                }
            }

            //查询用户所属专营店信息
            String token = String.valueOf(mapParam.get("token"));
//            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
//            mapParam.put("dlrCode", userBusiEntity.getDlrCode());
//            mapParam.put("dlrName", userBusiEntity.getDlrName());
//            mapParam.put("dlrId", userBusiEntity.getDlrID());
//            mapParam.put("reviewPersonId", userBusiEntity.getUserID());
//            mapParam.put("reviewPersonName", userBusiEntity.getUserName());
            // 客户预约单查重:根据电话、预约试乘试驾日期、预约试乘试驾时间段、专营店编码查重
            // 普通试乘试驾查重
//            int plateNumberCount = 0;
            mapParam.put("isCheckRepeak", true);
            mapParam.put("updateFlag", updateFlag);
            Object checkUserAppoint = redisUtil.get(String.format("TestAppointmentSheet-%s-%s", mapParam.get("dlrCode"),
                    mapParam.get("customerPhone")));
            if(Objects.nonNull(checkUserAppoint)) {
                throw new BusicenException(message.get("APPOINTMENT-SHEET-01"));
            }
//            plateNumberCount = sacAppointmentSheetMapper.checkRepeat(mapParam);
//            if (plateNumberCount > 0) {
//                throw new BusicenException(message.get("APPOINTMENT-SHEET-01"));
//            }
        } catch (Exception e) {
            log.error("checkRepeat_performance异常", e);
            throw new BusicenException(e.getMessage());
        }
    }

    public void parseAppointmentTime(Map<String, Object> appointmentMap) {
        // 提取日期和时间
        String appointmentTestDate = "";
        String appointmentTestTime = "";

        // 获取开始和结束时间
        String startTimeStr = String.valueOf(appointmentMap.get("appointmentStartTime"));
        String endTimeStr = String.valueOf(appointmentMap.get("appointmentEndTime"));

        if (startTimeStr != null && endTimeStr != null) {
            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 解析开始和结束时间
            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
            LocalDateTime endTime = LocalDateTime.parse(endTimeStr, formatter);

            appointmentTestDate = startTime.toLocalDate().toString();
            appointmentTestTime = startTime.toLocalTime().toString() + "-" + endTime.toLocalTime().toString();
        }

        appointmentMap.put("appointmentTestDate", appointmentTestDate);
        appointmentMap.put("appointmentTestTime", appointmentTestTime);
    }


    /**
     * 判断线索是否存在，不存在则新建线索
     *
     * @param mapParam
     */
    public void checkCustomerClue(Map<String, Object> mapParam) {
        // 试驾单新增线索逻辑
        try {
            Boolean updateFlag = (Boolean) mapParam.get("updateFlag");
            //新增时，需要判断线索是否存在
            if (!updateFlag) {

                ParamBase<Map<String, Object>> mapParamBase = new ParamBase<>();
                Map<String, Object> param = new HashMap<>();
                param.put("phone", mapParam.get("customerPhone"));
                param.put("token", String.valueOf(mapParam.get("token")));
                mapParamBase.setParam(param);
                // 先查询线索是否存在，存在则存线索单号与顾客id
                Map<String, Object> clue = sacClueInfoDlrService.clueDlrCheckRepeat(String.valueOf(mapParam.get("token")), mapParamBase).getRows();

                Map<String, Object> dlrClue = new HashMap<>();
                if (clue == null) {
                    // 根据token获取用户信息
                    String token = mapParam.get("token").toString();
                    // 根据token获取当前用户信息
                    UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
                    ParamBase<Map<String, Object>> newParamBase = new ParamBase<>();
                    Map<String, Object> newParam = new HashMap<>();
                    newParam.put("custName", mapParam.get("customerName"));
                    newParam.put("phone", mapParam.get("customerPhone"));
                    newParam.put("token", mapParam.get("token"));
                    if (!StringHelper.IsEmptyOrNull(mapParam.get("intenLevelCode"))) {
                        newParam.put("intenLevelCode", mapParam.get("intenLevelCode"));
                        newParam.put("intenLevelName", mapParam.get("intenLevelName"));
                    }
                    newParam.put("intenCarTypeCode", mapParam.get("smallCarTypeCode"));
                    newParam.put("intenCarTypeName", mapParam.get("smallCarTypeName"));
                    newParam.put("infoChanMCode", "agent_nature");
                    newParam.put("infoChanMName", "门店自然客流");
                    newParam.put("infoChanDCode", "agent_nature");
                    newParam.put("infoChanDName", "门店自然客流");
                    newParam.put("channelCode", "agent_nature");
                    newParam.put("channelName", "门店自然客流");
                    newParam.put("businessHeatCode", mapParam.get("businessHeatCode"));
                    newParam.put("businessHeatName", mapParam.get("businessHeatName"));
                    newParam.put("planBuyDateName", mapParam.get("planBuyDateName"));
                    newParam.put("planBuyDate", mapParam.get("planBuyDate"));
                    newParam.put("genderCode", mapParam.get("customerSexCode"));
                    newParam.put("genderName", mapParam.get("customerSexName"));
                    newParam.put("reviewPersonId", userBusiEntity.getUserID());
                    newParam.put("reviewPersonName", userBusiEntity.getEmpName());
                    newParam.put("planReviewTime", LocalDateTime.now().plusDays(+1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    // 来源渠道
                    newParam.put("systemSource", "ADP-DRIVE");

//                    Object cdpToken = redisUtil.get(CDP_ACCESS_TOKEN_REDIS_KEY);
//                    List<LookupValue> cdpSys = sacAppointmentSheetMapper.findCdpSys();
//                    Map<String, String> collectMap = cdpSys.stream().collect(Collectors
//                            .toMap(LookupValue::getLookupValueCode, LookupValue::getLookupValueName, (k1, k2) -> k2));
//                    HttpHeaders httpHeaders = new HttpHeaders();
//                    if (StringHelper.IsEmptyOrNull(cdpToken)) {
//
//                        try {
//                            String rsp = RequestClient.postData(collectMap.get("CDP_ACCESS_TOKEN_URL"), httpHeaders, new HashMap<String, String>());
//                            if (!StringHelper.IsEmptyOrNull(rsp)) {
//                                Map<String, Object> rspMap = JSONObject.parseObject(rsp, Map.class);
//                                Object access_token = rspMap.get("access_token");
//                                if (!StringHelper.IsEmptyOrNull(access_token)) {
//                                    cdpToken = access_token;
//                                    //   dlrClue.put("serverOrder", rspMap.get("data"));
//                                    redisUtil.set(CDP_ACCESS_TOKEN_REDIS_KEY, cdpToken, 3600);
//                                }
//                            }
//                        } catch (Exception ignored) {
//                            log.info("获取CDPtoken异常{}", ignored);
//                        }
//                    }
//
//                    String leadsUrl = collectMap.get("leadsUrl") + newParam.get("phone") + "?access_token=" + cdpToken;
//
//                    try {
//                        String rsp = RequestClient.getData(leadsUrl, httpHeaders, new HashMap<>());
//                        log.info("获取cdp三级渠道返回{}", rsp);
//                        if (!StringHelper.IsEmptyOrNull(rsp)) {
//                            CdpRsp cdpRsp = JSONObject.parseObject(rsp, CdpRsp.class);
//                            log.info("获取cdp三级渠道返回格式化{}", rsp);
//                            String c_third_channel = cdpRsp.getC_third_channel();
//                            if (!StringHelper.IsEmptyOrNull(c_third_channel)) {
//                                Map<String, String> thirdChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_072", c_third_channel);
//                                newParam.put("infoChanDCode", c_third_channel);
//                                newParam.put("infoChanDName", thirdChannel.get("lookupValueName"));
//                                String attribute1 = thirdChannel.get("attribute1");
//                                Map<String, String> secondChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_049", attribute1);
//
//                                newParam.put("infoChanMCode", attribute1);
//                                newParam.put("infoChanMName", secondChannel.get("lookupValueName"));
//                                newParam.put("channelCode", attribute1);
//                                newParam.put("channelName", secondChannel.get("lookupValueName"));
//                            }
//                        }
//                    } catch (Exception ignored) {
//                        log.info("获取CDP三级渠道异常{}", ignored);
//                    }
                    newParamBase.setParam(newParam);
                    EntityResult<Map<String, Object>> newClueResult = sacClueInfoDlrService.clueDlrSave(String.valueOf(mapParam.get("token")), newParamBase);
                    if ("0".equals(newClueResult.getResult())) {
                        throw new BusicenException("新建线索失败");
                    }
                    // 新建线索
                    newParamBase.getParam().putAll(newClueResult.getRows());


                    dlrClue.put("serverOrder", newParamBase.getParam().get("serverOrder"));
                    dlrClue.put("custId", newParamBase.getParam().get("custId"));
                    dlrClue.put("intenLevelCode", newParamBase.getParam().get("intenLevelCode"));
                    dlrClue.put("intenLevelName", newParamBase.getParam().get("intenLevelName"));
                } else {
                    dlrClue.put("serverOrder", clue.get("serverOrder"));
                    dlrClue.put("custId", clue.get("custId"));
                    dlrClue.put("intenLevelCode", clue.get("intenLevelCode"));
                    dlrClue.put("intenLevelName", clue.get("intenLevelName"));
                }
                // 获取客户信息
                mapParam.put("dlrClueOrderNo", dlrClue.get("serverOrder"));
                mapParam.put("customerId", dlrClue.get("custId"));
                mapParam.put("intenLevelCode", dlrClue.get("intenLevelCode"));
                mapParam.put("intenLevelName", dlrClue.get("intenLevelName"));
            }
            //不管新增还是修改，性别都能修改
            if (!StringHelper.IsEmptyOrNull(mapParam.get("customerSexCode"))) {
                mapParam.put("customerSex", mapParam.get("customerSexCode"));
            }
        } catch (Exception e) {
            log.error("checkCustomerClue异常", e);
            throw e;
        }
    }

    /**
     * 判断线索是否存在，不存在则新建线索
     *
     * @param mapParam
     */
    public void checkCustomerClue_performance(Map<String, Object> mapParam) {
        // 试驾单新增线索逻辑
        try {
            Boolean updateFlag = (Boolean) mapParam.get("updateFlag");
            Boolean skipClue = (Boolean) mapParam.get("skipClue");
            //新增时，需要判断线索是否存在
            if (!updateFlag && !skipClue) {

                ParamBase<Map<String, Object>> mapParamBase = new ParamBase<>();
                Map<String, Object> param = new HashMap<>();
                param.put("phone", mapParam.get("customerPhone"));
                param.put("token", String.valueOf(mapParam.get("token")));
                mapParamBase.setParam(param);
                // 先查询线索是否存在，存在则存线索单号与顾客id
                Map<String, Object> clue = sacClueInfoDlrService.clueDlrCheckRepeat(String.valueOf(mapParam.get("token")), mapParamBase).getRows();

                Map<String, Object> dlrClue = new HashMap<>();
                if (clue == null) {
                    // 根据token获取用户信息
                    String token = mapParam.get("token").toString();
                    // 根据token获取当前用户信息
                    UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
                    ParamBase<Map<String, Object>> newParamBase = new ParamBase<>();
                    Map<String, Object> newParam = new HashMap<>();
                    newParam.put("custName", mapParam.get("customerName"));
                    newParam.put("phone", mapParam.get("customerPhone"));
                    newParam.put("token", mapParam.get("token"));
                    if (!StringHelper.IsEmptyOrNull(mapParam.get("intenLevelCode"))) {
                        newParam.put("intenLevelCode", mapParam.get("intenLevelCode"));
                        newParam.put("intenLevelName", mapParam.get("intenLevelName"));
                    }
                    newParam.put("intenCarTypeCode", mapParam.get("smallCarTypeCode"));
                    newParam.put("intenCarTypeName", mapParam.get("smallCarTypeName"));
                    newParam.put("infoChanMCode", "agent_nature");
                    newParam.put("infoChanMName", "门店自然客流");
                    newParam.put("infoChanDCode", "agent_nature");
                    newParam.put("infoChanDName", "门店自然客流");
                    newParam.put("channelCode", "agent_nature");
                    newParam.put("channelName", "门店自然客流");
                    newParam.put("businessHeatCode", mapParam.get("businessHeatCode"));
                    newParam.put("businessHeatName", mapParam.get("businessHeatName"));
                    newParam.put("planBuyDateName", mapParam.get("planBuyDateName"));
                    newParam.put("planBuyDate", mapParam.get("planBuyDate"));
                    newParam.put("genderCode", mapParam.get("customerSexCode"));
                    newParam.put("genderName", mapParam.get("customerSexName"));
                    newParam.put("reviewPersonId", userBusiEntity.getUserID());
                    newParam.put("reviewPersonName", userBusiEntity.getEmpName());
                    newParam.put("planReviewTime", LocalDateTime.now().plusDays(+1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    // 来源渠道
                    newParam.put("systemSource", "ADP-DRIVE");

//                    Object cdpToken = redisUtil.get(CDP_ACCESS_TOKEN_REDIS_KEY);
//                    List<LookupValue> cdpSys = sacAppointmentSheetMapper.findCdpSys();
//                    Map<String, String> collectMap = cdpSys.stream().collect(Collectors
//                            .toMap(LookupValue::getLookupValueCode, LookupValue::getLookupValueName, (k1, k2) -> k2));
//                    HttpHeaders httpHeaders = new HttpHeaders();
//                    if (StringHelper.IsEmptyOrNull(cdpToken)) {
//
//                        try {
//                            String rsp = RequestClient.postData(collectMap.get("CDP_ACCESS_TOKEN_URL"), httpHeaders, new HashMap<String, String>());
//                            if (!StringHelper.IsEmptyOrNull(rsp)) {
//                                Map<String, Object> rspMap = JSONObject.parseObject(rsp, Map.class);
//                                Object access_token = rspMap.get("access_token");
//                                if (!StringHelper.IsEmptyOrNull(access_token)) {
//                                    cdpToken = access_token;
//                                    //   dlrClue.put("serverOrder", rspMap.get("data"));
//                                    redisUtil.set(CDP_ACCESS_TOKEN_REDIS_KEY, cdpToken, 3600);
//                                }
//                            }
//                        } catch (Exception ignored) {
//                            log.info("获取CDPtoken异常{}", ignored);
//                        }
//                    }
//
//                    String leadsUrl = collectMap.get("leadsUrl") + newParam.get("phone") + "?access_token=" + cdpToken;
//
//                    try {
//                        String rsp = RequestClient.getData(leadsUrl, httpHeaders, new HashMap<>());
//                        log.info("获取cdp三级渠道返回{}", rsp);
//                        if (!StringHelper.IsEmptyOrNull(rsp)) {
//                            CdpRsp cdpRsp = JSONObject.parseObject(rsp, CdpRsp.class);
//                            log.info("获取cdp三级渠道返回格式化{}", rsp);
//                            String c_third_channel = cdpRsp.getC_third_channel();
//                            if (!StringHelper.IsEmptyOrNull(c_third_channel)) {
//                                Map<String, String> thirdChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_072", c_third_channel);
//                                newParam.put("infoChanDCode", c_third_channel);
//                                newParam.put("infoChanDName", thirdChannel.get("lookupValueName"));
//                                String attribute1 = thirdChannel.get("attribute1");
//                                Map<String, String> secondChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_049", attribute1);
//
//                                newParam.put("infoChanMCode", attribute1);
//                                newParam.put("infoChanMName", secondChannel.get("lookupValueName"));
//                                newParam.put("channelCode", attribute1);
//                                newParam.put("channelName", secondChannel.get("lookupValueName"));
//                            }
//                        }
//                    } catch (Exception ignored) {
//                        log.info("获取CDP三级渠道异常{}", ignored);
//                    }
                    newParamBase.setParam(newParam);
                    EntityResult<Map<String, Object>> newClueResult = sacClueInfoDlrService.clueDlrSave(String.valueOf(mapParam.get("token")), newParamBase);
                    if ("0".equals(newClueResult.getResult())) {
                        throw new BusicenException("新建线索失败");
                    }
                    // 新建线索
                    newParamBase.getParam().putAll(newClueResult.getRows());


                    dlrClue.put("serverOrder", newParamBase.getParam().get("serverOrder"));
                    dlrClue.put("custId", newParamBase.getParam().get("custId"));
                    dlrClue.put("intenLevelCode", newParamBase.getParam().get("intenLevelCode"));
                    dlrClue.put("intenLevelName", newParamBase.getParam().get("intenLevelName"));
                } else {
                    dlrClue.put("serverOrder", clue.get("serverOrder"));
                    dlrClue.put("custId", clue.get("custId"));
                    dlrClue.put("intenLevelCode", clue.get("intenLevelCode"));
                    dlrClue.put("intenLevelName", clue.get("intenLevelName"));
                }
                // 获取客户信息
                mapParam.put("dlrClueOrderNo", dlrClue.get("serverOrder"));
                mapParam.put("customerId", dlrClue.get("custId"));
                mapParam.put("intenLevelCode", dlrClue.get("intenLevelCode"));
                mapParam.put("intenLevelName", dlrClue.get("intenLevelName"));
            }
            //不管新增还是修改，性别都能修改
            if (!StringHelper.IsEmptyOrNull(mapParam.get("customerSexCode"))) {
                mapParam.put("customerSex", mapParam.get("customerSexCode"));
            }
            if (skipClue) {
                mapParam.put("dlrClueOrderNo", "dlrClueOrderNo");
                mapParam.put("customerId", "customerId");
                mapParam.put("intenLevelCode", mapParam.get("intenLevelCode"));
                mapParam.put("intenLevelName", mapParam.get("intenLevelName"));
            }
        } catch (Exception e) {
            log.error("checkCustomerClue_performance异常", e);
            throw e;
        }
    }

    public void checkCustomerClueNew(Map<String, Object> mapParam) {
        // 试驾单新增线索逻辑
        try {
            Boolean updateFlag = (Boolean) mapParam.get("updateFlag");
            //新增时，需要判断线索是否存在
            if (!updateFlag) {

                ParamBase<Map<String, Object>> mapParamBase = new ParamBase<>();
                Map<String, Object> param = new HashMap<>();
                param.put("phone", mapParam.get("customerPhone"));
                param.put("token", String.valueOf(mapParam.get("token")));
                mapParamBase.setParam(param);
                // 先查询线索是否存在，存在则存线索单号与顾客id
                Map<String, Object> clue = sacClueInfoDlrService.clueDlrCheckRepeat(String.valueOf(mapParam.get("token")), mapParamBase).getRows();

                Map<String, Object> dlrClue = new HashMap<>();
                if (clue == null) {
                    // 根据token获取用户信息
                    String token = mapParam.get("token").toString();
                    // 根据token获取当前用户信息
                    UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
                    ParamBase<Map<String, Object>> newParamBase = new ParamBase<>();
                    Map<String, Object> newParam = new HashMap<>();
                    newParam.put("custName", mapParam.get("customerName"));
                    newParam.put("phone", mapParam.get("customerPhone"));
                    newParam.put("token", mapParam.get("token"));
                    if (!StringHelper.IsEmptyOrNull(mapParam.get("intenLevelCode"))) {
                        newParam.put("intenLevelCode", mapParam.get("intenLevelCode"));
                        newParam.put("intenLevelName", mapParam.get("intenLevelName"));
                    }
                    newParam.put("intenCarTypeCode", mapParam.get("smallCarTypeCode"));
                    newParam.put("intenCarTypeName", mapParam.get("smallCarTypeName"));
                    newParam.put("infoChanMCode", "agent_nature");
                    newParam.put("infoChanMName", "门店自然客流");
                    newParam.put("infoChanDCode", "agent_nature");
                    newParam.put("infoChanDName", "门店自然客流");
                    newParam.put("channelCode", "agent_nature");
                    newParam.put("channelName", "门店自然客流");
                    newParam.put("businessHeatCode", mapParam.get("businessHeatCode"));
                    newParam.put("businessHeatName", mapParam.get("businessHeatName"));
                    newParam.put("planBuyDateName", mapParam.get("planBuyDateName"));
                    newParam.put("planBuyDate", mapParam.get("planBuyDate"));
                    newParam.put("genderCode", mapParam.get("customerSexCode"));
                    newParam.put("genderName", mapParam.get("customerSexName"));
                    newParam.put("reviewPersonId", userBusiEntity.getUserID());
                    newParam.put("reviewPersonName", userBusiEntity.getEmpName());
                    newParam.put("planReviewTime", LocalDateTime.now().plusDays(+1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    // 来源渠道
                    newParam.put("systemSource", "ADP-DRIVE");

//                    Object cdpToken = redisUtil.get(CDP_ACCESS_TOKEN_REDIS_KEY);
//                    List<LookupValue> cdpSys = sacAppointmentSheetMapper.findCdpSys();
//                    Map<String, String> collectMap = cdpSys.stream().collect(Collectors
//                            .toMap(LookupValue::getLookupValueCode, LookupValue::getLookupValueName, (k1, k2) -> k2));
//                    HttpHeaders httpHeaders = new HttpHeaders();
//                    if (StringHelper.IsEmptyOrNull(cdpToken)) {
//
//                        try {
//                            String rsp = RequestClient.postData(collectMap.get("CDP_ACCESS_TOKEN_URL"), httpHeaders, new HashMap<String, String>());
//                            if (!StringHelper.IsEmptyOrNull(rsp)) {
//                                Map<String, Object> rspMap = JSONObject.parseObject(rsp, Map.class);
//                                Object access_token = rspMap.get("access_token");
//                                if (!StringHelper.IsEmptyOrNull(access_token)) {
//                                    cdpToken = access_token;
//                                    //   dlrClue.put("serverOrder", rspMap.get("data"));
//                                    redisUtil.set(CDP_ACCESS_TOKEN_REDIS_KEY, cdpToken, 3600);
//                                }
//                            }
//                        } catch (Exception ignored) {
//                            log.info("获取CDPtoken异常{}", ignored);
//                        }
//                    }
//
//                    String leadsUrl = collectMap.get("leadsUrl") + newParam.get("phone") + "?access_token=" + cdpToken;
//
//                    try {
//                        String rsp = RequestClient.getData(leadsUrl, httpHeaders, new HashMap<>());
//                        log.info("获取cdp三级渠道返回{}", rsp);
//                        if (!StringHelper.IsEmptyOrNull(rsp)) {
//                            CdpRsp cdpRsp = JSONObject.parseObject(rsp, CdpRsp.class);
//                            log.info("获取cdp三级渠道返回格式化{}", rsp);
//                            String c_third_channel = cdpRsp.getC_third_channel();
//                            if (!StringHelper.IsEmptyOrNull(c_third_channel)) {
//                                Map<String, String> thirdChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_072", c_third_channel);
//                                newParam.put("infoChanDCode", c_third_channel);
//                                newParam.put("infoChanDName", thirdChannel.get("lookupValueName"));
//                                String attribute1 = thirdChannel.get("attribute1");
//                                Map<String, String> secondChannel = sacAppointmentSheetMapper.findThirdLeads("ADP_CLUE_049", attribute1);
//
//                                newParam.put("infoChanMCode", attribute1);
//                                newParam.put("infoChanMName", secondChannel.get("lookupValueName"));
//                                newParam.put("channelCode", attribute1);
//                                newParam.put("channelName", secondChannel.get("lookupValueName"));
//                            }
//                        }
//                    } catch (Exception ignored) {
//                        log.info("获取CDP三级渠道异常{}", ignored);
//                    }
                    newParamBase.setParam(newParam);
                    EntityResult<Map<String, Object>> newClueResult = sacClueInfoDlrService.clueDlrSave(String.valueOf(mapParam.get("token")), newParamBase);
                    if ("0".equals(newClueResult.getResult())) {
                        throw new BusicenException("新建线索失败");
                    }
                    // 新建线索
                    newParamBase.getParam().putAll(newClueResult.getRows());


                    dlrClue.put("serverOrder", newParamBase.getParam().get("serverOrder"));
                    dlrClue.put("custId", newParamBase.getParam().get("custId"));
                    dlrClue.put("intenLevelCode", newParamBase.getParam().get("intenLevelCode"));
                    dlrClue.put("intenLevelName", newParamBase.getParam().get("intenLevelName"));
                } else {
                    dlrClue.put("serverOrder", clue.get("serverOrder"));
                    dlrClue.put("custId", clue.get("custId"));
                    dlrClue.put("intenLevelCode", clue.get("intenLevelCode"));
                    dlrClue.put("intenLevelName", clue.get("intenLevelName"));
                }
                // 获取客户信息
                mapParam.put("dlrClueOrderNo", dlrClue.get("serverOrder"));
                mapParam.put("customerId", dlrClue.get("custId"));
                mapParam.put("intenLevelCode", dlrClue.get("intenLevelCode"));
                mapParam.put("intenLevelName", dlrClue.get("intenLevelName"));
            }
            //不管新增还是修改，性别都能修改
            if (!StringHelper.IsEmptyOrNull(mapParam.get("customerSexCode"))) {
                mapParam.put("customerSex", mapParam.get("customerSexCode"));
            }
        } catch (Exception e) {
            log.error("checkCustomerClueNew异常", e);
            throw e;
        }
    }

    /**
     * 预约人数阈值校验
     *
     * @param mapParam
     */
    public void checkThresholdValue(Map<String, Object> mapParam) {
        try {
            String token = String.valueOf(mapParam.get("token"));
            // 查询系统配置中的试乘试驾预约人数阈值设置，DRIVE_NUM_SWITCH
            String configValue = "1";
            configValue = cacheDataFactory.querySysConfigValue("DRIVE_NUM_SWITCH", token);
            // 查询到配置时，做校验(排除更换门店情况)
            if (!StringHelper.IsEmptyOrNull(configValue)) {
                // 获取阈值
                int threshold = Integer.valueOf(configValue);
                // 查找同一时间这辆车已经被预约了多少人
                mapParam.put("isCheckRepeak", false);
                int testCount = sacAppointmentSheetMapper.checkRepeat(mapParam);
                if (testCount >= threshold) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-16"));
                }
                //查看这辆车这段时间内有没有被超长出库
                int longCount = sacAppointmentSheetMapper.checkLongRepeat(mapParam);
                if (longCount > 0) {
                    throw new BusicenException("该车辆这段时间被超长出库，不能预约");
                }

            }
        } catch (Exception e) {
            log.error("csc_clue_appointment_sheet_threshold", e);

            throw e;
        }
    }
    /**
     * 预约人数阈值校验
     *
     * @param mapParam
     */
    public void checkThresholdValue_performance(Map<String, Object> mapParam) {
        try {
            String token = String.valueOf(mapParam.get("token"));
            // 查询系统配置中的试乘试驾预约人数阈值设置，DRIVE_NUM_SWITCH
            String configValue = "1";
            Object driveNum = redisUtil.get(String.format("driveNumSwitch-%s", mapParam.get("dlrCode")));
            if(Objects.isNull(driveNum)) {
                configValue = cacheDataFactory.querySysConfigValue("DRIVE_NUM_SWITCH", token);
                redisUtil.set(String.format("driveNumSwitch-%s", mapParam.get("dlrCode")), configValue, 1, TimeUnit.HOURS);
            }
            else {
                configValue = (String) driveNum;
            }
            // 查询到配置时，做校验(排除更换门店情况)
            if (!StringHelper.IsEmptyOrNull(configValue)) {
                // 获取阈值
                int threshold = Integer.valueOf(configValue);
                // 查找同一时间这辆车已经被预约了多少人
                mapParam.put("isCheckRepeak", false);
//                Object testCountRedis = redisUtil.get(String.format("testCount-%s", mapParam.get("plateNumber")));
                int testCount = sacAppointmentSheetMapper.checkRepeat(mapParam);
                if (testCount >= threshold) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-16"));
                }
                //查看这辆车这段时间内有没有被超长出库
                int longCount = sacAppointmentSheetMapper.checkLongRepeat(mapParam);
                if (longCount > 0) {
                    throw new BusicenException("该车辆这段时间被超长出库，不能预约");
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_appointment_sheet_threshold", e);

            throw e;
        }
    }

    public void checkAppointmentCancelValidate(Map<String, Object> mapParam) {
        if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
            ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-appointment-cancel-check", "maindata");
            String resMsg = fireRule.getNotValidMessage();
            if (!fireRule.isValid()) {
                throw new BusicenException(resMsg);
            }
        }
    }

    public void getToken(Map<String, Object> mapParam) {
        if (StringHelper.IsEmptyOrNull(mapParam.get("userName"))) {
            throw new BusicenException("用户名称不能为空");
        }
        Map<String, Object> dataInfo = new HashMap<>();
        dataInfo.put("userName", mapParam.get("userName"));
        OptResult result = basedataFeignClient.createMpTokenInfo(dataInfo);
        if ("1".equals(result.getResult())) {
            mapParam.put("token", result.getMsg());
        } else {
            throw new BusicenException("token获取失败：" + result.getMsg());
        }

    }

    public void getTokenByParamBase(ParamBase<Map<String, Object>> mapParam) {
        if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("dlrCode"))) {
            throw new BusicenException("专营店编码不能为空");
        }
        Map<String, Object> dataInfo = new HashMap<>();
        dataInfo.put("dlrCode", mapParam.getParam().get("dlrCode"));
        OptResult result = basedataFeignClient.createMpTokenInfo(dataInfo);
        if ("1".equals(result.getResult())) {
            mapParam.getParam().put("token", result.getMsg());
        } else {
            throw new BusicenException("token获取失败：" + result.getMsg());
        }

    }

    public void checkAppointmentIdExits(Map<String, Object> mapParam) {
        if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
            try {
            //    String token = mapParam.get("token").toString();
                //   UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
                QueryWrapper<SacAppointmentSheet> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("APPOINTMENT_ID", mapParam.get("appointmentId"));
                // 判断主键是否存在
                List<SacAppointmentSheet> list = sacAppointmentSheetMapper.selectList(queryWrapper);
                if (list.size() == 0) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-08"));
                }
                // 已开始或已完成试驾的不能取消预约单
                if ("1".equals(list.get(0).getIsTestDrive())) {
                    throw new BusicenException(message.get("APPOINTMENT-SHEET-09"));
                }
                // 已经过期的试乘试驾预约单不能修改（当前时间大于等于预约单的开始时间）
			/*Calendar date = Calendar.getInstance();
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String dateString = simpleDateFormat.format(date.getTime());
			if (simpleDateFormat.parse(dateString)
					.after(simpleDateFormat.parse(list.get(0).getAppointmentStartTime()))) {
				throw new BusicenException(message.get("APPOINTMENT-SHEET-10"));
			}*/
            } catch (Exception e) {
                log.error("csc_clue_appointment_sheet_cancel_exits", e);
                throw new BusicenException(e.getMessage());
            }
        }
    }

    /**
     * 取消预约单后删除试乘试驾单(若试驾单存在的话)
     *
     * @param mapParam
     */
    public void deleteTestDriveSheet(Map<String, Object> mapParam) {
        if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
            try {
                if ("1".equals(mapParam.get("type"))) {
                    //校验带邀约码的试驾单不可以取消
                    Boolean flag = sacAppointmentSheetMapper.checkIsHaveColumn1(mapParam);
                    if (flag) {
                        return;
                    }
                }
                Map<String, Object> newParam = new HashMap<>();
                newParam.put("APPOINTMENT_ID", mapParam.get("appointmentId"));
                //根据预约单id查询试驾单
                List<SacTestDriveSheet> list = sacTestDriveSheetMapper.selectByMap(newParam);
                //若存在就将对应的试驾单改为不可用状态
                if (list.size() > 0) {
                    String token = mapParam.get("token").toString();
                    // 删除试乘试驾单
                    mapParam.put("isEnable", "0");
                    mapParam.put("testStatus", TestStatusEnum.CANCELLED.getCode());
                    mapParam.put("testDriveSheetId", list.get(0).getTestDriveSheetId());
                    BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
                    sacTestDriveSheetMapper.updateSacTestDriveSheet(mapParam);
                }
            } catch (Exception e) {
                throw e;
            }
        }
    }

    @Override
    @Interceptor("csc_clue_drive_capacity_app")
    public ListResult<Map<String, Object>> sacTestDriveCapacityQueryListApp(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<>();

        try {
            result = (ListResult<Map<String, Object>>) mapParam.getParam().get("result");
        } catch (Exception e) {
            log.error("sacTestDriveCapacityQueryListApp", e);
            throw e;
        }
        return result;
    }

    public void sendMessage(Map<String, Object> mapParam) {
        try {
            if (mapParam.containsKey("isSendMessage") && "1".equals(mapParam.get("isSendMessage"))) {
                Map<String, Object> paramM = new HashMap<>();
                Map<String, Object> param = new HashMap<>();
                param.put("time", mapParam.get("appointmentTestDate") + " " + mapParam.get("appointmentTestTime"));
                param.put("dlrShortName", mapParam.get("dlrName"));
                paramM.put("recNum", new String[]{mapParam.get("customerPhone").toString()});
                paramM.put("smsParam", param);
                paramM.put("smsTemplateId", "adp002");//模板id
                accPushFeignService.smsSendMessage(paramM);
            }
        } catch (Exception e) {
            log.error(String.format("appointmentSheetSave试驾预约发送短信%s", mapParam.get("customerPhone")), e);
        }
    }

    @Override
    public OptResult sactestdrivecapacitysendmessage(Map<String, Object> mapParam) {
        if ("1".equals(mapParam.get("testDriveMethod"))) {

            return OptResultBuilder.createOk().build();

        }
        Map<String, Object> paramM = new HashMap<>();
        Map<String, Object> param = new HashMap<>();
        param.put("dlrShortName", mapParam.get("dlrNameOwner"));
        paramM.put("recNum", new String[]{mapParam.get("phone").toString()});
        paramM.put("smsParam", param);
        paramM.put("smsTemplateId", "adp003");
        accPushFeignService.smsSendMessage(paramM);
        return OptResultBuilder.createOk().build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OptResult cancellationOfTestDriveTask(Map<String, Object> mapParam) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String token = request.getHeader(HEADER_TOKEN);
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        mapParam.put("modifier", userBusiEntity.getEmpID());
        mapParam.put("modifyName", userBusiEntity.getEmpName());

        int result = sacAppointmentSheetMapper.updateDriveTask(mapParam);
        if (result == 0) {
            return OptResultBuilder.createFail().build();
        }
        HashMap<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("event", "c_cancel_drive");
        paramMap.put("bk", mapParam.get("phone"));
        paramMap.put("date", BaseSpecialDateUtils.sysDateTZ());
        paramMap.put("logs_id", StringHelper.GetGUID());
        paramMap.put("configCode", "ADP_INSERT_CDP_LEADS_EVENT");
        Map<String, Object> param = new HashMap<>(1);
        param.put("mapParam", paramMap);
        accPushFeignService.sendCancleData(paramMap.get("configCode").toString(), param);
        return OptResultBuilder.createOk().build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OptResult testDriveCheckIn(String authentication, TestDriveCheckInReq testDriveCheckInReq) {
        log.info("testDriveCheckIn入参:{}", JSONObject.toJSONString(testDriveCheckInReq));
        // 获取当前用户信息
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(authentication);
        try {
            if(StringUtils.isNotEmpty(testDriveCheckInReq.getIDNumber())) {
                testDriveCheckInReq.setIDNumberEncryption(aesUtil.encrypt(testDriveCheckInReq.getIDNumber()));
            }
        }
        catch (Exception e) {
            log.error("身份证号加密失败", e);
        }
        // 执行更新操作
        int update = sacAppointmentSheetMapper.updateTestDriveSheet(testDriveCheckInReq, userBusiEntity.getEmpCode(), userBusiEntity.getEmpName());
        // 返回结果
        return update <= 0 ?
                CommonResultUitl.bizOpt(ResultCodeEnum.TEST_DRIVE_SIGN_FAILED)
                : CommonResultUitl.bizSuccessOpt(CommonConstants.TEST_DRIVE_SIGN_SUCCESS);
    }
}
