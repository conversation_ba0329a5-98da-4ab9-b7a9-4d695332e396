package com.ly.adp.drive.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.ly.adp.drive.otherservice.AdpMsUtil;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.StringHelper;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/24
 */
@Service
public class SactdaZtService {
    private final Logger logger = LoggerFactory.getLogger(SactdaZtService.class);
    @Autowired
    AdpMsUtil adpMsUtil;


    @Async
    public void sendTda(Map<String, Object> param, HashMap<String, Object> driveEndMap, Map<String, Object> urlMap) {

        // HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(param);
        HashMap<String, Object> driveMap = Maps.newHashMap();
        driveMap.put("drive_id", driveEndMap.get("testDriveOrderNo"));
        driveMap.put("user_id", driveEndMap.get("empCode"));

        long receptionEd = LocalDateTime.now().plusMinutes(30).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driveMap.put("reception_ed", receptionEd);
        Object receiverTime = driveEndMap.get("receiverTime");
        if (!StringHelper.IsEmptyOrNull(receiverTime)) {
            LocalDateTime parse = LocalDateTime.parse(receiverTime.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).minusMonths(30);
            long receptionBg = parse.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            driveMap.put("reception_bg", receptionBg);
        }

        HashMap<String, Object> driver = Maps.newHashMap();
        long driveEd = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driver.put("drive_ed", driveEd);
        long driveBg = LocalDateTime.parse(driveEndMap.get("startTime").toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driver.put("drive_bg", driveBg);
        driver.put("drive_car", driveEndMap.get("smallCarTypeCode"));
        List<Object> list = Lists.newArrayList();
        driver.put("drive_route", list);
        driveMap.put("drive_info", driver);
        HashMap<String, Object> clientInfo = Maps.newHashMap();
        clientInfo.put("client_name", driveEndMap.get("customerName"));
        clientInfo.put("client_phone", driveEndMap.get("customerPhone"));
        clientInfo.put("client_id", driveEndMap.get("customerId"));
        driveMap.put("client_info", clientInfo);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json; charset=UTF-8");
        //   System.out.println("JSONObject.toJSONString(driveMap) = " + JSONObject.toJSONString(driveMap));
        HttpEntity<Object> entity = new HttpEntity<>(JSONObject.toJSONString(driveMap), httpHeaders);
        //  Map<String, Object> urlMap = sacTestDriveSheetMapper.findUrl();
        try {
            if ("1".equals(urlMap.get("isEnable"))) {
                String postData = postData(urlMap.get("url").toString(), entity);
                logger.info("发送tdatda返回{}", postData);
                SacTestDriveSheetService.Rsp rspData = JSONObject.parseObject(postData, SacTestDriveSheetService.Rsp.class);
                if (!"0".equals(rspData.getCode())) {
                    logger.info("发送tda失败{}", postData);
                    throw new BusicenException(rspData.getMsg());

                }
            }
        } catch (Exception e) {
            logger.info("发送tda失败{}", e);
        }
    }

    @Async
    public void sendZtMQ(Map<String, Object> param, HashMap<String, Object> driveEndMap) {

        // HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(param);
        // HashMap<String, Object> driveMap = Maps.newHashMap();
        try {
            driveEndMap.put("endTime", param.get("endTime"));
            logger.info("试驾结束发送ZTMQ{}", driveEndMap);
            adpMsUtil.sendMq("adp_ms_base_driveEnd", driveEndMap);
        } catch (Exception e) {
            logger.info("试驾结束发送ZTMQ失败{}", e);
        }

    }

    public static String postData(String url, HttpEntity<Object> param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);


            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, param, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }

    public static class Rsp {
        String code;

        String msg;

        JSON data;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public JSON getData() {
            return data;
        }

        public void setData(JSON data) {
            this.data = data;
        }
    }
}
