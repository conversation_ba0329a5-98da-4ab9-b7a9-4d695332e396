package com.ly.adp.drive.controller;

import com.google.common.net.HttpHeaders;
import com.ly.adp.drive.entities.enums.TestStatusEnum;
import com.ly.adp.drive.entities.in.ProductSpecialistDTO;
import com.ly.adp.drive.entities.in.TestDriveTransferDTO;
import com.ly.adp.drive.entities.out.ProductSpecialistVO;
import com.ly.adp.drive.entities.req.GetBiVehicleDataJobReq;
import com.ly.adp.drive.entities.req.GetBiVehicleDataReq;
import com.ly.adp.drive.entities.vo.GetBiVehicleDataResultVO;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.adp.drive.service.ISacTestDriveSheetService;
import com.ly.mp.bucn.pack.entity.ParamBase;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.BusicenInvoker;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试乘试驾单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Api(value = "试乘试驾单管理", tags = {"试乘试驾单管理"})
@RestController
@RequestMapping("/ly/tdm/sactestdrivesheet")
public class SacTestDriveSheetController {

    @Autowired
    ISacTestDriveSheetService sacTestDriveSheetService;

    @ApiOperation(value = "试乘试驾单列表查询", notes = "试乘试驾单列表查询")
    @RequestMapping(value = "/sactestdrivesheetquerylist.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveSheetQueryList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetQueryList(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾单列表查询", notes = "试乘试驾单列表查询")
    @RequestMapping(value = "/sactestdrivesheetquerylist_performance.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveSheetQueryList_performance(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetQueryList_performance(mapParam)).result();
    }

    @ApiOperation(value = "个人试乘试驾单列表查询", notes = "个人试乘试驾单列表查询")
    @RequestMapping(value = "/sactestdrivesheetsinglelist.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetSingleList(mapParam)).result();
    }

    @ApiOperation(value = "个人试乘试驾单列表查询导出", notes = "个人试乘试驾单列表查询导出")
    @PostMapping(value = "/sactestdriveexport.do")
    public OptResult sactestdriveexport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                        @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveSheetSingleListExport(dataInfo, token, response)).result();
    }

    @ApiOperation(value = "补录试乘试驾单列表查询导入", notes = "补录试乘试驾单列表查询导入")
    @PostMapping(value = "/sactestdriveImport.do")
    public Result sactestdriveImport(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = true) MultipartFile uploadfile) {
        return sacTestDriveSheetService.sactestdriveImport(authentication, uploadfile);
    }

    @ApiOperation(value = "补录试驾单删除", notes = "补录试驾单删除")
    @PostMapping(value = "/sactestdrivedelete.do")
    public OptResult sactestdrivedelete(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sactestdrivedelete(mapParam.getParam())).result();
    }

    @ApiOperation(value = "补录试驾单导出", notes = "补录试驾单导出")
    @PostMapping(value = "/sacTestDriveReplenishExport.do")
    public OptResult sacTestDriveReplenishExport(@RequestHeader(HttpHeaders.AUTHORIZATION) String token, @RequestBody(required = false) Map<String, Object> dataInfo, HttpServletResponse response) {
        return BusicenInvoker.doOpt(() -> {
            return sacTestDriveSheetService.sacTestDriveReplenishExport(dataInfo, token, response);
        }).result();
    }

    @ApiOperation(value = "手动推送五星随手评", notes = "手动推送五星随手评")
    @RequestMapping(value = "/sacTestDriveSendMessage.do", method = RequestMethod.POST)
    public OptResult sacTestDriveSendMessage(@RequestHeader(name = "authorization", required = false) String authentication,
                                             @RequestBody(required = false) ParamBase<List<Map<String, Object>>> mapParam) {
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveSendMessage(mapParam.getParam())).result();
    }

    @ApiOperation(value = "个人试乘试驾单列表查询APP", notes = "个人试乘试驾单列表查询APP")
    @RequestMapping(value = "/sactestdrivesheetsinglelist.todo", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp(
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetSingleListApp(mapParam)).result();
    }

    @ApiOperation(value = "个人试乘试驾单列表查询APP", notes = "个人试乘试驾单列表查询APP")
    @RequestMapping(value = "/sactestdrivesheetsinglelistP.todo", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp_p(
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetSingleListApp_p(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾单详情查询", notes = "试乘试驾单详情查询")
    @RequestMapping(value = "/sactestdrivesheetquerydetail.do", method = RequestMethod.POST)
    public EntityResult<Map<String, Object>> sacTestDriveSheetQueryDetail(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doEntity(() -> sacTestDriveSheetService.sacTestDriveSheetQueryDetail(mapParam.getParam()))
                .result();
    }

    @ApiOperation(value = "试乘试驾单开始", notes = "试乘试驾单开始")
    @RequestMapping(value = "/sactestdrivesheetstart.do", method = RequestMethod.POST)
    public OptResult sacTestDriveSheetStart(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        mapParam.getParam().put("testStatus", TestStatusEnum.IN_PROGRESS.getCode());
        mapParam.getParam().put("startTime",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveSheetStatusRedisLock(mapParam.getParam()))
                .result();
    }

    @ApiOperation(value = "试乘试驾单结束", notes = "试乘试驾单结束")
    @RequestMapping(value = "/sactestdrivesheetend.do", method = RequestMethod.POST)
    public OptResult sacTestDriveSheetEnd(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        mapParam.getParam().put("testStatus", TestStatusEnum.COMPLETED.getCode());
        mapParam.getParam().put("endTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveSheetStatusRedisLock(mapParam.getParam()))
                .result();
    }

    @ApiOperation(value = "试乘试驾单保存", notes = "试乘试驾单保存")
    @RequestMapping(value = "/sactestdrivesheetsave.do", method = RequestMethod.POST)
    public OptResult sacTestDriveSheetSave(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveSheetSave(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试乘试驾协议保存", notes = "试乘试驾协议保存")
    @RequestMapping(value = "/sactestdriveagreementsave.do", method = RequestMethod.POST)
    public OptResult sacTestDriveAgreementSave(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.sacTestDriveAgreementSave(mapParam.getParam()))
                .result();
    }

    @ApiOperation(value = "试乘试驾协议查询", notes = "试乘试驾协议查询")
    @RequestMapping(value = "/sactestdriveagreementquerylist.do", method = RequestMethod.POST)
    public ListResult<Map<String, Object>> sacTestDriveAgreementQueryList(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamPage<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        mapParam.getParam().put("isAgreement", true);
        return BusicenInvoker.doList(() -> sacTestDriveSheetService.sacTestDriveSheetQueryList(mapParam)).result();
    }

    @ApiOperation(value = "试乘试驾单保存(包含预约单)", notes = "试乘试驾单保存(包含预约单)")
    @RequestMapping(value = "/sactestdrivesheetallsave.do", method = RequestMethod.POST)
    public EntityResult<Map<String, Object>> sacTestDriveSheetAllSave(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doEntity(() -> sacTestDriveSheetService.sacTestDriveSheetAllSave(mapParam.getParam())).result();
    }
    @ApiOperation(value = "试乘试驾单保存(包含预约单)", notes = "试乘试驾单保存(包含预约单)")
    @RequestMapping(value = "/sactestdrivesheetallsave_performance.do", method = RequestMethod.POST)
    public EntityResult<Map<String, Object>> sacTestDriveSheetAllSave_performance(
            @RequestHeader(name = "authorization", required = false) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> mapParam) {
        mapParam.getParam().put("token", authentication);
        return BusicenInvoker.doEntity(() -> sacTestDriveSheetService.sacTestDriveSheetAllSave_performance(mapParam.getParam())).result();
    }

    @ApiOperation(value = "试驾转移", notes = "试驾转移")
    @RequestMapping(value = "/testDriveTransfer.do", method = RequestMethod.POST)
    public OptResult testDriveTransfer(

            @RequestBody(required = false) TestDriveTransferDTO testDriveTransferDTO) {

        return BusicenInvoker.doOpt(() -> sacTestDriveSheetService.testDriveTransfer(testDriveTransferDTO)).result();

    }

    @ApiOperation(value = "试驾转移产品专家", notes = "试驾转移")
    @RequestMapping(value = "/findProductSpecialist.do", method = RequestMethod.POST)
    public ListResult<ProductSpecialistVO> findProductSpecialist(

            @RequestBody(required = false) ProductSpecialistDTO productSpecialist) {
        return sacTestDriveSheetService.findProductSpecialist(productSpecialist);
    }


    @ApiOperation(value = "通过试驾单号查询车机数据（企微）", notes = "通过试驾单号查询车机数据（企微）")
    @GetMapping(value = "/queryVehicleData.do/{testDriveOrderNo}")
    public EntityResult<VehicleDataVO> queryVehicleData(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @PathVariable("testDriveOrderNo") String testDriveOrderNo){
        return sacTestDriveSheetService.queryVehicleData(testDriveOrderNo);
    }

    @ApiOperation(value = "获取BI车机数据", notes = "获取BI车机数据")
    @PostMapping(value = "/getBiVehicleData.do")
    public ListResult<GetBiVehicleDataResultVO> getBiVehicleData(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody GetBiVehicleDataReq req){
        GetBiVehicleDataJobReq dataJobReq = new GetBiVehicleDataJobReq();
        dataJobReq.setManual(true);
        dataJobReq.setTestDriveOrderNo(req.getTestDriveOrderNo());
        return sacTestDriveSheetService.getBiVehicleData(dataJobReq, authentication);
    }

    @ApiOperation(value = "获取BI车机数据(JOB用)", notes = "获取BI车机数据(JOB用)")
    @PostMapping(value = "/getBiVehicleData")
    public ListResult<GetBiVehicleDataResultVO> getBiVehicleDataForJob(@RequestBody GetBiVehicleDataJobReq req){
        return sacTestDriveSheetService.getBiVehicleData(req, "");
    }
}
