package com.ly.adp.drive.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES工具类
 *
 * <AUTHOR>
 * @date 2025/6/26
 */
@Component
public class AESUtil {

    @Value("${aes.secretKey}")
    private String secretKey;

    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int TAG_LENGTH_BIT = 128;
    private static final int IV_LENGTH_BYTE = 12;

    /**
     * 加密
     *
     * @param encryptStr String
     * @return String
     */
    public String encrypt(String encryptStr) throws Exception {
        return encrypt(secretKey.getBytes(StandardCharsets.UTF_8), encryptStr);
    }

    /**
     * 解密
     *
     * @param decryptStr String
     * @return String
     */
    public String decrypt(String decryptStr) throws Exception {
        return decrypt(secretKey.getBytes(StandardCharsets.UTF_8), decryptStr);
    }

    /**
     * 加密
     *
     * @param key        byte[]
     * @param encryptStr String
     * @return String
     */
    public String encrypt(byte[] key, String encryptStr) throws Exception {
        // 随机IV
        byte[] iv = new byte[IV_LENGTH_BYTE];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(iv);
        // 初始化密钥和加密器
        SecretKey secretKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);
        // 加密
        byte[] encryptedBytes = cipher.doFinal(encryptStr.getBytes(StandardCharsets.UTF_8));
        // 组合IV和密文
        byte[] combined = new byte[iv.length + encryptedBytes.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);
        // 编码
        return Base64.getEncoder().encodeToString(combined);
    }

    /**
     * 解密
     *
     * @param key        byte[]
     * @param decryptStr String
     * @return String
     */
    public String decrypt(byte[] key, String decryptStr) throws Exception {
        // 解码
        byte[] combined = Base64.getDecoder().decode(decryptStr);
        // 分离IV和密文
        byte[] iv = new byte[IV_LENGTH_BYTE];
        System.arraycopy(combined, 0, iv, 0, iv.length);
        byte[] encryptedBytes = new byte[combined.length - IV_LENGTH_BYTE];
        System.arraycopy(combined, IV_LENGTH_BYTE, encryptedBytes, 0, encryptedBytes.length);
        // 初始化密钥和解密器
        SecretKey secretKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmParameterSpec);
        // 解密
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

}
