package com.ly.adp.drive.util;

import com.ly.mp.busicen.common.context.BusicenException;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/1/9
 */
public class RequestClient {

    private static final Logger logger = LoggerFactory.getLogger(RequestClient.class);


    public static String getData(String url, HttpHeaders head, Map<String, String> param) {
        ResponseEntity<String> responseEntity = null;


        HttpEntity<Object> entity = new HttpEntity<>(param, head);

        try {
            responseEntity = getRestTemplate().exchange(url, HttpMethod.GET, entity, String.class);
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            logger.info("自定义请求失败{}",e);
            throw new BusicenException("请求失败");

        }


        return responseEntity.getBody();
    }


    public static String postData(String url, HttpHeaders httpHeaders, Map<String, String> head) {
        ResponseEntity<String> responseEntity = null;


        HttpEntity<Object> entity = new HttpEntity<>(httpHeaders);

         try {
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            logger.info("自定义请求失败{}",e);
            throw new BusicenException("请求失败");
        }


        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }
}
