package com.ly.adp.drive.entities.in;

import java.io.Serializable;
import java.util.Map;

public class FieldMappingIn implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 源表编码
     */
    private String sourceTableCode;
    
    
    /**
     * 单据类型
     */
    private String billType;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 需要转换的参数
     */
    private Map<String,Object> param;
    
    /**
     * 原来保存的json
     */
    private String oldJson;

	public String getSourceTableCode() {
		return sourceTableCode;
	}

	public void setSourceTableCode(String sourceTableCode) {
		this.sourceTableCode = sourceTableCode;
	}

	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Map<String, Object> getParam() {
		return param;
	}

	public void setParam(Map<String, Object> param) {
		this.param = param;
	}
	

	public String getOldJson() {
		return oldJson;
	}

	public void setOldJson(String oldJson) {
		this.oldJson = oldJson;
	}

	@Override
	public String toString() {
		return "FieldMappingIn [sourceTableCode=" + sourceTableCode + ", billType=" + billType + ", businessType="
				+ businessType + ", param=" + param + ", oldJson=" + oldJson + "]";
	}
	
    
    

}
