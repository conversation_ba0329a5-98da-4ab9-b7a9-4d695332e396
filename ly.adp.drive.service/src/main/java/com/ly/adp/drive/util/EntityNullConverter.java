package com.ly.adp.drive.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class EntityNullConverter {
    private static final Logger log = LoggerFactory.getLogger(EntityNullConverter.class);
    
    // 缓存类的字段信息，避免重复反射
    private static final Map<Class<?>, Field[]> FIELDS_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 将实体类中的null字符串转换为空字符串
     *
     * @param entity 需要转换的实体对象
     * @param <T> 实体类型
     * @return 转换后的实体对象
     */
    public static <T> T convertNullToEmpty(T entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            // 获取缓存的字段信息
            Field[] fields = FIELDS_CACHE.computeIfAbsent(
                entity.getClass(),
                clazz -> clazz.getDeclaredFields()
            );
            
            // 遍历所有字段
            for (Field field : fields) {
                // 只处理String类型的字段
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    if (value == null) {
                        field.set(entity, "");
                    }
                }
            }
            
            return entity;
        } catch (Exception e) {
            log.error("Convert null to empty string failed for entity: {}", entity.getClass().getName(), e);
            return entity;
        }
    }
    
    /**
     * 批量转换实体类列表
     *
     * @param entities 实体对象列表
     * @param <T> 实体类型
     * @return 转换后的实体对象列表
     */
    public static <T> List<T> convertListNullToEmpty(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }
        
        entities.forEach(EntityNullConverter::convertNullToEmpty);
        return entities;
    }
    
    /**
     * 转换指定字段
     *
     * @param entity 实体对象
     * @param fieldNames 需要转换的字段名数组
     * @param <T> 实体类型
     * @return 转换后的实体对象
     */
    public static <T> T convertSpecificFieldsNullToEmpty(T entity, String... fieldNames) {
        if (entity == null || fieldNames == null || fieldNames.length == 0) {
            return entity;
        }
        
        try {
            for (String fieldName : fieldNames) {
                Field field = entity.getClass().getDeclaredField(fieldName);
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    if (value == null) {
                        field.set(entity, "");
                    }
                }
            }
            
            return entity;
        } catch (Exception e) {
            log.error("Convert specific fields null to empty string failed for entity: {}", 
                     entity.getClass().getName(), e);
            return entity;
        }
    }
}