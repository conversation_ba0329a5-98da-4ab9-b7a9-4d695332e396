package com.ly.adp.drive.entities.enums;

/**
 * 发送企微消息消息类型枚举
 * <AUTHOR>
 * @Date 2024/7/26
 * @Version 1.0.0
 **/
public enum SendWeComMsgTypeEnum {
    text("text", "文本消息"),
    image("image", "图片消息"),
    textcard("textcard", "文本卡片消息");

    private final String code;
    private final String description;

    SendWeComMsgTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SendWeComMsgTypeEnum fromCode(String code) {
        for (SendWeComMsgTypeEnum sendWeComMsgTypeEnum : SendWeComMsgTypeEnum.values()) {
            if (sendWeComMsgTypeEnum.getCode().equalsIgnoreCase(code)) {
                return sendWeComMsgTypeEnum;
            }
        }
        return null;
    }
}
