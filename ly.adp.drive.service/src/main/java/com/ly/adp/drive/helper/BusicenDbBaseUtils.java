package com.ly.adp.drive.helper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.mp.busi.base.constant.ErrorEnum;
import com.ly.mp.busi.base.constant.UserBusiEntity;
import com.ly.mp.busi.base.context.BusicenException;
import com.ly.mp.busi.base.handler.BusicenUtils;
import com.ly.mp.busi.base.handler.BusicenUtils.saveOrUpdate;
import com.ly.mp.busi.base.handler.ObjConvertToNull;
import com.ly.mp.busi.base.handler.ResultHandler;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.Reflections;
import com.ly.mp.component.helper.StringHelper;


public class BusicenDbBaseUtils {
	private static Logger log = LoggerFactory.getLogger(BusicenDbBaseUtils.class);

	public static <T> ListResult<T> baseQueryFindAll(int pageIndex, int pageSize, T info, BaseMapper<T> baseMapper) {
		// 返回参数
		ListResult<T> returnResult = new ListResult<>();
		try {
			// 获取分页数据
			IPage<T> list = baseMapper.selectPage(
					new Page<>(pageIndex <= 0 ? 1 : pageIndex, pageSize <= 0 ? 100 : pageSize),
					new QueryWrapper<T>(info));
			// 构造返回参数
			returnResult = BusicenUtils.page2ListResult(list);
		} catch (Exception e) {
			log.error("BaseQueryFindAll", e);
			// 抛出RuntimeException，事务才会回滚
			throw e;
		}
		return returnResult;

	}

	/**
	 * 保存 根据主键ID判断新增还是修改 自动填充公共字段 修改时，会校验并发字段
	 * 
	 * @param info
	 * @param baseMapper
	 * @return
	 */
	public static <T> EntityResult<T> baseSaveById(T info, BaseMapper<T> baseMapper,String token) {

		try {
			String saveType = "";

			// 验证参数是否为空
			if (info == null) {
				return ResultHandler.updateError(ErrorEnum.PARAM_ERROR.getResult(), ErrorEnum.PARAM_ERROR.getMsg(),
						"参数不能为空");
			}

			// 获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(info.getClass());
			//TableInfo tableInfo = SqlHelper.table(info.getClass());

			// [start] 判断新增还是修改
			String keyFiledMybatis = tableInfo.getKeyProperty();// 实体类主键字段编码
			String keyFiledCode = tableInfo.getKeyColumn();// 数据库主键字段编码
			String keyFiledValue = "";// 主键值
			try {
				keyFiledValue = (String) Reflections.getFieldValue(info, keyFiledMybatis);
			} catch (Exception e) {

			}
			if (!StringHelper.IsEmptyOrNull(keyFiledValue)) {

				T keyObject = baseMapper.selectById(keyFiledValue);
				if (keyObject != null) {
					// 根据主键获取到数据，则为更新
					saveType = "UPDATE";
				} else {
					// 根据主键获取不到数据，则为插入
					saveType = "INSERT";
				}
			} else {
				// 主键为空，则为插入
				saveType = "INSERT";

				// 主键默认guid
				keyFiledValue = StringHelper.GetGUID();
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);
			}
			boolean saveflag;
			if ("INSERT".equals(saveType)) {
				// 插入
				BusicenUtils.invokeUserInfo(info, saveOrUpdate.Save,token);
				saveflag = baseMapper.insert(info) > 0 ? true : false;
			} else {
				// 并发控制更新

				// 获取传入的并发字段值
				Object upTemp = Reflections.getFieldValue(info, "updateControlId");
				String updateControlId = ""; // info.getUpdateControlId();
				if (!StringHelper.IsEmptyOrNull(upTemp)) {
					updateControlId = upTemp.toString();
				} else {
					// 抛出RuntimeException，事务才会回滚
					throw BusicenException.create("缺少并发控制字段,请重试");
				}

				// 主键和并发字段
				QueryWrapper<T> updateWrapper = new QueryWrapper<>();
				updateWrapper.eq("UPDATE_CONTROL_ID", updateControlId).eq(keyFiledCode, keyFiledValue);

				// 更新并发控制Id updateDateControlId
				// Reflections.invokeSetter(info, "updateControlId", StringHelper.GetGUID());
				BusicenUtils.invokeUserInfo(info, saveOrUpdate.Update,token);

				saveflag = baseMapper.update(info, updateWrapper) > 0 ? true : false;
			}
			if (!saveflag) {
				// 抛出RuntimeException，事务才会回滚
				throw BusicenException.create("并发操作,请重试");
			}
			// 操作成功
			return ResultHandler.updateOk(baseMapper.selectOne(new QueryWrapper<T>(info)));

		} catch (Exception e) {
			log.error("dbNodeSave", e);
			// 抛出RuntimeException，事务才会回滚
			throw  e;
		}

	}
	
	

	/**
	 * 保存 根据主键ID判断新增还是修改 自动填充公共字段 修改时，会校验并发字段
	 * 
	 * @param info
	 * @param baseMapper
	 * @param checkFileds   查重字段
	 * @param repeatMsg     查重重复时返回信息
	 * @param checkUpdateId 是否校验并发
	 * @return
	 */
	public static <T> EntityResult<T> baseSaveById(T info, BaseMapper<T> baseMapper, String[] checkFileds,
			String repeatMsg, Boolean checkUpdateId,String token) {

		EntityResult<T> resultRepeat = new EntityResult<T>();

		try {
			String saveType = "";

			// 验证参数是否为空
			if (info == null) {
				return ResultHandler.updateError(ErrorEnum.PARAM_ERROR.getResult(), ErrorEnum.PARAM_ERROR.getMsg(),
						"参数不能为空");
			}

			// 获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(info.getClass());
			//TableInfo tableInfo = SqlHelper.table(info.getClass());
			// [start] 判断新增还是修改
			String keyFiledMybatis = tableInfo.getKeyProperty();// 实体类主键字段编码
			String keyFiledCode = tableInfo.getKeyColumn();// 数据库主键字段编码
			String keyFiledValue = "";// 主键值
			try {
				keyFiledValue = (String) Reflections.getFieldValue(info, keyFiledMybatis);
			} catch (Exception e) {

			}
			if (!StringHelper.IsEmptyOrNull(keyFiledValue)) {

				T keyObject = baseMapper.selectById(keyFiledValue);
				if (keyObject != null) {
					// 根据主键获取到数据，则为更新
					saveType = "UPDATE";
				} else {
					// 根据主键获取不到数据，则为插入
					saveType = "INSERT";
				}
			} else {
				// 主键为空，则为插入
				saveType = "INSERT";

				// 主键默认guid
				keyFiledValue = StringHelper.GetGUID();
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);
			}


			// [start] 查重
			//T checkInfo = null;
			if (checkFileds !=null && checkFileds.length > 0) {
				// 根据字段获取新实体
				T checkInfo = ObjConvertToNull.copyObjFromFileds(info, checkFileds);
				// 主键设为null 查重排除主键
				Reflections.setFieldValue(checkInfo, keyFiledMybatis, null);
				// 排除主键 获取数据 这里Map的键是mabatis实体字段编码，不是数据库字段编码
				List<Map<String, Object>> checkList = baseMapper.selectMaps(new QueryWrapper<T>(checkInfo));
				// 大于1时必定重复，如果只有1个，则与当前主键相比，不相同则数据重复
				if (checkList.size() > 1 || (checkList.size() > 0
						&& !keyFiledValue.equals(checkList.get(0).get(keyFiledMybatis).toString()))) {

					throw BusicenException.create(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//resultRepeat.setResult("0");
					//resultRepeat.setMsg(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//return resultRepeat;
				}
			}
			// [end]

			boolean saveflag;
			if ("INSERT".equals(saveType)) {
				// 填充字段
				BusicenUtils.invokeUserInfo(info, BusicenUtils.SOU.Save,token);
				// 插入
				saveflag = baseMapper.insert(info) > 0 ? true : false;
			} else {
				// 更新条件
				QueryWrapper<T> updateWrapper = new QueryWrapper<>();
				updateWrapper.eq(keyFiledCode, keyFiledValue);// 根据主键更新
				// 并发控制
				if (checkUpdateId) {
					// 获取传入的并发字段值
					Object upTemp = Reflections.getFieldValue(info, "updateControlId");
					String updateControlId = ""; // info.getUpdateControlId();
					if (!StringHelper.IsEmptyOrNull(upTemp)) {
						updateControlId = upTemp.toString();
					} else {
						// 抛出RuntimeException，事务才会回滚
						throw BusicenException.create("缺少并发控制字段,请重试");
					}
					// 主键和并发字段
					updateWrapper.eq("UPDATE_CONTROL_ID", updateControlId);
				}

				// 填充公共字段
				BusicenUtils.invokeUserInfo(info, BusicenUtils.SOU.Update,token);
				// 更新
				Reflections.setFieldValue(info, keyFiledMybatis, null);//不更新主键
				saveflag = baseMapper.update(info, updateWrapper) > 0 ? true : false;
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);//把主键给回实体
			}
			if (!saveflag) {
				// 抛出RuntimeException，事务才会回滚
				throw BusicenException.create("并发操作,请重试!");
			}
			// 操作成功
			QueryWrapper<T> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq(keyFiledCode, keyFiledValue);// 根据主键查询
			return ResultHandler.updateOk(baseMapper.selectOne(queryWrapper));

		} catch (Exception e) {
			log.error("BusicenDbBaseUtils.baseSaveById", e);
			// 抛出RuntimeException，事务才会回滚
			throw e;
		}

	}
	
	
	/**
	 * 保存 根据主键ID判断新增还是修改 自动填充公共字段 修改时，会校验并发字段
	 * 
	 * @param info
	 * @param baseMapper
	 * @param checkFileds   查重字段
	 * @param repeatMsg     查重重复时返回信息
	 * @param checkUpdateId 是否校验并发
	 * @param user 操作用户
	 * @return
	 */
	public static <T> EntityResult<T> baseSaveById(T info, BaseMapper<T> baseMapper, String[] checkFileds,
			String repeatMsg, Boolean checkUpdateId,UserBusiEntity user,String token) {

		EntityResult<T> resultRepeat = new EntityResult<T>();

		try {
			String saveType = "";

			// 验证参数是否为空
			if (info == null) {
				return ResultHandler.updateError(ErrorEnum.PARAM_ERROR.getResult(), ErrorEnum.PARAM_ERROR.getMsg(),
						"参数不能为空");
			}

			// 获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(info.getClass());
			//TableInfo tableInfo = SqlHelper.table(info.getClass());
			// [start] 判断新增还是修改
			String keyFiledMybatis = tableInfo.getKeyProperty();// 实体类主键字段编码
			String keyFiledCode = tableInfo.getKeyColumn();// 数据库主键字段编码
			String keyFiledValue = "";// 主键值
			try {
				keyFiledValue = (String) Reflections.getFieldValue(info, keyFiledMybatis);
			} catch (Exception e) {

			}
			if (!StringHelper.IsEmptyOrNull(keyFiledValue)) {

				T keyObject = baseMapper.selectById(keyFiledValue);
				if (keyObject != null) {
					// 根据主键获取到数据，则为更新
					saveType = "UPDATE";
				} else {
					// 根据主键获取不到数据，则为插入
					saveType = "INSERT";
				}
			} else {
				// 主键为空，则为插入
				saveType = "INSERT";

				// 主键默认guid
				keyFiledValue = StringHelper.GetGUID();
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);
			}

			// [start] 查重
			//T checkInfo = null;
			if (checkFileds !=null && checkFileds.length > 0) {
				// 根据字段获取新实体
				T checkInfo = ObjConvertToNull.copyObjFromFileds(info, checkFileds);
				// 主键设为null 查重排除主键
				Reflections.setFieldValue(checkInfo, keyFiledMybatis, null);
				// 排除主键 获取数据 这里Map的键是mabatis实体字段编码，不是数据库字段编码
				List<Map<String, Object>> checkList = baseMapper.selectMaps(new QueryWrapper<T>(checkInfo));
				// 大于1时必定重复，如果只有1个，则与当前主键相比，不相同则数据重复
				if (checkList.size() > 1 || (checkList.size() > 0
						&& !keyFiledValue.equals(checkList.get(0).get(keyFiledMybatis).toString()))) {

					throw BusicenException.create(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//resultRepeat.setResult("0");
					//resultRepeat.setMsg(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//return resultRepeat;
				}
			}
			// [end]

			boolean saveflag;
			if ("INSERT".equals(saveType)) {
				// 填充字段
				BusicenUtils.invokeUserInfo(info,saveOrUpdate.Save,user,token);
				//BusicenUtils.invokeUserInfo(info, 1);
				// 插入
				saveflag = baseMapper.insert(info) > 0 ? true : false;
			} else {
				// 更新条件
				QueryWrapper<T> updateWrapper = new QueryWrapper<>();
				updateWrapper.eq(keyFiledCode, keyFiledValue);// 根据主键更新
				// 并发控制
				if (checkUpdateId) {
					// 获取传入的并发字段值
					Object upTemp = Reflections.getFieldValue(info, "updateControlId");
					String updateControlId = ""; // info.getUpdateControlId();
					if (!StringHelper.IsEmptyOrNull(upTemp)) {
						updateControlId = upTemp.toString();
					} else {
						// 抛出RuntimeException，事务才会回滚
						throw BusicenException.create("缺少并发控制字段,请重试");
					}
					// 主键和并发字段
					updateWrapper.eq("UPDATE_CONTROL_ID", updateControlId);
				}

				// 填充公共字段
				BusicenUtils.invokeUserInfo(info,saveOrUpdate.Update,user,token);
				//BusicenUtils.invokeUserInfo(info, 0);
				// 更新
				Reflections.setFieldValue(info, keyFiledMybatis, null);//不更新主键
				saveflag = baseMapper.update(info, updateWrapper) > 0 ? true : false;
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);//把主键给回实体
			}
			if (!saveflag) {
				// 抛出RuntimeException，事务才会回滚
				throw BusicenException.create("并发操作,请重试!");
			}
			// 操作成功
			QueryWrapper<T> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq(keyFiledCode, keyFiledValue);// 根据主键查询
			return ResultHandler.updateOk(baseMapper.selectOne(queryWrapper));

		} catch (Exception e) {
			log.error("BusicenDbBaseUtils.baseSaveById", e);
			// 抛出RuntimeException，事务才会回滚
			throw e;
		}

	}
	/**
	 * @Description: 该函数的功能描述
	 * @version: v1.0.0
	 * @author: ly-lijindi
	 * @date: 2019年8月13日 下午3:35:39 Modification History: Date Author Version
	 *        Description ---------------------------------------------------------*
	 *        2019年8月13日 ly-lijindi v1.0.0 修改原因
	 */
	public static <T> String baseSave(T info, BaseMapper<T> baseMapper,String token) {
		String strResult = "";
		try {
			String saveType = "";
			// 获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(info.getClass());
			//TableInfo tableInfo = SqlHelper.table(info.getClass());
			// [start] 判断新增还是修改
			String keyFiledMybatis = tableInfo.getKeyProperty();// 实体类主键字段编码
			String keyFiledCode = tableInfo.getKeyColumn();// 数据库主键字段编码
			String keyFiledValue = "";// 主键值
			try {
				keyFiledValue = (String) Reflections.getFieldValue(info, keyFiledMybatis);
			} catch (Exception e) {
				throw e;
			}
			if (!StringHelper.IsEmptyOrNull(keyFiledValue)) {
				T keyObject = baseMapper.selectById(keyFiledValue);
				if (keyObject != null) {
					// 根据主键获取到数据，则为更新
					saveType = "UPDATE";
				} else {
					// 根据主键获取不到数据，则为插入
					saveType = "INSERT";
				}
			} else {
				// 主键为空，则为插入
				saveType = "INSERT";
				// 主键默认guid
				keyFiledValue = StringHelper.GetGUID();
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);
			}
			if ("INSERT".equals(saveType)) {
				// 插入
				BusicenUtils.invokeUserInfo(info, BusicenUtils.saveOrUpdate.Save,token);
				strResult = baseMapper.insert(info) > 0 ? "3" : "2";
			} else {
				// 获取传入的并发字段值
				String updateControlId = Reflections.getFieldValue(info, "updateControlId").toString();
				// 主键和并发字段
				QueryWrapper<T> updateWrapper = new QueryWrapper<>();
				updateWrapper.eq("UPDATE_CONTROL_ID", updateControlId).eq(keyFiledCode, keyFiledValue);
				// 更新并发控制Id updateDateControlId
				BusicenUtils.invokeUserInfo(info, BusicenUtils.saveOrUpdate.Update,token);
				strResult = baseMapper.update(info, updateWrapper) > 0 ? "3" : "5";
			}
		} catch (Exception e) {
			throw e;
		}
		return strResult;//返回操作状态码
	}
	
	/**
	 * 保存 根据主键ID判断新增还是修改 自动填充公共字段 修改时，会校验并发字段
	 * 
	 * @param info
	 * @param baseMapper
	 * @param checkFileds   查重字段
	 * @param repeatMsg     查重重复时返回信息
	 * @param checkUpdateId 是否校验并发
	 * @return
	 */
	public static <T> OptResult baseSaveByIdOpt(T info, BaseMapper<T> baseMapper, String[] checkFileds,
			String repeatMsg, Boolean checkUpdateId,String token) {

		OptResult resultRepeat = new OptResult();

		try {
			String saveType = "";

			// 验证参数是否为空
			if (info == null) {
				return ResultHandler.updateError(ErrorEnum.PARAM_ERROR.getResult(), ErrorEnum.PARAM_ERROR.getMsg());
			}

			// 获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(info.getClass());
			//TableInfo tableInfo = SqlHelper.table(info.getClass());
			// [start] 判断新增还是修改
			String keyFiledMybatis = tableInfo.getKeyProperty();// 实体类主键字段编码
			String keyFiledCode = tableInfo.getKeyColumn();// 数据库主键字段编码
			String keyFiledValue = "";// 主键值
			try {
				keyFiledValue = (String) Reflections.getFieldValue(info, keyFiledMybatis);
			} catch (Exception e) {

			}
			if (!StringHelper.IsEmptyOrNull(keyFiledValue)) {

				T keyObject = baseMapper.selectById(keyFiledValue);
				if (keyObject != null) {
					// 根据主键获取到数据，则为更新
					saveType = "UPDATE";
				} else {
					// 根据主键获取不到数据，则为插入
					saveType = "INSERT";
				}
			} else {
				// 主键为空，则为插入
				saveType = "INSERT";

				// 主键默认guid
				keyFiledValue = StringHelper.GetGUID();
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);
			}

			// [end]
			// [start] 查重
			//T checkInfo = null;
			if (checkFileds !=null && checkFileds.length > 0) {
				// 根据字段获取新实体
				T checkInfo = ObjConvertToNull.copyObjFromFileds(info, checkFileds);
				// 主键设为null 查重排除主键
				Reflections.setFieldValue(checkInfo, keyFiledMybatis, null);
				// 排除主键 获取数据 这里Map的键是mabatis实体字段编码，不是数据库字段编码
				List<Map<String, Object>> checkList = baseMapper.selectMaps(new QueryWrapper<T>(checkInfo));
				// 大于1时必定重复，如果只有1个，则与当前主键相比，不相同则数据重复
				if (checkList.size() > 1 || (checkList.size() > 0
						&& !keyFiledValue.equals(checkList.get(0).get(keyFiledMybatis).toString()))) {

					
					throw BusicenException.create(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//resultRepeat.setResult("0");
					//resultRepeat.setMsg(StringHelper.IsEmptyOrNull(repeatMsg) ? "操作失败，数据重复！" : repeatMsg);
					//return resultRepeat;
				}
			}
			// [end]

			boolean saveflag;
			if ("INSERT".equals(saveType)) {
				// 填充字段
				BusicenUtils.invokeUserInfo(info, saveOrUpdate.Save,token);
				// 插入
				saveflag = baseMapper.insert(info) > 0 ? true : false;
			} else {
				// 更新条件
				QueryWrapper<T> updateWrapper = new QueryWrapper<>();
				updateWrapper.eq(keyFiledCode, keyFiledValue);// 根据主键更新
				// 并发控制
				if (checkUpdateId) {
					// 获取传入的并发字段值
					Object upTemp = Reflections.getFieldValue(info, "updateControlId");
					String updateControlId = ""; // info.getUpdateControlId();
					if (!StringHelper.IsEmptyOrNull(upTemp)) {
						updateControlId = upTemp.toString();
					} else {
						// 抛出RuntimeException，事务才会回滚
						throw BusicenException.create("缺少并发控制字段,请重试");
					}
					// 主键和并发字段
					updateWrapper.eq("UPDATE_CONTROL_ID", updateControlId);
				}

				// 填充公共字段
				BusicenUtils.invokeUserInfo(info, saveOrUpdate.Update,token);
				// 更新
				Reflections.setFieldValue(info, keyFiledMybatis, null);//不更新主键
				saveflag = baseMapper.update(info, updateWrapper) > 0 ? true : false;
				Reflections.setFieldValue(info, keyFiledMybatis, keyFiledValue);//把主键给回实体
			}
			if (!saveflag) {
				// 抛出RuntimeException，事务才会回滚
				throw BusicenException.create("并发操作,请重试");
			}
			// 操作成功
			return ResultHandler.updateOk();

		} catch (Exception e) {
			log.error("BusicenDbBaseUtils.baseSaveByIdOpt", e);
			// 抛出RuntimeException，事务才会回滚
			throw e;
		}

	}
	
	public static <T> ListResult<T> baseQueryTest(T info) {
		// pending 测试
		ListResult<T> returnResult = new ListResult<>();
		// T obj= baseMapper.selectById("");
		// Class<?> objnew = Reflections.getUserClass(obj);

		List<T> rows = new ArrayList<>();
		rows.add(info);
		returnResult.setMsg("成功");
		returnResult.setResult("1");
		returnResult.setRows(rows);
		return returnResult;
	}
}
