package com.ly.adp.drive.service.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.UUID;

import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.busicen.common.excel.ExcelExportUtil;
import com.ly.mp.component.entities.OptResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.adp.common.util.BusicenMpUploadResponEntity;
import com.ly.adp.common.util.BusicenMpUtil;
import com.ly.adp.drive.entities.SacTestDriveLongApply;
import com.ly.adp.drive.entities.in.SacTestDriveLongApplyIn;
import com.ly.adp.drive.idal.mapper.SacTestDriveLongApplyMapper;
import com.ly.adp.drive.otherservice.IBasedataFeignClient;
import com.ly.adp.drive.service.ISacTestDriveLongApplyService;
import com.ly.mp.busi.base.handler.ListResultBuilder;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.busicen.common.util.BusicenUtils.SOU;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 超长试驾申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-10
 */
@Service
public class SacTestDriveLongApplyService extends ServiceImpl<SacTestDriveLongApplyMapper, SacTestDriveLongApply>
        implements ISacTestDriveLongApplyService {

    private final Logger log = LoggerFactory.getLogger(SacTestDriveLongApplyService.class);

    @Autowired
    SacTestDriveLongApplyMapper sacTestDriveLongApplyMapper;

    @Value("${refer.url.mp.allinone}") // 文件上传服务地址
    String allInoneUrl;

    @Autowired
    IBasedataFeignClient baseDataFeignClient;

    /**
     * 超长试驾申请历史查询/出库审核列表查询/出库审核历史查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveLongApplyQueryFindAll(String token, SacTestDriveLongApplyIn dataInfo) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        try {
            Map<String, Object> paramMap = BusicenUtils.entityToMap(dataInfo);
            UserBusiEntity userBusiInfo = BusicenContext.getCurrentUserBusiInfo(token);
            String auditStatus = dataInfo.getAuditStatus();

            if (auditStatus == null && StringUtils.isEmpty(dataInfo.getAuditType())) {
                // 超长试驾申请历史查询，状态由参数过滤，审核通过/驳回/已取消
                paramMap.put("applyDlrCode", userBusiInfo.getDlrCode());
            }
            // auditStatus != null && auditStatus=1 		=> 出库审核列表查询
            // auditStatus != null && auditStatus=2、3 		=> 区域经理，出库审核历史查询
            else {
                // 查询主岗位和兼职岗位，判断审核类型：店长(查本店)，区域经理及其他(查管辖的门店)
                // Map<String, Object> map = new HashMap<>();
                // map.put("empId", userBusiInfo.getEmpID());
                // map.put("userId", userBusiInfo.getUserID());
                // ListResult<Map<String, Object>> stationInfoList = baseDataFeignClient.locationEmpInfo(map);
                // if (!"1".equals(stationInfoList.getResult())) {
                //		throw BusicenException.create("locationEmpInfo查询岗位信息失败，Feign调用BASE服务失败");
                // }
                // Map<String, Object> stationInfo = stationInfoList.getRows().get(0);
                // 岗位类型：门店管理员、店长、销售经理 ; 岗位编码：店长smart_bm_0016
                // 字段：主岗位STATION_ID/STATION_CODE/STATION_NAME, 兼职岗位：STATION_ID2/STATION_CODE2/STATION_NAME2
                // if ("smart_bm_0016".equals(stationInfo.get("stationId")) || "smart_bm_0016".equals(stationInfo.get("stationId2"))) {
                if ("1".equals(dataInfo.getAuditType())) {
                    // paramMap.put("auditType", "1"); //店长审核
                    // 取参数中的applyDlrCode,
                    paramMap.put("applyDlrCode", dataInfo.getApplyDlrCode());
                } else if ("2".equals(dataInfo.getAuditType())) {
                    // paramMap.put("auditType", "2"); //区域经理审核
                    Map<String, Object> map2 = new HashMap<>();
                    map2.put("userId", userBusiInfo.getUserID());
                    map2.put("pageIndex", -1);
                    map2.put("pageSize", -1);
                    StringJoiner dlrList = new StringJoiner(",");
                    ListResult<Map<String, Object>> managedDlrList = baseDataFeignClient.findManagedDlrList(map2);
                    if (!"1".equals(managedDlrList.getResult())) {
                        throw BusicenException.create("调用Base服务查询管辖门店异常");
                    }
                    for (Map<String, Object> managedDlrInfoMap : managedDlrList.getRows()) {
                        String dlrCode = (String) managedDlrInfoMap.get("dlrCode");
                        if (dlrCode != null) dlrList.add(dlrCode);
                    }
                    paramMap.put("applyDlrCodeList", dlrList.toString()); // 查区域经理管辖的门店
                }
            }

            IPage<Map<String, Object>> page = new Page<>(dataInfo.getPageIndex(), dataInfo.getPageSize());
            List<Map<String, Object>> list = SwitchDbInvoke.invokeTidb(() -> sacTestDriveLongApplyMapper.sacTestDriveLongApplyFindAll(page, paramMap));
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("SacTestDriveLongApplyService::sacTestDriveLongApplyQueryFindAll", e);
            throw e;
        }
        return result;
    }

    @Override
    public OptResult querylongapplyexport(SacTestDriveLongApplyIn dataInfo, String token, HttpServletResponse response) {
        try {
            //String excelFileName = (String) dataInfo.get("excelExportTitle");
            String title = "出库审核导出";
            String[][] columns = new String[][]{
                    {"carLicenceNo", "试乘试驾车牌"},
                    {"carTypeName", "试乘试驾车型"},
                    {"carColorName", "颜色"},
                    {"carIncolorName", "内饰"},
                    {"applyTimeBegin", "开始时间"},
                    {"applyTimeEnd", "结束时间"},
                    {"applyTimeLong", "申请时长"},
                    {"dlrShortName", "申请门店"},
                    {"createdName", "申请人"},
                    {"createdDate", "申请时间"},
                    {"applyReason", "申请原因"},
                    {"auditStatusName", "审核状态"},
                    {"auditDate", "审核时间"}
            };
            //  SacTestDriveLongApplyIn entiti = new SacTestDriveLongApplyIn();
            //  BeanUtils.copyProperties(dataInfo,entiti);
            //  SacTestDriveLongApplyIn entiti = BusicenUtils.map2Object(dataInfo, SacTestDriveLongApplyIn.class);
            List<Map<String, Object>> transferRecordsList = this.sacTestDriveLongApplyQueryFindAll(token, dataInfo).getRows();
            ExcelExportUtil.exportExcel(title, columns, transferRecordsList, response);
        } catch (Exception e) {
           // e.printStackTrace();
            log.error("querylongapplyexport；错误信息{" + e + "}");
            throw e;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EntityResult<Map<String, Object>> sacTestDriveLongApplySave(SacTestDriveLongApplyIn dataInfo, String token) {
        EntityResult<Map<String, Object>> result = new EntityResult<>();
        try {
            Map<String, Object> paramMap = BusicenUtils.entityToMap(dataInfo);
            UserBusiEntity userBusiInfo = BusicenContext.getCurrentUserBusiInfo(token);
            if (StringUtils.isEmpty((String) paramMap.get("applyId"))) {
                // 1.参数校验
                if (StringUtils.isBlank(dataInfo.getVin())) {
                    throw BusicenException.create("车辆VIN码不能为空!");
                }
                if (StringUtils.isBlank(dataInfo.getCarTypeCode())) {
                    throw BusicenException.create("车型编码CarTypeCode不能为空!");
                }
                if (StringUtils.isBlank(dataInfo.getCarTypeName())) {
                    throw BusicenException.create("车型名称CarTypeName不能为空!");
                }
                if (StringUtils.isBlank(dataInfo.getCarStatusCode())) {
                    throw BusicenException.create("试驾车状态carStatusCode不能为空!");
                }

                // 2.参数赋值
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime applyTimeEnd = LocalDateTime.parse(dataInfo.getApplyTimeEnd(), formatter);
                LocalDateTime applyTimeBegin = LocalDateTime.parse(dataInfo.getApplyTimeBegin(), formatter);
                if (applyTimeEnd.compareTo(applyTimeBegin) < 0) {
                    throw BusicenException.create("参数错误:申请结束时间应不小于申请开始时间!");
                }
                paramMap.put("auditStatus", "1");
                paramMap.put("auditStatusName", "审核中");
                paramMap.put("applyId", UUID.randomUUID().toString());
                paramMap.put("billId", paramMap.get("applyId"));
                paramMap.put("applyTimeLong", dataInfo.getApplyTimeLong());
                // 申请天数<=3天，店长审核；>3天，区域经理审核
                paramMap.put("auditType", applyTimeBegin.plusDays(3).compareTo(applyTimeEnd) > 0 ? "1" : "2");
                // 3.校验申请时间段内是否已有其他申请单存在
                List<Map<String, Object>> longApplyList = sacTestDriveLongApplyMapper.sacTestDriveLongApplyFindByApplyTime(paramMap);
                if (longApplyList.size() > 0) {
                    throw BusicenException.create("该时间段已存在申请单！请重新选择申请时间段");
                }
                // 4.校验预约申请表该时间段内是否存在预约, IS_ENABLE = 1
                List<Map<String, Object>> appointmentList = sacTestDriveLongApplyMapper.sacTestDriveAppointmentFindByTime(paramMap);
                if (appointmentList.size() > 0) {
                    throw BusicenException.create("车辆 " + dataInfo.getCarLicenceNo() + "该时间段内已被预约，不可申请!");
                }

                BusicenUtils.invokeUserInfo(paramMap, SOU.Save, token);
                paramMap.put("dlrCode", userBusiInfo.getDlrCode());
                int i = sacTestDriveLongApplyMapper.sacTestDriveLongApplySaveOne(paramMap);
                if (i > 0) {
                    result.setMsg("新增成功");
                    result.setRows(paramMap);
                } else {
                    result.setMsg("新增失败");
                }
            } else { // 修改
                for (Map<String, Object> applyMap : dataInfo.getApplyListMap()) {
                    if (StringUtils.isEmpty((String) applyMap.get("updateControlId"))) {
                        throw BusicenException.create("并发控制ID不能为空!");
                    }
                    if (StringUtils.isEmpty((String) applyMap.get("applyId"))) {
                        throw BusicenException.create("参数错误：applyId不能为空!");
                    }
                    // 校验超长出库申请单未审核通过时，该车在该申请单的申请时间段内是否又被试乘试驾申请
                    List<Map<String, Object>> longApplyList = sacTestDriveLongApplyMapper.sacTestDriveAppointmentFindByTime(applyMap);
                    if (longApplyList.size() > 0) {
                        throw BusicenException.create("当前时段已预约试驾，请驳回后重新申请");
                    }
                    String updateControlId = (String) applyMap.get("updateControlId");
                    BusicenUtils.invokeUserInfo(applyMap, SOU.Update, token);
                    applyMap.put("auditStatus", dataInfo.getAuditStatus());
                    applyMap.put("auditStatusName", dataInfo.getAuditStatusName());
                    applyMap.put("dlrCode", userBusiInfo.getDlrCode());
                    applyMap.put("updateControlId", updateControlId);
                    sacTestDriveLongApplyMapper.sacTestDriveLongApplyUpdateById(applyMap);
                }
            }
        } catch (Exception e) {
            log.error("SacTestDriveLongApplyService::sacTestDriveLongApplySave", e);
            throw e;
        }
        return result;
    }

    /**
     * 图片上传:单个文件
     *
     * @param uploadfiles
     * @param token
     * @return
     */
    @Override
    public ListResult<Map<String, Object>> uploadImage(MultipartFile uploadfiles, String token) {
        ListResult<Map<String, Object>> callResult = ListResultBuilder.create().result("0").build();
        Path path = Paths.get(String.format("%s.jpg", UUID.randomUUID().toString()));
        try {
            // 保存多个文件的上传结果
            List<Map<String, Object>> uploadResultList = new ArrayList<>();
            // 缓冲区
            File tmpFile = File.createTempFile(UUID.randomUUID().toString(), ".jpg.tmp");
            tmpFile.deleteOnExit();
            uploadfiles.transferTo(tmpFile);
            Files.copy(tmpFile.toPath(), path, StandardCopyOption.REPLACE_EXISTING);
            Map<String, Object> filePathMap = new HashMap<String, Object>();
            // 上传文件
            BusicenMpUploadResponEntity result = BusicenMpUtil.uploadFile(allInoneUrl, Files.readAllBytes(path),
                    String.format("%s.jpg", UUID.randomUUID().toString()), token);
            // 封装返回结果
            if ("1".equals(result.getResult()) && null != result.getPath() && !result.getPath().isEmpty()) {
                filePathMap.put("path", result.getPath().get(0).get(0));
                uploadResultList.add(filePathMap);
                callResult.setRows(uploadResultList);
                callResult.setResult("1");
                callResult.setMsg("上传文件成功");
            } else {
                callResult.setMsg("上传文件失败:" + result.getMsg());
            }
        } catch (IOException ioe) {
            log.error("上传文件失败, 原因:", ioe);
            throw BusicenException.create("上传文件失败:" + ioe.getMessage());
        } finally {
            try {
                Files.delete(path);
            } catch (IOException ignore) {
                // ignore
            }
        }
        return callResult;
    }
}
