package com.ly.adp.drive.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.util.UUID;

/**
 * 控制器增强-入参、结果日志记录
 */
@Aspect
@Component
public class ControllerAdvice {

    private static final Logger log = LoggerFactory.getLogger(ControllerAdvice.class);

    public ControllerAdvice() {
        log.info("ControllerAdvice initialized");
    }

    @Around("execution(* com..controller..*.*(..))")
    public Object aroundInController(ProceedingJoinPoint joinPoint) throws Throwable {
        // 生成唯一标识符
        String requestId = UUID.randomUUID().toString();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();
        String fullMethodName = className + "#" + methodName;
        Object[] args = joinPoint.getArgs();

        Object[] arguments = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof ServletRequest || args[i] instanceof ServletResponse || args[i] instanceof MultipartFile) {
                // ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                // ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                continue;
            }
            arguments[i] = args[i];
        }
        String argsJson = "";
        if (arguments != null) {
            try {
                argsJson = JSONObject.toJSONString(arguments);
            } catch (Exception e) {
                argsJson = arguments.toString();
            }
        }
        log.info("Controller 请求 - ID:{} 调用方法:{} -开始执行 => 参数:{}", requestId, fullMethodName, argsJson);
        try {
            Object result = joinPoint.proceed();
            log.info("Controller 响应 - ID:{} -执行成功 => 结果:{}", requestId, JSON.toJSONString(result));
            return result;
        } catch (Throwable e) {
            log.info("Controller 异常 - ID:{} -执行失败 => 原因:{}", requestId, e.getMessage(), e);
            throw e;
        }
    }


}
