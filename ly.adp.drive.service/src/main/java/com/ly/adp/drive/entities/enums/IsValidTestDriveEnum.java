package com.ly.adp.drive.entities.enums;

public enum IsValidTestDriveEnum {
    CALCULATING(2, "计算中"),
    VALID(1, "有效试驾"),
    INVALID(0, "无效试驾");

    private final int code;
    private final String description;

    IsValidTestDriveEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }


    public static IsValidTestDriveEnum fromCode(int code) {
        switch (code) {
            case 0:
                return INVALID;
            case 1:
                return VALID;
            case 2:
            default:
                return CALCULATING;
        }
    }
}
