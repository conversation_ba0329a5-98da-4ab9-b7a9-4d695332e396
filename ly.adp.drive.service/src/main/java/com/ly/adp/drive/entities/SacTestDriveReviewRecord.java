package com.ly.adp.drive.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 试乘试驾跟进记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@TableName("t_sac_test_drive_review_record")
public class SacTestDriveReviewRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录ID
     */
    @TableId("RECORD_ID")
    private String recordId;

    /**
     * 试乘试驾单ID
     */
    @TableField("TEST_DRIVE_SHEET_ID")
    private String testDriveSheetId;

    /**
     * 试乘试驾单号
     */
    @TableField("TEST_DRIVE_ORDER_NO")
    private String testDriveOrderNo;

    /**
     * 试乘试驾单跟进内容
     */
    @TableField("TEST_DRIVE_DESC")
    private String testDriveDesc;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getTestDriveSheetId() {
        return testDriveSheetId;
    }

    public void setTestDriveSheetId(String testDriveSheetId) {
        this.testDriveSheetId = testDriveSheetId;
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public String getTestDriveDesc() {
        return testDriveDesc;
    }

    public void setTestDriveDesc(String testDriveDesc) {
        this.testDriveDesc = testDriveDesc;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }

    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SlTSacTestDriveReviewRecord{" +
        "recordId=" + recordId +
        ", testDriveSheetId=" + testDriveSheetId +
        ", testDriveOrderNo=" + testDriveOrderNo +
        ", testDriveDesc=" + testDriveDesc +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", column6=" + column6 +
        ", column7=" + column7 +
        ", column8=" + column8 +
        ", column9=" + column9 +
        ", column10=" + column10 +
        ", mycatOpTime=" + mycatOpTime +
        ", oemId=" + oemId +
        ", groupId=" + groupId +
        ", oemCode=" + oemCode +
        ", groupCode=" + groupCode +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", sdpUserId=" + sdpUserId +
        ", sdpOrgId=" + sdpOrgId +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
