package com.ly.adp.drive.otherservice;

import com.ly.adp.drive.otherservice.entities.BuTestcarPrepare;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/9/5
 */
public interface ICscSysBaseService {

    /**
     * 试驾车保存
     * @param authentication
     * @param param
     * @return
     */
    EntityResult<Map<String, Object>> inset(String authentication, ParamPage<BuTestcarPrepare> param);
}
