package com.ly.adp.drive.otherservice;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.drive.otherservice.entities.MdsLookupValue;
import com.ly.adp.drive.otherservice.entities.in.MdsLookupValueIn;
import com.ly.adp.drive.otherservice.util.ListResultUtil;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import ly.adp.drive.otherservice.ICscSysBaseDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class CscSysBaseDataService implements ICscSysBaseDataService {

    private static final Logger LOG = LoggerFactory.getLogger(CscSysBaseDataService.class);
    @Autowired
    IBasedataFeignClient basedataFeignClient;

    @Override
    public ListResult<Map<String, Object>> generateOrderCode(String dlrId, String billTypeId, String token) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        try {
            OptResult optResult = basedataFeignClient.generateOrderCodeTest(token, dlrId, billTypeId);
            result.setMsg(optResult.getMsg());
            result.setResult(optResult.getResult());
            LOG.info("生成订单号返回结果 {}", JSONObject.toJSONString(optResult));
        } catch (Exception e) {
            LOG.error("CscSysBaseDataService::generateOrderCode", e);
            throw BusicenException.create(e.getMessage());
        }
        return result;
    }

    @Override
    public ListResult<Map<String, Object>> generateOrderCodeNoToken(String dlrId, String billTypeId) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("dlrId", dlrId);
            map.put("billTypeId", billTypeId);
            OptResult optResult = basedataFeignClient.generateOrderCodeForFeign("", map);
            result.setMsg(optResult.getMsg());
            result.setResult(optResult.getResult());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public ListResult<Map<String, Object>> mdslookupvaluefindbypage(String lookupTypeCode, String token) {
        MdsLookupValueIn mdsLookupValue = new MdsLookupValueIn();
        mdsLookupValue.setLookupTypeCode(lookupTypeCode);
        ListResult<MdsLookupValue> listResult = basedataFeignClient.mdsLookupValueFindByPage(token, mdsLookupValue);
        return ListResultUtil.ListResultOfMap(listResult);
    }

//    @Override
//    public EntityResult<Map<String, Object>> inset(String authentication, ParamPage<BuTestcarPrepare> param) {
//        //转换参数类型
//        ParamPage<BuTestcarPrepare> newParam = new ParamPage<>();
//        newParam.setPageIndex(param.getPageIndex());
//        newParam.setPageSize(param.getPageSize());
//        BuTestcarPrepare prepare = new BuTestcarPrepare();
//        BeanUtils.copyProperties(param.getParam(), prepare);
//        //BuTestcarPrepare buTestcarPrepare=BusicenUtils.map2Object(param.getParam(), BuTestcarPrepare.class);
//        newParam.setParam(prepare);
//        EntityResult<BuTestcarPrepare> result = basedataFeignClient.inset(authentication, newParam);
//        //转换结果类型
//        EntityResult<Map<String, Object>> newResult = new EntityResult<>();
//        newResult.setMsg(result.getMsg());
//        newResult.setExtInfo(result.getExtInfo());
//        newResult.setResult(result.getResult());
//        newResult.setRows(BusicenUtils.entityToMap(result.getRows()));
//        return newResult;
//    }


}
