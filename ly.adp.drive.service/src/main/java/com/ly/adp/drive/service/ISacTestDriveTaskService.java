package com.ly.adp.drive.service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.adp.drive.entities.SacTestDriveTask;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 试驾任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
public interface ISacTestDriveTaskService extends IService<SacTestDriveTask> {
	/**
	 * 分页查询
	 * @param pageInfo
	 * @param info
	 * @return
	 */
	ListResult<Map<String, Object>> sacTestDriveTaskFindInfo(Map<String, Object> dataInfo,String token);

	EntityResult<Map<String, Object>> sacTestDriveTaskFindCount(Map<String, Object> dataInfo,String token);
	
	/**
	 * 根据主键判断插入或更新
	 * @param info
	 * @return
	 */
	EntityResult<Map<String, Object>> sacTestDriveTaskSaveInfo(Map<String, Object> dataInfo, String token);

	ListResult<Map<String, Object>> sacTestDriveTaskFindAllInfo(Map<String, Object> dateInfo, String authentication);

	ListResult<Map<String, Object>> sacTestDriveTaskFindListInfo(Map<String, Object> dateInfo, String authentication);

	OptResult sacTestDriveTaskExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);

	OptResult sacTestDriveTaskAllExport(Map<String, Object> dataInfo, String token, HttpServletResponse response);
}
