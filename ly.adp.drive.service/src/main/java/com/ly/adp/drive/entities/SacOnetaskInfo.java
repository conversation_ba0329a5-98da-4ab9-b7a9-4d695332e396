package com.ly.adp.drive.entities;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-14
 */
@TableName("t_sac_onetask_info")
public class SacOnetaskInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId("TASK_ID")
    private String taskId;

    /**
     * 任务标题
     */
    @TableField("TASK_TITLE")
    private String taskTitle;

    /**
     * 任务描述
     */
    @TableField("TASK_DESCRIBE")
    private String taskDescribe;

    /**
     * 任务类型编码 值列表:ADP_CLUE_035
     */
    @TableField("TASK_TYPE_CODE")
    private String taskTypeCode;

    /**
     * 任务类型名称 值列表:ADP_CLUE_035
     */
    @TableField("TASK_TYPE_NAME")
    private String taskTypeName;

    /**
     * 任务状态编码 值列表:ADP_CLUE_036
     */
    @TableField("TASK_STATE_CODE")
    private String taskStateCode;

    /**
     * 任务状态名称 值列表:ADP_CLUE_036
     */
    @TableField("TASK_STATE_NAME")
    private String taskStateName;

    /**
     * 任务是否认证
     */
    @TableField("TASK_ATTESTATION_IS")
    private String taskAttestationIs;

    /**
     * 任务是否重复
     */
    @TableField("TASK_REPEAT_IS")
    private String taskRepeatIs;

    /**
     * 作业时间
     */
    @TableField("BUSS_TIME")
    private LocalDateTime bussTime;

    /**
     * 作业开始时间
     */
    @TableField("BUSS_START_TIME")
    private LocalDateTime bussStartTime;

    /**
     * 作业结束时间
     */
    @TableField("BUSS_END_TIME")
    private LocalDateTime bussEndTime;

    /**
     * 扩展信息
     */
    @TableField("EXTEND_JSON")
    private String extendJson;

    /**
     * 扩展字段1
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    public String getTaskTitle() {
        return taskTitle;
    }

    public void setTaskTitle(String taskTitle) {
        this.taskTitle = taskTitle;
    }
    public String getTaskDescribe() {
        return taskDescribe;
    }

    public void setTaskDescribe(String taskDescribe) {
        this.taskDescribe = taskDescribe;
    }
    public String getTaskTypeCode() {
        return taskTypeCode;
    }

    public void setTaskTypeCode(String taskTypeCode) {
        this.taskTypeCode = taskTypeCode;
    }
    public String getTaskTypeName() {
        return taskTypeName;
    }

    public void setTaskTypeName(String taskTypeName) {
        this.taskTypeName = taskTypeName;
    }
    public String getTaskStateCode() {
        return taskStateCode;
    }

    public void setTaskStateCode(String taskStateCode) {
        this.taskStateCode = taskStateCode;
    }
    public String getTaskStateName() {
        return taskStateName;
    }

    public void setTaskStateName(String taskStateName) {
        this.taskStateName = taskStateName;
    }
    public String getTaskAttestationIs() {
        return taskAttestationIs;
    }

    public void setTaskAttestationIs(String taskAttestationIs) {
        this.taskAttestationIs = taskAttestationIs;
    }
    public String getTaskRepeatIs() {
        return taskRepeatIs;
    }

    public void setTaskRepeatIs(String taskRepeatIs) {
        this.taskRepeatIs = taskRepeatIs;
    }
    public LocalDateTime getBussTime() {
        return bussTime;
    }

    public void setBussTime(LocalDateTime bussTime) {
        this.bussTime = bussTime;
    }
    public LocalDateTime getBussStartTime() {
        return bussStartTime;
    }

    public void setBussStartTime(LocalDateTime bussStartTime) {
        this.bussStartTime = bussStartTime;
    }
    public LocalDateTime getBussEndTime() {
        return bussEndTime;
    }

    public void setBussEndTime(LocalDateTime bussEndTime) {
        this.bussEndTime = bussEndTime;
    }
    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SacOnetaskInfo{" +
        "taskId=" + taskId +
        ", taskTitle=" + taskTitle +
        ", taskDescribe=" + taskDescribe +
        ", taskTypeCode=" + taskTypeCode +
        ", taskTypeName=" + taskTypeName +
        ", taskStateCode=" + taskStateCode +
        ", taskStateName=" + taskStateName +
        ", taskAttestationIs=" + taskAttestationIs +
        ", taskRepeatIs=" + taskRepeatIs +
        ", bussTime=" + bussTime +
        ", bussStartTime=" + bussStartTime +
        ", bussEndTime=" + bussEndTime +
        ", extendJson=" + extendJson +
        ", column1=" + column1 +
        ", column2=" + column2 +
        ", column3=" + column3 +
        ", column4=" + column4 +
        ", column5=" + column5 +
        ", mycatOpTime=" + mycatOpTime +
        ", oemId=" + oemId +
        ", groupId=" + groupId +
        ", oemCode=" + oemCode +
        ", groupCode=" + groupCode +
        ", creator=" + creator +
        ", createdName=" + createdName +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", modifyName=" + modifyName +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", sdpUserId=" + sdpUserId +
        ", sdpOrgId=" + sdpOrgId +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
