package com.ly.adp.drive.idal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.drive.entities.LookupValue;
import com.ly.adp.drive.entities.SacAppointmentSheet;
import com.ly.adp.drive.entities.req.TestDriveCheckInReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 试乘试驾预约单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface SacAppointmentSheetMapper extends BaseMapper<SacAppointmentSheet> {

    /**
     * 试乘试驾预约单新增
     *
     * @param param
     * @return
     */
    public int insertSacAppointmentSheet(@Param("param") Map<String, Object> param);

    /**
     * 试乘试驾预约单批量新增
     *
     * @param list
     * @return
     */
    public int insertSacAppointmentSheetList(@Param("list") List<Map<String, Object>> list);

    /**
     * 试乘试驾预约单更新
     *
     * @param param
     * @return
     */
    public int updateSacAppointmentSheet(@Param("param") Map<String, Object> param);

    /**
     * 补充更新试乘试驾预约单更新
     * @param param
     * @return
     */
    public int updateSacAppointmentSheetSupplement(@Param("param") Map<String, Object> param);

    /**
     * 查重
     *
     * @param param
     * @return
     */
    public int checkRepeat(@Param("param") Map<String, Object> param);

    /**
     * 超长出库查询
     *
     * @param param
     * @return
     */
    public int checkLongRepeat(@Param("param") Map<String, Object> param);


    /**
     * 手机号查重
     *
     * @param param
     * @return
     */
    public int checkPhoneRepeat(@Param("param") Map<String, Object> param);

    /**
     * 试乘试驾预约单查询
     *
     * @param param
     * @param page
     * @return
     */
    public List<Map<String, Object>> selectSacAppointmentSheet(@Param("param") Map<String, Object> param, Page<Map<String, Object>> page);

    /**
     * 试驾车容量查询
     *
     * @param param
     * @param page
     * @return
     */
    public List<Map<String, Object>> selectCarCapacity(@Param("param") Map<String, Object> param, Page<Map<String, Object>> page);

    /**
     * 查询预约时间段有哪些
     *
     * @param param
     * @return
     */
    public List<Map<String, Object>> selectCarCapacityTimeRange(@Param("param") Map<String, Object> param);

    List<Map<String, Object>> selectSacAppointmentSheetById(@Param("param") Map<String, Object> map);

    void deleteSacAppointmentSheet(@Param("param") Map<String, Object> param);

    Map<String, Object> findDriveTask(Map<String, Object> mapParam);

    int updateDriveTask(Map<String, Object> mapParam);

    List<Map<String, Object>> querCarList(@Param("param") Map<String, Object> param);

    List<Map<String, Object>> querDriveLong(@Param("param") Map<String, Object> param);

    List<Map<String, Object>> querTestDriver(@Param("param") Map<String, Object> param);

    List<Map<String, Object>> querTestDriverNew(@Param("param") Map<String, Object> param);

    int updateTestDriveSheet(@Param("param") TestDriveCheckInReq param, @Param("empCode") String empCode, @Param("empName") String empName);

    Boolean checkIsHaveColumn1(@Param("param") Map<String, Object> mapParam);

    List<LookupValue> findCdpSys();

    Map<String, String> findThirdLeads(@Param("lookupTypeCode") String lookupTypeCode, @Param("thirdChannel") String c_third_channel);
}
