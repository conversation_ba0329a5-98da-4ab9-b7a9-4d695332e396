package com.ly.adp.drive.entities.in;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/16
 */
public class ProductSpecialistDTO implements Serializable {

    private static final long serialVersionUID = 5719259148473718513L;

    @ApiModelProperty("门店编码")
    private String dlrCode;

    @ApiModelProperty("岗位编码")
    private String stationId;

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    @Override
    public String toString() {
        return "ProductSpecialistDTO{" +
                "dlrCode='" + dlrCode + '\'' +
                ", stationId='" + stationId + '\'' +
                '}';
    }
}
