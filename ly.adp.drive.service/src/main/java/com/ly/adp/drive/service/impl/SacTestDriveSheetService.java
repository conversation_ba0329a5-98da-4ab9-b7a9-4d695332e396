package com.ly.adp.drive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ly.adp.common.configuration.DwsFeign;
import com.ly.adp.common.entity.ParamQry;
import com.ly.adp.common.entity.RedisLockPrefixConstants;
import com.ly.adp.common.entity.enums.YesOrNoEnum;
import com.ly.adp.common.util.ImportEntiei;
import com.ly.adp.drive.config.MybatisBatchExecutor;
import com.ly.adp.drive.entities.SacAppointmentSheet;
import com.ly.adp.drive.entities.SacTestDriveSheet;
import com.ly.adp.drive.entities.TSwitch;
import com.ly.adp.drive.entities.TestDriveVehicleData;
import com.ly.adp.drive.entities.constants.CommonConstant;
import com.ly.adp.drive.entities.enums.TestStatusEnum;
import com.ly.adp.drive.entities.in.ProductSpecialistDTO;
import com.ly.adp.drive.entities.in.TestDriveTransferDTO;
import com.ly.adp.drive.entities.out.ProductSpecialistVO;
import com.ly.adp.drive.entities.req.GetBiVehicleDataJobReq;
import com.ly.adp.drive.entities.vo.GetBiVehicleDataResultVO;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.adp.drive.idal.mapper.SacAppointmentSheetMapper;
import com.ly.adp.drive.idal.mapper.SacTestDriveSheetMapper;
import com.ly.adp.drive.idal.mapper.SacTestDriveTaskMapper;
import com.ly.adp.drive.otherservice.AdpMsUtil;
import com.ly.adp.drive.otherservice.ICscSysBaseService;
import com.ly.adp.drive.otherservice.IXapiPushFeignService;
import com.ly.adp.drive.otherservice.entities.BuTestcarPrepare;
import com.ly.adp.drive.service.ISacAppointmentSheetService;
import com.ly.adp.drive.service.ISacTestDriveSheetService;
import com.ly.adp.drive.service.ITSwitchService;
import com.ly.adp.drive.service.IVehicleDataService;
import com.ly.adp.drive.util.AESUtil;
import com.ly.adp.drive.util.CommonUtil;
import com.ly.adp.drive.util.DateTimeConverter;
import com.ly.bucn.component.interceptor.InterceptorWrapperRegist;
import com.ly.bucn.component.interceptor.InterceptorWrapperRegistor;
import com.ly.bucn.component.interceptor.annotation.Interceptor;
import com.ly.bucn.component.message.Message;
import com.ly.mp.bucn.pack.StringUtil;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.busi.base.constant.UserBusiEntity;
import com.ly.mp.busi.base.context.BusicenContext;
import com.ly.mp.busi.base.handler.*;
import com.ly.mp.busi.base.handler.BusicenUtils.SOU;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.excel.ExcelExportUtil;
import com.ly.mp.busicen.common.excel.ExcelImportUtil;
import com.ly.mp.busicen.common.helper.SpringContextHolder;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.busicen.rule.field.IFireFieldRule;
import com.ly.mp.busicen.rule.field.execution.ValidResultCtn;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;
import com.ly.mp.component.identityUtils.UUIDUtils;
import ly.adp.drive.otherservice.ICscSysBaseDataService;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 试乘试驾单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Service
public class SacTestDriveSheetService extends ServiceImpl<SacTestDriveSheetMapper, SacTestDriveSheet>
        implements ISacTestDriveSheetService, InterceptorWrapperRegist {

    private Logger logger = LoggerFactory.getLogger(SacTestDriveSheetService.class);

    private static final String HEADER_TOKEN = "Authorization";

    @Autowired
    SacTestDriveSheetMapper sacTestDriveSheetMapper;
    @Autowired
    SacAppointmentSheetMapper sacAppointmentSheetMapper;
    @Autowired
    ISacAppointmentSheetService sacAppointmentSheetService;
    @Autowired
    SacTestDriveTaskMapper sacTestDriveTaskMapper;
    @Autowired
    Message message;
    @Autowired
    ICscSysBaseDataService baseDataService;
    @Autowired
    ICscSysBaseService baseService;
    @Autowired
    IFireFieldRule fireFieldRule;
    @Autowired
    Executor asyncTaskExecutor;
    @Autowired
    IXapiPushFeignService accPushFeignService;
    @Autowired
    MybatisBatchExecutor mybatisBatchExecutor;
    @Autowired
    SactdaZtService SactdaZtService;
    @Autowired
    private IXapiPushFeignService xapiPushFeignService;
    @Autowired
    private IVehicleDataService vehicleDataService;
    @Resource
    private RedisLockUtil redisLockUtil;
    @Autowired
    private ITSwitchService itSwitchService;
    @Resource
    private DwsFeign dwsFeign;
    @Autowired
    private AESUtil aesUtil;
    /**
     * 试乘试驾单查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveSheetQueryList(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        try {
            // 去掉空字符串
            MapUtil.removeNullValue(mapParam.getParam());
            // 字段检验
            ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-test-driver-sheet-check1", "maindata");
            String resMsg = fireRule.getNotValidMessage();
            if (!fireRule.isValid()) {
                throw new BusicenException(resMsg);
            }
            //默认查询可用的
            if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("isEnable"))) {
                mapParam.getParam().put("isEnable", "1");
            }
            String token = mapParam.getParam().get("token").toString();
            // 根据token获取当前用户信息
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            //查看已完成的试驾单时，需要关联线索表,指定销售顾问：查找这个销售顾问负责的线索的所有已完成的试驾单，不指定销售顾问则查本店销售顾问所有负责的已完成的试驾单（不区分门店）
            if ("1".equals(mapParam.getParam().get("isEnd"))) {
                //指定销售顾问
                if (!StringHelper.IsEmptyOrNull(mapParam.getParam().get("salesConsultantId"))) {
                    mapParam.getParam().put("reviewPersonId", mapParam.getParam().get("salesConsultantId"));
                } else {
                    //店长--查专营店
                    mapParam.getParam().put("clueDlrCode", userBusiEntity.getDlrCode());
                }
                mapParam.getParam().remove("salesConsultantId");
            } else {
                //其他的正常查询
                mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
            }
            Page<Map<String, Object>> page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize());
            // 改了
            if (Objects.equals(mapParam.getPageIndex(), 1)) {
                mapParam.getParam().put("pageNo", mapParam.getPageIndex() - 1);
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            } else {
                mapParam.getParam().put("pageNo", (mapParam.getPageIndex() - 1) * mapParam.getPageSize());
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            }
            Long counts = sacTestDriveSheetMapper.selectSacTestDriveSheetCount(mapParam.getParam());
            if (counts == 0) {
                page.setRecords(new ArrayList<>());
                page.setTotal(counts);
                result = BusicenUtils.page2ListResult(page);
                return result;
            }
            List<Map<String, Object>> list = sacTestDriveSheetMapper.selectSacTestDriveSheet(mapParam.getParam());
            if(CollectionUtil.isNotEmpty(list)){
                list.stream().forEach(item -> {
                    try {
                        if (item.containsKey("idNumber") && !StringUtils.isEmpty(item.get("idNumber"))) {
                            String decrypted = aesUtil.decrypt((String) item.get("idNumber"));
                            item.put("idNumber", decrypted);
                        }
                    } catch (Exception e) {
                        log.error(String.format("身份证号解密失败记录ID：%s", item.get("testDriveSheetId")), e);
                    }
                });
            }
            page.setRecords(list);
            page.setTotal(counts);
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("sacTestDriveSheetQueryList异常", e);
            throw e;
        }
        return result;
    }

    /**
     * 试乘试驾单查询，性能优化
     * @param mapParam
     * @return
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveSheetQueryList_performance(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        try {
            TSwitch tSwitchResult = itSwitchService.querySwitch(new TSwitch("switch-sacTestDriveSheetQueryList_performance"));
            logger.info("switchResult departClueTransfer_performance {}", JSONObject.toJSONString(tSwitchResult));
            if(Objects.nonNull(tSwitchResult) && !tSwitchResult.oldOrPerformance()) {
                logger.info("原接口");
                return sacTestDriveSheetQueryList(mapParam);
            }
            logger.info("优化后的接口");
            // 去掉空字符串
            MapUtil.removeNullValue(mapParam.getParam());
            // 字段检验
            ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-test-driver-sheet-check1", "maindata");
            String resMsg = fireRule.getNotValidMessage();
            if (!fireRule.isValid()) {
                throw new BusicenException(resMsg);
            }
            //默认查询可用的
            if (StringHelper.IsEmptyOrNull(mapParam.getParam().get("isEnable"))) {
                mapParam.getParam().put("isEnable", "1");
            }
            String token = mapParam.getParam().get("token").toString();
            // 根据token获取当前用户信息
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            //查看已完成的试驾单时，需要关联线索表,指定销售顾问：查找这个销售顾问负责的线索的所有已完成的试驾单，不指定销售顾问则查本店销售顾问所有负责的已完成的试驾单（不区分门店）
            if ("1".equals(mapParam.getParam().get("isEnd"))) {
                //指定销售顾问
                if (!StringHelper.IsEmptyOrNull(mapParam.getParam().get("salesConsultantId"))) {
                    mapParam.getParam().put("reviewPersonId", mapParam.getParam().get("salesConsultantId"));
                } else {
                    //店长--查专营店
                    mapParam.getParam().put("clueDlrCode", userBusiEntity.getDlrCode());
                }
                mapParam.getParam().remove("salesConsultantId");
            } else {
                //其他的正常查询
                mapParam.getParam().put("dlrCode", userBusiEntity.getDlrCode());
            }
            Page<Map<String, Object>> page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize());
            // 改了
            if (Objects.equals(mapParam.getPageIndex(), 1)) {
                mapParam.getParam().put("pageNo", mapParam.getPageIndex() - 1);
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            } else {
                mapParam.getParam().put("pageNo", (mapParam.getPageIndex() - 1) * mapParam.getPageSize());
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            }
            mapParam.getParam().put("pageIndex", page.getCurrent());
            mapParam.getParam().put("pageOffset", page.getSize());
            mapParam.getParam().put("pageSize", page.getSize());
            Page<Map<String, Object>> dwsResult = dwsFeign.selectTestDriveSheet(ParamQry.buildDwsQry(mapParam.getParam()));
            page.setRecords(dwsResult.getRecords());
            page.setTotal(dwsResult.getTotal());
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("sacTestDriveSheetQueryList异常", e);
            throw e;
        }
        return result;
    }

    /**
     * 个人试乘试驾单查询
     */
    @Override
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleList(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        logger.info("个人试乘试驾单查询入参：{}",JSON.toJSONString(mapParam));
        // 去掉空字符串
        MapUtil.removeNullValue(mapParam.getParam());
        logger.info("sacTestDriveSheetSingleList去除空值入参：{}", JSON.toJSONString(mapParam));
        // 校验入参
        validateParams(mapParam);
        try {
            //ifNullInitParam(mapParam);
            Page<Map<String, Object>> page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize());
            // 改了
            if (Objects.equals(mapParam.getPageIndex(), 1)) {
                mapParam.getParam().put("pageNo", mapParam.getPageIndex() - 1);
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            } else {
                mapParam.getParam().put("pageNo", (mapParam.getPageIndex() - 1) * mapParam.getPageSize());
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
            }
//            List<Map<String, Object>> list = sacTestDriveSheetMapper.selectSacTestDriveSheetSingle(mapParam.getParam(), page);
            List<Map<String, Object>> list = new ArrayList<>();
            Long counts = sacTestDriveSheetMapper.selectSacTestDriveSheetSingleCount(mapParam.getParam());
            if(counts != 0) {
                list = sacTestDriveSheetMapper.selectSacTestDriveSheetSingleNoPage(mapParam.getParam());
            }
            if (counts < 1 && "external".equals(mapParam.getParam().get("systemSource")) && !StringHelper.IsEmptyOrNull(mapParam.getParam().get("customerPhone"))) {
                mapParam.getParam().put("phone", mapParam.getParam().get("customerPhone"));
                mapParam.getParam().put("pageIndex", mapParam.getPageIndex());
                mapParam.getParam().put("pageSize", mapParam.getPageSize());
                list = sacTestDriveTaskMapper.querySacTestDriveTask(page, mapParam.getParam());
            }
            for (Map<String, Object> map : list) {
                Object idNumber = map.get("customerIdNumber");
                if (Objects.isNull(idNumber) || StringUtils.isEmpty(idNumber.toString())) {
                    continue;
                }
                try {
                    String decrypted = aesUtil.decrypt(String.valueOf(map.get("customerIdNumber")));
//                    String idCard = decrypted.substring(0, decrypted.length() - 4) + "****";
                    char[] chars = decrypted.toCharArray();
                    Arrays.fill(chars, chars.length - 4, chars.length, '*');
                    String idCard = String.valueOf(chars);
                    map.put("customerIdNumber", decrypted);
                    map.put("customerIdCard", idCard);
                } catch (Exception e) {
                    logger.error("身份证号解密失败: {}", e.getMessage());
                }
            }
            page.setTotal(counts);
            page.setRecords(handleTestDriveSheetSingleList(list));
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("sacTestDriveSheetSingleList异常", e);
            throw e;
        }
        return result;
    }

    private VehicleDataVO buildAndConvertVehicleDataVO(Map<String, Object> data) {
        // 构建TestDriveVehicleData对象
        TestDriveVehicleData vehicleData = new TestDriveVehicleData();
        // 设置基础字段
        vehicleData.setTestDriveOrderNo((String) data.get("testDriveOrderNo"));
        vehicleData.setIsEnable(CommonUtil.convertToInt(data.get("biDriveDataIsEnable")));
        vehicleData.setQueryBiTimes(CommonUtil.convertToInt(data.get("queryBiTimes")));
        vehicleData.setIsValidTestDrive(CommonUtil.convertToInt(data.get("isValidTestDrive")));

        // 设置车机相关数据
        vehicleData.setVehStartVoc((String) data.get("vehStartVoc"));
        vehicleData.setVehStartMileage((String) data.get("vehStartMileage"));
        vehicleData.setVehEndMileage((String) data.get("vehEndMileage"));
        vehicleData.setVehTotalMileage((String) data.get("vehTotalMileage"));
        vehicleData.setVehTotalDur((String) data.get("vehTotalDur"));
        vehicleData.setVehMaxSpeed((String) data.get("vehMaxSpeed"));
        vehicleData.setTestDriveAvgSpeed((String) data.get("testDriveAvgSpeed"));

        vehicleData.setIsGoodTestDrive(Optional.ofNullable(data.get("isGoodTestDrive")).map(CommonUtil::convertToInt).orElse(null));
        vehicleData.setIsMatchRecord(Optional.ofNullable(data.get("isMatchRecord")).map(CommonUtil::convertToInt).orElse(null));
        vehicleData.setRecordTime(DateTimeConverter.convert(data.get("recordTime")));
        vehicleData.setRecordDuration(CommonUtil.convertToBigDecimal(data.get("recordDuration")));
        vehicleData.setRecordScore(CommonUtil.convertToBigDecimal(data.get("recordScore")));

        // 复用原有的转换逻辑
        return vehicleDataService.convertToVehicleDataVO(vehicleData);
    }

    private List<Map<String, Object>> handleTestDriveSheetSingleList(List<Map<String, Object>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        // 获取试驾单号列表
        List<String> testDriveOrderNoList = list.stream()
                .map(d -> (String) d.get("testDriveOrderNo"))
                .collect(Collectors.toList());

        // 查询车机数据
        // Map<String, VehicleDataVO> vehicleDataVOMap = vehicleDataService.queryVehicleDataMap(testDriveOrderNoList);

        // 构建车机数据Map
        Map<String, VehicleDataVO> vehicleDataVOMap = list.stream()
                // 过滤还没有获取车机数据的试驾单
                .filter(item -> Objects.nonNull(item.get("isValidTestDrive")))
                .collect(Collectors.toMap(
                        item -> (String) item.get("testDriveOrderNo"),
                        this::buildAndConvertVehicleDataVO,
                        (existing, replacement) -> existing
                ));

        // 处理列表
        return list.stream()
                .map(d -> {
                    String testDriveOrderNo = (String) d.get("testDriveOrderNo");
                    VehicleDataVO vehicleDataVO = vehicleDataVOMap.getOrDefault(testDriveOrderNo, VehicleDataVO.getDefaultInstance(testDriveOrderNo));
                    d.putAll(vehicleDataVO.toMap());
                    return d;
                })
                .collect(Collectors.toList());
    }

    @Override
    public OptResult sacTestDriveSheetSingleListExport(Map<String, Object> dataInfo, String token, HttpServletResponse response) {
        try {
            String title = "个人试乘试驾单列表查询导出";
            String[][] columns = new String[][]{
                    {"customerName", "客户姓名"},
                    {"realName", "真实姓名"},
                    {"customerIdCard", "身份证号（驾照号）"},
                    {"testDriveOrderNo", "试驾单号"},
                    {"customerPhoneTm", "客户电话"},
                    {"customerSexName", "性别"},
                    {"clueDlrName", "线索所属门店"},
                    {"salesConsultantName", "线索所属产品专家"},
                    {"cityName", "城市"},
                    {"dlrName", "试乘试驾门店"},
                    {"salesConsultantName", "试驾执行产品专家"},
                    {"testTypeName", "类型"},
                    {"testDriveMethodName", "试驾方式"},
                    {"smallCarTypeName", "试乘试驾车型"},
                    {"plateNumber", "试乘试驾车牌"},
                    {"testDriveTimePJ", "试乘试驾预约时间"},
//                    {"testRoadHaul", "试驾里程(KM)"},
                    {"testStatusName", "试驾状态"},
                    {"routeTypeName", "试驾路线"},
//                    {"testcarTime", "试驾时间"},
//                    {"testcarDistance", "试驾距离"},
                    {"testcarDescription", "试驾描述信息"},
                    {"startTime", "试驾开始时间"},
                    {"endTime", "试乘结束时间"},
                    {"routeTypeName", "试驾路线名称"},
                    {"inviteCode", "试驾邀请码"},
                    {"isValidTestDriveCn", "是否有效试驾(车机)"},
                    {"isGoodTestDriveCn", "是否优秀试驾"},
                    {"vehStartVoc", "开始试驾电量（车机）"},
                    {"vehStartMileage", "试驾开始里程 (车机)"},
                    {"vehEndMileage", "试驾结束里程 (车机)"},
                    {"vehTotalMileage", "试驾行驶里程(车机)"},
                    {"vehTotalDur", "试驾行驶时长(车机)"},
                    {"vehMaxSpeed", "试驾最高时速(车机)"},
                    {"testDriveAvgSpeed", "试驾平均时速(车机)"},
                    {"isMatchRecordCn", "是否匹配试驾录音"},
                    {"recordTime", "试驾录音时间"},
                    {"recordDuration", "试驾录音时长"},
                    {"recordScore", "试驾得分"},
            };
            ParamPage<Map<String, Object>> paramPage = new ParamPage<Map<String, Object>>();
            paramPage.setPageIndex(1);
            paramPage.setPageSize(-1);
            paramPage.setParam(dataInfo);
            paramPage.getParam().put("token", token);
            List<Map<String, Object>> transferRecordsList = this.sacTestDriveSheetSingleList(paramPage).getRows();
            ExcelExportUtil.exportExcel(title, columns, transferRecordsList, response);
        } catch (Exception e) {
            log.error("sacTestDriveSheetSingleListExport异常；错误信息{" + e + "}");
            throw e;
        }
        return null;
    }

    /**
     * 试乘试驾单保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Interceptor("csc_clue_test_driver_sheet")
    public OptResult sacTestDriveSheetSave(Map<String, Object> mapParam) {
        OptResult optResult = new OptResult();
        String token = String.valueOf(mapParam.get("token"));
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        if (StringHelper.IsEmptyOrNull(mapParam.get("salesConsultantId"))) {
            mapParam.put("salesConsultantId", userBusiEntity.getUserID());
        }
        if (StringHelper.IsEmptyOrNull(mapParam.get("salesConsultantName"))) {
            mapParam.put("salesConsultantName", userBusiEntity.getEmpName());
        }
        try {
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("updateFlag"))) {
                updateFlag = (Boolean) mapParam.get("updateFlag");
            }
            // 新增
            if (!updateFlag) {
                if (StringHelper.IsEmptyOrNull(mapParam.get("testDriveSheetId"))) {
                    mapParam.put("testDriveSheetId", StringHelper.GetGUID());
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("customerSex"))) {
                    mapParam.put("customerSex", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isEnable"))) {
                    mapParam.put("isEnable", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentChannel"))) {
                    mapParam.put("appointmentChannel", "0");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isCanChange"))) {
                    mapParam.put("isCanChange", "1");
                }
                // 开始里程为空时，将里程初始化为0
                if (StringHelper.IsEmptyOrNull(mapParam.get("testStartRoadHaul"))) {
                    mapParam.put("testStartRoadHaul", "0");
                }
                // 试乘试驾里程初始化为0
                mapParam.put("testRoadHaul", "0");

                // 初始化试乘试驾状态为0：未开始
                if (StringHelper.IsEmptyOrNull(mapParam.get("testStatus"))) {
                    mapParam.put("testStatus", "0");
                }
                BusicenUtils.invokeUserInfo(mapParam, SOU.Save, token);
                // 单号生成
                setOrderCode(mapParam);
                int result = sacTestDriveSheetMapper.insertSacTestDriveSheet(mapParam);
                if (result == 0) {
                    optResult.setMsg(message.get("TEST-DRIVER-SHEET-01"));
                    optResult.setResult("0");
                    return optResult;
                }
                if ("0".equals(mapParam.get("testStatus"))) {
                    mapParam.put("isSendMessage", "1");
                }
            } else {
                BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
                int result = sacTestDriveSheetMapper.updateSacTestDriveSheet(mapParam);
                if (result == 0) {
                    optResult.setMsg(message.get("TEST-DRIVER-SHEET-02"));
                    optResult.setResult("0");
                    return optResult;
                }
                if ("0".equals(mapParam.get("testStatus"))) {
                    mapParam.put("isSendMessage", "1");
                }
            }
        } catch (Exception e) {
            log.error("sacTestDriveSheetSave异常", e);
            throw e;
        }
        return OptResultBuilder.createOk().build();
    }

    /**
     * 试乘试驾单保存
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @Interceptor("csc_clue_test_driver_sheet")
    public OptResult sacTestDriveSheetSave_performance(Map<String, Object> mapParam) {
        logger.info("开始 sacTestDriveSheetSave_performance");
        checkValidate(mapParam);
        logger.info("结束 checkValidate");
        checkRepeat_performance(mapParam);
        logger.info("结束 checkRepeat_performance");
        checkCanChange(mapParam);
        logger.info("结束 checkCanChange");
        OptResult optResult = new OptResult();
        String token = String.valueOf(mapParam.get("token"));
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        if (StringHelper.IsEmptyOrNull(mapParam.get("salesConsultantId"))) {
            mapParam.put("salesConsultantId", userBusiEntity.getUserID());
        }
        if (StringHelper.IsEmptyOrNull(mapParam.get("salesConsultantName"))) {
            mapParam.put("salesConsultantName", userBusiEntity.getEmpName());
        }
        try {
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("updateFlag"))) {
                updateFlag = (Boolean) mapParam.get("updateFlag");
            }
            // 新增
            if (!updateFlag) {
                if (StringHelper.IsEmptyOrNull(mapParam.get("testDriveSheetId"))) {
                    mapParam.put("testDriveSheetId", StringHelper.GetGUID());
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("customerSex"))) {
                    mapParam.put("customerSex", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isEnable"))) {
                    mapParam.put("isEnable", "1");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("appointmentChannel"))) {
                    mapParam.put("appointmentChannel", "0");
                }
                if (StringHelper.IsEmptyOrNull(mapParam.get("isCanChange"))) {
                    mapParam.put("isCanChange", "1");
                }
                // 开始里程为空时，将里程初始化为0
                if (StringHelper.IsEmptyOrNull(mapParam.get("testStartRoadHaul"))) {
                    mapParam.put("testStartRoadHaul", "0");
                }
                // 试乘试驾里程初始化为0
                mapParam.put("testRoadHaul", "0");

                // 初始化试乘试驾状态为0：未开始
                if (StringHelper.IsEmptyOrNull(mapParam.get("testStatus"))) {
                    mapParam.put("testStatus", "0");
                }
                BusicenUtils.invokeUserInfo(mapParam, SOU.Save, token);
                // 单号生成
                //setOrderCode(mapParam);
                mapParam.put("testDriveOrderNo","testDriveOrderNo");
                int result = sacTestDriveSheetMapper.insertSacTestDriveSheet(mapParam);
                logger.info("结束 insertSacTestDriveSheet");
                if (result == 0) {
                    logger.info("结束 result");
                    optResult.setMsg(message.get("TEST-DRIVER-SHEET-01"));
                    optResult.setResult("0");
                    return optResult;
                }
                if ("0".equals(mapParam.get("testStatus"))) {
                    mapParam.put("isSendMessage", "1");
                }
            } else {
                BusicenUtils.invokeUserInfo(mapParam, SOU.Update, token);
                int result = sacTestDriveSheetMapper.updateSacTestDriveSheet(mapParam);
                logger.info("结束 updateSacTestDriveSheet");
                if (result == 0) {
                    logger.info("结束 result");
                    optResult.setMsg(message.get("TEST-DRIVER-SHEET-02"));
                    optResult.setResult("0");
                    return optResult;
                }
                if ("0".equals(mapParam.get("testStatus"))) {
                    mapParam.put("isSendMessage", "1");
                }
            }
        } catch (Exception e) {
            log.error("sacTestDriveSheetSave_performance异常", e);
            throw e;
        }
        addHaul(mapParam);
        logger.info("结束 addHaul");
        updateIsTest(mapParam);
        logger.info("结束 updateIsTest");
        changeDlr(mapParam);
        logger.info("结束 changeDlr");
        saveAppointment(mapParam);
        logger.info("结束 saveAppointment");
        return OptResultBuilder.createOk().build();
    }

    @Override
    public void setOrderCode(Map<String, Object> mapParam) {
        try {
            // 试乘试驾预约单单号生成
            logger.info("试乘试驾单号生成");
            String billTypeId = "bucn_sjsc_no";
            String dlrId = mapParam.get("dlrId").toString();
            String token = String.valueOf(mapParam.get("token"));
            logger.info("试乘试驾单号生成 {} -{} -{}", billTypeId, dlrId, token);
            ListResult<Map<String, Object>> generateOrderCode = baseDataService.generateOrderCode(dlrId, billTypeId,
                    token);
            // 调用服务成功则直接获取单号，不成功则调用自定义单号生成方法
            if ("1".equals(generateOrderCode.getResult())) {
                mapParam.put("testDriveOrderNo", generateOrderCode.getMsg());
            } else {
                throw BusicenException.create("调用生成【试驾单单号】出错！[result=" + generateOrderCode.getResult() + ", msg="
                        + generateOrderCode.getMsg() + "]");
            }
        } catch (Exception e) {
            log.error("试乘试驾预约单单号生成异常", e);
            throw e;
        }
    }

    /**
     * 试乘试驾协议保存
     */
    @Override
    @Interceptor("csc_clue_test_driver_agreement")
    public OptResult sacTestDriveAgreementSave(Map<String, Object> mapParam) {
        OptResult optResult = new OptResult();
        String token = String.valueOf(mapParam.get("token"));
        // 只需要传协议url以及试乘试驾单id
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("testDriveAgreement", mapParam.get("testDriveAgreement"));
        param.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
        try {
            BusicenUtils.invokeUserInfo(param, SOU.Update, token);
            int result = sacTestDriveSheetMapper.updateSacTestDriveSheet(param);
            if (result == 0) {
                optResult.setMsg(message.get("TEST-DRIVER-SHEET-01"));
                optResult.setResult("0");
                return optResult;
            }
        } catch (Exception e) {
            log.error("csc_clue_test_driver_agreement异常", e);
            throw e;
        }
        return OptResultBuilder.createOk().build();
    }

    @Override
    public Result sactestdriveImport(String token, MultipartFile file) {
        long time1 = System.currentTimeMillis();
        Result result = new Result();
        result.setResult("1");
        result.setMsg("导入成功");
        try {

            // 校验文件类型是否正确
            String fileName = file.getOriginalFilename();
            String subffix = fileName.substring(fileName.lastIndexOf('.') + 1);
            if (!"xlsx".equalsIgnoreCase(subffix)) {
                result.setResult("0");
                result.setMsg("该文件不是.xlsx (excel)文件!");
                return result;
            }

            String[] columnName = {"客户姓名", "联系电话", "性别", "试乘试驾类型", "试乘试驾门店编码", "试乘试驾门店名称", "预约试乘试驾日期", "预约试乘试驾时间段", "试乘试驾开始时间",
                    "试乘试驾结束时间", "试乘试驾车型", "试乘试驾车牌", "VIN", "试驾执行产品专家", "试乘试驾开始里程", "试乘试驾结束里程"};
            String[] columnCode = {"customerName", "customerPhone", "customerSex", "testType", "dlrCode", "dlrName", "appointmentTestDate", "appointmentTestTime", "startTime",
                    "endTime", "smallCarTypeName", "plateNumber", "carVin", "salesConsultantName", "testStartRoadHaul", "testEndRoadHaul"};

            // 校验表头是否正确
            InputStream is = file.getInputStream();

			/*List<String> reultList = new LinkedList<String>(Arrays.asList(columnName));
			Workbook hssfWorkbook = WorkbookFactory.create(is);
			Sheet hssfSheet = hssfWorkbook.getSheetAt(0);
			Row headRow = hssfSheet.getRow(0);
			for (Cell cell : headRow) {
				if (!reultList.contains(cell.toString())) {
					result.setResult("0");
					result.setMsg("导入失败 【  错误表头 " + cell.toString() + " 】");
					return result;
				}
			}



			int rowNum = hssfSheet.getLastRowNum();
			if (rowNum > 1000) {
				throw com.ly.mp.busicen.common.context.BusicenException.create("单次导入不能超过1000行，请重新整理再导入！");
			}*/

            List<Map<String, String>> maps = ExcelImportUtil.parseExcel(is);


            long time4 = System.currentTimeMillis();
            System.out.println("当前程序耗时1：" + (time4 - time1) + "ms");
            List<Map<String, Object>> dataList = new ArrayList<>();// 存储导入的数据
            List<String> list = new ArrayList<>();// 存储异常信息
            if (maps.size() > 300) {
                list.add("单次导入不能超过300行，请重新整理再导入！");
                ImportEntiei importClueInfoOut = new ImportEntiei();
                importClueInfoOut.setStrInfo(list);
                result.setRows(importClueInfoOut);
                return result;
            }
            //生成单号参数
            Map<String, Object> map = new HashMap<>();
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            map.put("token", token);
            if (StringUtil.isEmpty(userBusiEntity.getDlrID())) {
                map.put("dlrId", "HOST");
            } else {
                map.put("dlrId", userBusiEntity.getDlrID());
            }

            int j = 1;
            for (Map<String, String> importMap : maps) {
                Map<String, Map<String, Object>> resultMap = getBoutiqueSetImportData(importMap, columnCode, userBusiEntity, map);
                if (StringHelper.IsEmptyOrNull(resultMap.get("checkMes").get("checkInfo"))) {
                    if (!StringHelper.IsEmptyOrNull(resultMap.get("dataMap"))) {
                        //BusicenUtils.invokeUserInfo(resultMap.get("dataMap"), SOU.Save, token);
                        //com.ly.mp.busicen.common.util.BusicenUtils.invokeUserInfo(resultMap.get("dataMap"), com.ly.mp.busicen.common.util.BusicenUtils.SOU.Save, token);
                        dataList.add(resultMap.get("dataMap"));
                    }
                } else {
                    list.add("第" + (j + 1) + "行" + resultMap.get("checkMes").get("checkInfo").toString());
                }
                j++;
            }
            long time3 = System.currentTimeMillis();
            System.out.println("当前程序耗时2：" + (time3 - time4) + "ms");

            // 获取结果保存
            Integer number1 = 0;

            if (!StringHelper.IsEmptyOrNull(dataList)) {
                mybatisBatchExecutor.insertOrUpdateBatch(dataList, SacTestDriveSheetMapper.class, (sacTestDriveSheetMapper, data) ->
                        sacTestDriveSheetMapper.insertSacTestDriveSheetList(data)
                );
                mybatisBatchExecutor.insertOrUpdateBatch(dataList, SacAppointmentSheetMapper.class, (sacAppointmentSheetMapper, data) ->
                        sacAppointmentSheetMapper.insertSacAppointmentSheetList(data)
                );
            }

            ImportEntiei importClueInfoOut = new ImportEntiei();
            int succeed = number1;
            int fail = maps.size() - number1;
            importClueInfoOut.setFail(fail);
            importClueInfoOut.setSucceed(succeed);
            importClueInfoOut.setStrInfo(list);
            result.setRows(importClueInfoOut);
            long time2 = System.currentTimeMillis();
            System.out.println("当前程序耗时3：" + (time2 - time3) + "ms");
        } catch (Exception e) {
            log.error("sacTestDriveSheetMapper异常;补录试驾单数据批量上传错误信息{" + e + "}");
            result.setMsg("导入异常，请检查数据格式是否正确");
            result.setResult("0");
            return result;
        }
        return result;
    }

    @Override
    public OptResult sactestdrivedelete(Map<String, Object> mapParam) {
        OptResult optResult = new OptResult();

        Map<String, Object> param = new HashMap<>();
        param.put("appointmentId", mapParam.get("appointmentId"));
        param.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
        try {
            int result = sacTestDriveSheetMapper.deleteSacTestDriveSheet(param);
            sacAppointmentSheetMapper.deleteSacAppointmentSheet(param);
            if (result == 0) {
                optResult.setMsg("删除失败");
                optResult.setResult("0");
                return optResult;
            }
        } catch (Exception e) {
            log.error("删除补录试驾单失败异常", e);
            throw e;
        }
        return OptResultBuilder.createOk().build();
    }

    @Override
    public OptResult sacTestDriveSendMessage(List<Map<String, Object>> listParam) {

        for (Map<String, Object> mapParam : listParam) {
            //只发送一次短信
            if (!"1".equals(mapParam.get("evaluateFlag"))) {
                try {
                    Map<String, Object> paramM = new HashMap<>();
                    Map<String, Object> param = new HashMap<>();
                    //todo 短信模板还未设置
                    param.put("dlrShortName", mapParam.get("dlrNameOwner"));
                    paramM.put("recNum", new String[]{mapParam.get("phone").toString()});
                    paramM.put("smsParam", param);
                    paramM.put("smsTemplateId", "adp003");
                    //accPushFeignService.smsSendMessage(paramM);

                    //更新补录试驾单状态
                    paramM.put("evaluateFlag", "1");
                    paramM.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
                    sacTestDriveSheetMapper.updateSacTestDriveSheet(paramM);
                } catch (Exception e) {
                    throw new BusicenException("发送消息失败异常：" + mapParam.get("phone"));
                }
            }
        }
        return OptResultBuilder.createOk().build();
    }

    public Map<String, Map<String, Object>> getBoutiqueSetImportData(Map<String, String> importMap, String[] column, UserBusiEntity entity, Map<String, Object> map) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        Map<String, Object> mapVuler = new HashMap<>();// 保存行信息
        StringBuilder checkInfo = new StringBuilder();// 异常行信息
        Map<String, Object> checkMes = new HashMap<>();
        //处理生成试驾单和预约单数据
        Map<String, Object> testDriveMap = new HashMap<>();
        //查询重复数据
        Map<String, Object> testMap = new HashMap<>();
        Page<Map<String, Object>> page = new Page<>(1, 10);

        try {

            if (!MapUtil.hasKeyAndValue(importMap, "客户姓名")) {
                checkInfo.append(",客户姓名不能为空");
            } else {
                mapVuler.put("customerName", importMap.get("客户姓名"));
            }
            if (!MapUtil.hasKeyAndValue(importMap, "联系电话")) {
                checkInfo.append(",联系电话不能为空");
            } else {
                mapVuler.put("customerPhone", importMap.get("联系电话"));
            }
            if (!MapUtil.hasKeyAndValue(importMap, "性别")) {
                checkInfo.append(",性别不能为空");
            } else {
                if ("男".equals(importMap.get("性别"))) {
                    mapVuler.put("customerSex", "1");
                } else {
                    mapVuler.put("customerSex", "0");
                }
            }
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾类型")) {
                checkInfo.append(",试乘试驾类型");
            } else {
                if ("试乘".equals(importMap.get("试乘试驾类型"))) {
                    mapVuler.put("testType", "0");
                } else if ("试驾".equals(importMap.get("试乘试驾类型"))) {
                    mapVuler.put("testType", "1");
                } else if ("超长试乘试驾".equals(importMap.get("试乘试驾类型"))) {
                    mapVuler.put("testType", "2");
                } else {
                    checkInfo.append(",试乘试驾类型非法");
                }
            }
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾门店编码")) {
                checkInfo.append(",试乘试驾门店编码不能为空");
            }
            if ("1".equals(entity.getOrgType()) && !entity.getDlrCode().equals(importMap.get("试乘试驾门店编码"))) {
                checkInfo.append(",非本门店试驾单");
            } else {
                mapVuler.put("dlrCode", importMap.get("试乘试驾类型"));
            }
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾门店名称")) {
                checkInfo.append(",试乘试驾门店名称不能为空");
            } else {
                mapVuler.put("dlrName", importMap.get("试乘试驾门店名称"));
            }
            mapVuler.put("appointmentTestDate", importMap.get("预约试乘试驾日期"));
            mapVuler.put("appointmentTestTime", importMap.get("预约试乘试驾时间段"));

            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾开始时间")) {
                checkInfo.append(",试乘试驾开始时间不能为空");
            } else {
                if (!dateStrIsValid(importMap.get("试乘试驾开始时间"), "yyyy-MM-dd HH:mm:ss")) {
                    checkInfo.append(",试乘试驾开始时间格式异常");
                } else {
                    mapVuler.put("startTime", importMap.get("试乘试驾开始时间"));
                }
            }
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾结束时间")) {
                checkInfo.append(",试乘试驾结束时间不能为空");
            } else {
                if (!dateStrIsValid(importMap.get("试乘试驾结束时间"), "yyyy-MM-dd HH:mm:ss")) {
                    checkInfo.append(",试乘试驾结束时间格式异常");
                } else {
                    mapVuler.put("endTime", importMap.get("试乘试驾结束时间"));
                }
            }

            mapVuler.put("startTime", importMap.get("试乘试驾开始时间"));
            mapVuler.put("endTime", importMap.get("试乘试驾结束时间"));
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾车型")) {
                checkInfo.append(",试乘试驾车型不能为空");
            } else {
                mapVuler.put("smallCarTypeName", importMap.get("试乘试驾车型"));
            }
            if (!MapUtil.hasKeyAndValue(importMap, "试乘试驾车牌")) {
                checkInfo.append(",试乘试驾车牌不能为空");
            } else {
                mapVuler.put("plateNumber", importMap.get("试乘试驾车牌"));
            }
            if (!MapUtil.hasKeyAndValue(importMap, "VIN")) {
                checkInfo.append(",VIN不能为空");
            } else {
                mapVuler.put("carVin", importMap.get("VIN"));
            }
            mapVuler.put("salesConsultantName", importMap.get("试驾执行产品专家"));
            mapVuler.put("testStartRoadHaul", importMap.get("试乘试驾开始里程"));
            mapVuler.put("testEndRoadHaul", importMap.get("试乘试驾结束里程"));


            //异常数据直接记录
            if (!StringUtil.isEmpty(checkInfo)) {
                checkMes.put("checkInfo", checkInfo.toString());
                result.put("dataMap", testDriveMap);
                result.put("checkMes", checkMes);
                return result;
            }

            //试驾单数据
            testDriveMap.put("testDriveSheetId", StringHelper.GetGUID()); //线索单号
            setOrderCode(map); //插入预约单号
            testDriveMap.put("testDriveOrderNo", map.get("testDriveOrderNo")); //试乘试驾预约单ID
            String appointmentId = StringHelper.GetGUID();
            testDriveMap.put("appointmentId", appointmentId); //试乘试驾预约单ID
            testDriveMap.put("testStatus", "2"); //试驾状态已结束
            testDriveMap.put("dlrCode", mapVuler.get("dlrCode")); //门店编码
            testDriveMap.put("dlrName", mapVuler.get("dlrName")); //门店名称
            testDriveMap.put("salesConsultantName", mapVuler.get("salesConsultantName")); //销售名称
            testDriveMap.put("testStartRoadHaul", mapVuler.get("testStartRoadHaul")); //试乘试驾开始里程
            testDriveMap.put("testEndRoadHaul", mapVuler.get("testEndRoadHaul")); //试乘试驾结束里程
            testDriveMap.put("testRoadHaul", Integer.parseInt((String) mapVuler.get("testEndRoadHaul")) - Integer.parseInt((String) mapVuler.get("testStartRoadHaul"))); //试乘试驾里程
            testDriveMap.put("dlrClueOrderNo", StringHelper.GetGUID()); //线索单号
            testDriveMap.put("customerName", mapVuler.get("customerName")); //客户姓名
            testDriveMap.put("customerPhone", mapVuler.get("customerPhone")); //联系电话
            testDriveMap.put("customerSex", mapVuler.get("customerSex")); //性别
            testDriveMap.put("smallCarTypeName", mapVuler.get("smallCarTypeName")); //试乘试驾车型名称
            testDriveMap.put("smallCarTypeCode", mapVuler.get("smallCarTypeName")); //试乘试驾车型
            testDriveMap.put("plateNumber", mapVuler.get("plateNumber")); //试驾车牌
            testDriveMap.put("carVin", mapVuler.get("carVin")); //vin
            testDriveMap.put("testType", mapVuler.get("testType")); //试驾类型
            testDriveMap.put("startTime", mapVuler.get("startTime")); //开始时间
            testDriveMap.put("endTime", mapVuler.get("endTime")); //结束时间

            testDriveMap.put("oemId", entity.getOemID());
            testDriveMap.put("oemCode", entity.getOemCode());
            testDriveMap.put("groupId", entity.getGroupID());
            testDriveMap.put("groupCode", entity.getGroupCode());
            testDriveMap.put("createdName", entity.getEmpName());
            testDriveMap.put("modifyName", entity.getEmpName());
            testDriveMap.put("modifier", entity.getUserID());
            testDriveMap.put("creator", entity.getUserID());
            testDriveMap.put("updateControlId", StringHelper.GetGUID());
            testDriveMap.put("createdDate", LocalDateTime.now());
            testDriveMap.put("lastUpdatedDate", LocalDateTime.now());

            //预约单数据
            testDriveMap.put("isTestDrive", "1"); //已试驾
            setAppointmentOrderCode(map);
            testDriveMap.put("appointmentOrderNo", map.get("appointmentOrderNo")); //预约单单号
            testDriveMap.put("appointmentTestDate", mapVuler.get("appointmentTestDate")); //预约试乘试驾日期(普通试乘试驾)
            testDriveMap.put("appointmentTestTime", mapVuler.get("appointmentTestTime")); //预约试乘试驾时间段(普通试乘试驾)
            //if ("2".equals(mapVuler.get("testType"))) {
            String[] times = mapVuler.get("appointmentTestTime").toString().split("-");
            String appointmentStartTime = mapVuler.get("appointmentTestDate") + " " + times[0] + ":00";
            String appointmentEndTime = mapVuler.get("appointmentTestDate") + " " + times[1] + ":00";
            testDriveMap.put("appointmentStartTime", appointmentStartTime); //预约试乘试驾时间段(普通试乘试驾)
            testDriveMap.put("appointmentEndTime", appointmentEndTime); //预约试乘试驾时间段(普通试乘试驾)
            testDriveMap.put("isEnable", "1");
            testDriveMap.put("appointmentChannel", "0");//预约渠道默认0：门店自建

            //查重
            testMap.put("customerPhone", mapVuler.get("customerPhone"));
            testMap.put("testStatus", "2");
			/*List<Map<String, Object>> maps = sacTestDriveSheetMapper.selectSacTestDriveSheet(testMap, page);
			if (maps.size() > 0) {
				testDriveMap.put("repeatFlag", "1");//补录重复数据
			} else {
				testDriveMap.put("repeatFlag", "0");
			}*/
            testDriveMap.put("importFlag", "1");//补录标识
        } catch (BusicenException e) {
            throw new BusicenException("导入异常：" + e.getMessage());
        }

        checkMes.put("checkInfo", checkInfo.toString());
        result.put("dataMap", testDriveMap);
        result.put("checkMes", checkMes);
        return result;
    }

    public boolean dateStrIsValid(String rawDateStr, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        Date date = null;
        try {
            // 转化为 Date类型测试判断
            date = dateFormat.parse(rawDateStr);
            return rawDateStr.equals(dateFormat.format(date));
        } catch (Exception e) {
            return false;
        }

    }


    private void setAppointmentOrderCode(Map<String, Object> mapParam) {
        try {
            // 试乘试驾预约单单号生成
            String billTypeId = "bucn_yy_no";
            String dlrId = mapParam.get("dlrId").toString();
            String token = String.valueOf(mapParam.get("token"));
            ListResult<Map<String, Object>> generateOrderCode = baseDataService.generateOrderCode(dlrId, billTypeId,
                    token);
            // 调用服务成功则直接获取单号，不成功则调用自定义单号生成方法
            if ("1".equals(generateOrderCode.getResult())) {
                mapParam.put("appointmentOrderNo", generateOrderCode.getMsg());
            } else {
                throw BusicenException.create("调用生成【试驾预约单单号】出错！[result=" + generateOrderCode.getResult() + ", msg="
                        + generateOrderCode.getMsg() + "]");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 前后置方法
     *
     * @param registor
     */
    @Override
    @SuppressWarnings("unchecked")
    public void regist(InterceptorWrapperRegistor registor) {
        // 校验字段
        registor.before("csc_clue_test_driver_sheet_valid", (context, model) -> {
            checkValidate((Map<String, Object>) context.data().getP()[0]);
        });
        // 校验是新增还是修改并查重
        registor.before("csc_clue_test_driver_sheet_repeat", (context, model) -> {
            checkRepeat((Map<String, Object>) context.data().getP()[0]);
        });
        // 校验是新增还是修改并查重
        registor.before("csc_clue_test_driver_sheet_repeat_performance", (context, model) -> {
            checkRepeat_performance((Map<String, Object>) context.data().getP()[0]);
        });
        //校验是否可以更换跟进门店，已开始的话不能更换门店/已经是从别的门店更换过来的试驾单不能做二次更换
        registor.before("csc_clue_test_driver_sheet_canChange", (context, model) -> {
            checkCanChange((Map<String, Object>) context.data().getP()[0]);
        });
        //校验是否可以更换跟进门店，已开始的话不能更换门店/已经是从别的门店更换过来的试驾单不能做二次更换
        registor.before("csc_clue_test_driver_sheet_canChange_performanc", (context, model) -> {
            checkCanChange((Map<String, Object>) context.data().getP()[0]);
        });
        // 修改试驾单后修改预约单（模型不需要用到）
        registor.after("csc_clue_test_driver_sheet_change", (context, model) -> {
            saveAppointment((Map<String, Object>) context.data().getP()[0]);
        });
        // 更新试驾车的里程与试驾车状态
        registor.after("csc_clue_test_driver_sheet_addhaul", (context, model) -> {
            addHaul((Map<String, Object>) context.data().getP()[0]);
        });
        // 更新试乘试驾预约的是否试驾状态
        registor.after("csc_clue_test_driver_sheet_istest", (context, model) -> {
            updateIsTest((Map<String, Object>) context.data().getP()[0]);
        });
        //更改跟进门店后置
        registor.after("csc_clue_test_driver_sheet_isChange", (context, model) -> {
            changeDlr((Map<String, Object>) context.data().getP()[0]);
        });
        // 试乘试驾协议字段校验
        registor.before("csc_clue_test_driver_agreement_valid", (context, model) -> {
            checkAgreementValidate((Map<String, Object>) context.data().getP()[0]);
        });
        // 试乘试驾协议保存：判断id是否存在
        registor.before("csc_clue_test_driver_agreement_exits", (context, model) -> {
            checkAgreementExits((Map<String, Object>) context.data().getP()[0]);
        });
        // 新增时更新评价表，初始化各评价项为0分
        registor.after("csc_clue_test_driver_sheet_evaluation", (context, model) -> {

        });
        // 试乘试驾开始/结束字段校验
        registor.before("csc_clue_test_driver_status_valid", (context, model) -> {
            checkDriveStatusValidate((Map<String, Object>) context.data().getP()[0]);
        });
        // 更新试驾车状态
        registor.after("csc_clue_test_driver_status_car", (context, model) -> {
            addHaul((Map<String, Object>) context.data().getP()[0]);
        });
        // 更新试乘试驾预约的是否试驾状态
        registor.after("csc_clue_test_driver_status_istest", (context, model) -> {
            updateIsTest((Map<String, Object>) context.data().getP()[0]);
        });
        // 试乘试驾结束时，若存在原试乘试驾单则修改原试乘试驾单与原预约单
        registor.after("csc_clue_test_driver_status_old", (context, model) -> {
            editHis((Map<String, Object>) context.data().getP()[0]);
            //    sendMsg((Map<String, Object>) context.data().getP()[0]);
        });
        registor.after("csc_clue_test_driver_sheet_sendMessage", (context, model) -> {
            editHis((Map<String, Object>) context.data().getP()[0]);
            CompletableFuture.runAsync(() -> {
                this.sendMessage((Map<String, Object>) context.data().getP()[0]);
            }, asyncTaskExecutor);
        });

    }

    private void sendMsg(Map<String, Object> mapParam) {
        HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(mapParam);
        // Map<String, Object> urlMap = sacTestDriveSheetMapper.findUrl();

        try {

            if ("2".equals(mapParam.get("testStatus"))) {
                sendTda(mapParam, driveEndMap);
                sendZtMQ(mapParam, driveEndMap);
            }

        } catch (Exception e) {
            log.error("sendMsg", e);
            throw e;
        }

    }

    /**
     * 试乘试驾单保存字段校验
     *
     * @param mapParam
     */
    public void checkValidate(Map<String, Object> mapParam) {
        ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-test-driver-sheet-check", "maindata");
        String resMsg = fireRule.getNotValidMessage();
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
        // 如果试乘试驾单开始时间大于等于结束时间则不合法
        try {
            if (!StringHelper.IsEmptyOrNull(mapParam.get("startTime"))
                    && !StringHelper.IsEmptyOrNull(mapParam.get("endTime"))) {
                SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date startTime = ft.parse(mapParam.get("startTime").toString());
                Date endTime = ft.parse(mapParam.get("endTime").toString());
                // 开始时间不能大于等于结束时间
                if (startTime.compareTo(endTime) >= 0) {
                    throw new BusicenException(message.get("TEST-DRIVER-SHEET-07"));
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_test_driver_sheet_valid异常", e);
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 校验是否可以更换跟进门店，已开始的话不能更换门店/已经是从别的门店更换过来的试驾单不能做二次更换
     *
     * @param mapParam
     */
    public void checkCanChange(Map<String, Object> mapParam) {
        Boolean updateFlag = false;
        updateFlag = (Boolean) mapParam.get("updateFlag");
        if (updateFlag) {
            //若想要更换门店
            if (!StringHelper.IsEmptyOrNull(mapParam.get("testDlrCode"))
                    && !mapParam.get("testDlrCode").equals(mapParam.get("dlrCode"))) {
                // 判断是否二次更换门店,根据id去查询试驾单，若isCanChange为0则代表已经更换门店了，不能再次更换
                QueryWrapper<SacTestDriveSheet> sacTestDriveSheetqQueryWrapper = new QueryWrapper<SacTestDriveSheet>();
                sacTestDriveSheetqQueryWrapper.eq("TEST_DRIVE_SHEET_ID", mapParam.get("testDriveSheetId"))
                        .eq("IS_ENABLE", "1");
                SacTestDriveSheet sacTestDriveSheet = baseMapper.selectOne(sacTestDriveSheetqQueryWrapper);
                if (sacTestDriveSheet != null && "0".equals(sacTestDriveSheet.getIsCanChange())) {
                    throw new BusicenException("不能进行二次更换门店");
                }
                // 更换之后原试驾单与新试驾单的isCanChange都为0
                mapParam.put("isCanChange", "0");
                // 备份一个原原试驾单id
                mapParam.put("newOldTestDriveSheetId", mapParam.get("oldTestDriveSheetId"));
                // 这个是避免原试驾单保存时将自身id存进oldTestDriveSheetId
                mapParam.remove("oldTestDriveSheetId");
                //代表当前是更换门店操作
                mapParam.put("isChangeDlr", "1");
            }
        }
    }

    /**
     * 判断是否修改
     *
     * @param mapParam
     */
    public void checkRepeat(Map<String, Object> mapParam) {
        try {
            String token = String.valueOf(mapParam.get("token"));
            UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            // 判断是新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("testDriveSheetId"))) {
                // 数据库查询是否存在此id
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
                Page<Map<String, Object>> page = new Page<Map<String, Object>>(1, -1);
//                List<Map<String, Object>> list = baseMapper.selectSacTestDriveSheet(map, page);
                Long counts = baseMapper.selectSacTestDriveSheetCount(map);
                if (counts > 0) {
                    updateFlag = true;
                }
            }

            mapParam.put("updateFlag", updateFlag);
            mapParam.put("dlrCode", userBusiEntity.getDlrCode());
            mapParam.put("dlrName", userBusiEntity.getDlrName());
            mapParam.put("dlrId", userBusiEntity.getDlrID());

        } catch (Exception e) {
            log.error("csc_clue_test_driver_sheet_repeat异常", e);
            throw e;
        }
    }

    public void checkRepeat_performance(Map<String, Object> mapParam) {
        try {
            String token = String.valueOf(mapParam.get("token"));
            Object userBusiEntityObj = mapParam.get("userBusiEntity");
            UserBusiEntity userBusiEntity = null;
            if(Objects.isNull(userBusiEntityObj)) {
                userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
            }
            userBusiEntity = (UserBusiEntity) userBusiEntityObj;
            // 判断是新增还是修改
            Boolean updateFlag = false;
            if (!StringHelper.IsEmptyOrNull(mapParam.get("testDriveSheetId"))) {
                // 数据库查询是否存在此id
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
                Page<Map<String, Object>> page = new Page<Map<String, Object>>(1, -1);
//                List<Map<String, Object>> list = baseMapper.selectSacTestDriveSheet(map, page);
                Long counts = baseMapper.selectSacTestDriveSheetCount(map);
                if (counts > 0) {
                    updateFlag = true;
                }
            }

            mapParam.put("updateFlag", updateFlag);
            mapParam.put("dlrCode", userBusiEntity.getDlrCode());
            mapParam.put("dlrName", userBusiEntity.getDlrName());
            mapParam.put("dlrId", userBusiEntity.getDlrID());

        } catch (Exception e) {
            log.error("csc_clue_test_driver_sheet_repeat_performance异常", e);
            throw e;
        }
    }

    /**
     * 试乘试驾协议字段校验
     *
     * @param mapParam
     */
    public void checkAgreementValidate(Map<String, Object> mapParam) {
        ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-test-driver-agreement-check", "maindata");
        String resMsg = fireRule.getNotValidMessage();
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
    }

    /**
     * 试乘试驾协议保存：判断id是否存在
     *
     * @param mapParam
     */
    public void checkAgreementExits(Map<String, Object> mapParam) {
        String testDriveSheetId = mapParam.get("testDriveSheetId").toString();
        QueryWrapper<SacTestDriveSheet> queryWrapper = new QueryWrapper<SacTestDriveSheet>();
        queryWrapper.eq("TEST_DRIVE_SHEET_ID", testDriveSheetId);
        int count = baseMapper.selectCount(queryWrapper);
        if (count < 1) {
            throw new BusicenException(message.get("TEST-DRIVER-SHEET-03"));
        }
    }

    /**
     * 更新试驾车的里程与试驾车状态
     *
     * @param mapParam
     */
    public void addHaul(Map<String, Object> mapParam) {
        try {
            if ("1".equals(mapParam.get("testStatus")) || "2".equals(mapParam.get("testStatus"))) {
                Map<String, Object> carParam = new HashMap<String, Object>();
                BuTestcarPrepare prepare = new BuTestcarPrepare();
                // 结束试乘试驾则更新试驾车表（里程，试驾车状态）
                if ("2".equals(mapParam.get("testStatus"))) {
                    //结束时更新最新里程
                    prepare.setTestcarKilometers(Integer.valueOf(mapParam.get("testEndRoadHaul").toString()));
                    // carParam.put("testcarKilometers", mapParam.get("testEndRoadHaul"));

                    //  carParam.put("testcarFrequency", 1);
                    //   carParam.put("carStatusCode", "0");
                    //  carParam.put("carStatusName", "空闲中");
                    prepare.setTestcarFrequency(1);
                    prepare.setCarStatusCode("0");
                    prepare.setCarStatusName("空闲中");
                } else {
                    //  carParam.put("carStatusCode", "1");
                    //   carParam.put("carStatusName", "试驾中");
                    prepare.setCarStatusCode("1");
                    prepare.setCarStatusName("试驾中");
                }
                //车架号
                prepare.setVin(mapParam.get("carVin").toString());
                //carParam.put("vin", mapParam.get("carVin"));
                ParamPage<BuTestcarPrepare> carParamPage = new ParamPage<>();
                carParamPage.setParam(prepare);
                EntityResult<Map<String, Object>> result = baseService.inset(String.valueOf(mapParam.get("token")), carParamPage);
                if (!"1".equals(result.getResult())) {
                    throw new BusicenException(message.get("TEST-DRIVER-SHEET-04"));
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_test_driver_sheet_addhaul异常", e);
            throw e;
        }
    }

    // 修改试驾单后修改预约
    public void saveAppointment(Map<String, Object> mapParam) {
        Boolean updateFlag = (Boolean) mapParam.get("updateFlag");
        if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId")) && updateFlag) {
            // 更改预约单
            Map<String, Object> newParam = new HashMap<String, Object>();
            newParam.put("token", mapParam.get("token"));
            //更换门店时不改原门店的任何预约时间
            if ("1".equals(mapParam.get("isChangeDlr"))) {
                //更换跟进门店时则不能修改原试驾预约单的预约时间，将当前传来的时间包装成新的门店预约时间
                if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentTestDate"))
                        && !StringHelper.IsEmptyOrNull(mapParam.get("appointmentTestTime"))) {
                    String[] timeStrings = String.valueOf(mapParam.get("appointmentTestTime")).split("-");
                    String newAppointmentStartTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[0] + ":00";
                    String newAppointmentEndTime = String.valueOf(mapParam.get("appointmentTestDate")) + " " + timeStrings[1] + ":00";
                    newParam.put("newDlrAppointmentStartTime", newAppointmentStartTime);
                    newParam.put("newDlrAppointmentEndTime", newAppointmentEndTime);
                }
                // 根据appointmentid查询原预约单的预约时间
                SacAppointmentSheet appointmentSheet = sacAppointmentSheetMapper.selectById(String.valueOf(mapParam.get("appointmentId")));
                if (appointmentSheet == null) {
                    throw new BusicenException("未找到该预约单，请检查预约单id是否正确");
                }
                newParam.put("appointmentTestDate", appointmentSheet.getAppointmentTestDate());
                newParam.put("appointmentTestTime", appointmentSheet.getAppointmentTestTime());
                newParam.put("appointmentStartTime", appointmentSheet.getAppointmentStartTime());
                newParam.put("appointmentEndTime", appointmentSheet.getAppointmentEndTime());
            } else {
                // 不更换门店时直接获取参数
                newParam.put("appointmentTestDate", mapParam.get("appointmentTestDate"));
                newParam.put("appointmentTestTime", mapParam.get("appointmentTestTime"));
                newParam.put("appointmentStartTime", mapParam.get("appointmentStartTime"));
                newParam.put("appointmentEndTime", mapParam.get("appointmentEndTime"));
            }
            newParam.put("testType", mapParam.get("testType"));
            newParam.put("plateNumber", mapParam.get("plateNumber"));
            newParam.put("carVin", mapParam.get("carVin"));
            newParam.put("smallCarTypeCode", mapParam.get("smallCarTypeCode"));
            newParam.put("smallCarTypeName", mapParam.get("smallCarTypeName"));
            newParam.put("appointmentId", mapParam.get("appointmentId"));
            newParam.put("isTestDrive", "0");
            newParam.put("customerId", mapParam.get("customerId"));
            newParam.put("customerName", mapParam.get("customerName"));
            newParam.put("customerPhone", mapParam.get("customerPhone"));
            newParam.put("dlrClueOrderNo", mapParam.get("dlrClueOrderNo"));
            newParam.put("appointmentChannel", mapParam.get("appointmentChannel"));
            if (StringHelper.IsEmptyOrNull(mapParam.get("isFollow"))) {
                newParam.put("isFollow", "0");
            }
            SpringContextHolder.getBean(SacAppointmentSheetService.class).appointmentSheetSave(newParam);
        }

    }

    /**
     * 更新试乘试驾预约的是否试驾状态为1，表示已开始试驾
     *
     * @param mapParam
     */
    public void updateIsTest(Map<String, Object> mapParam) {
        try {
            // 开始或结束试乘试驾单时，修改预约单的状态
            if ("1".equals(mapParam.get("testStatus")) || "2".equals(mapParam.get("testStatus"))) {
                //判断是否有预约单
                QueryWrapper<SacTestDriveSheet> queryWrapperTestDrive = new QueryWrapper<SacTestDriveSheet>();
                queryWrapperTestDrive.eq("TEST_DRIVE_SHEET_ID", String.valueOf(mapParam.get("testDriveSheetId")));
                queryWrapperTestDrive.lambda().select(SacTestDriveSheet::getAppointmentId);
                SacTestDriveSheet sacTestDriveSheet = sacTestDriveSheetMapper.selectOne(queryWrapperTestDrive);
//                SacTestDriveSheet sacTestDriveSheet = sacTestDriveSheetMapper
//                        .selectById(String.valueOf(mapParam.get("testDriveSheetId")));
                if (!StringHelper.IsEmptyOrNull(sacTestDriveSheet.getAppointmentId())) {
                    // 先验证一下预约单id是否存在
                    QueryWrapper<SacAppointmentSheet> queryWrapper = new QueryWrapper<SacAppointmentSheet>();
                    queryWrapper.eq("APPOINTMENT_ID", sacTestDriveSheet.getAppointmentId());
                    int count = sacAppointmentSheetMapper.selectCount(queryWrapper);
                    if (count == 0) {
                        throw new BusicenException(message.get("APPOINTMENT-SHEET-08"));
                    }
                    // id存在则修改预约单
                    Map<String, Object> param = new HashMap<String, Object>();
                    param.put("appointmentId", sacTestDriveSheet.getAppointmentId());
                    param.put("isTestDrive", "1");
                    BusicenUtils.invokeUserInfo(param, SOU.Update, mapParam.get("token").toString());
                    int result = sacAppointmentSheetMapper.updateSacAppointmentSheet(param);
                    if (result == 0) {
                        throw new BusicenException(message.get("TEST-DRIVER-SHEET-06"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("csc_clue_test_driver_sheet_istest异常", e);
            throw e;
        }
    }

    /**
     * 更换门店操作
     */
    public void changeDlr(Map<String, Object> mapParam) {

        if ("1".equals(mapParam.get("isChangeDlr"))) {
            mapParam.put("oldTestDriveSheetId", mapParam.get("newOldTestDriveSheetId"));
        }

    }


    /**
     * 字段校验
     *
     * @param mapParam
     */
    public void checkDriveStatusValidate(Map<String, Object> mapParam) {
        ValidResultCtn fireRule = fireFieldRule.fireRule(mapParam, "csc-clue-test-driver-status-check", "maindata");
        String resMsg = fireRule.getNotValidMessage();
        if (!fireRule.isValid()) {
            throw new BusicenException(resMsg);
        }
    }

    /**
     * 试乘试驾开始/结束
     */
    @Override
    @Interceptor("csc_clue_test_driver_status")
    public OptResult sacTestDriveSheetStatus(Map<String, Object> mapParam) {
        logger.info("sacTestDriveSheetStatus入参:{}", JSONObject.toJSONString(mapParam));
        // 先查询试驾单信息
        HashMap<String, Object> driveSheetMap= sacTestDriveSheetMapper.findSheet(mapParam);
        // 幂等校验
        // 如果试乘试驾单结束，则更新行驶里程
        if (TestStatusEnum.COMPLETED.getCode().equals(mapParam.get("testStatus"))) {
            if (StringHelper.IsEmptyOrNull(mapParam.get("testEndRoadHaul"))) {
                throw BusicenException.create(message.get("TEST-DRIVER-SHEET-08"));
            }
            // 记录开始里程
            double testStartRoadHaul = Double.valueOf(String.valueOf(mapParam.get("testStartRoadHaul")));
            double testEndRoadHaul = Double.valueOf(String.valueOf(mapParam.get("testEndRoadHaul")));
            if (testStartRoadHaul >= testEndRoadHaul) {
                throw BusicenException.create(message.get("TEST-DRIVER-SHEET-09"));
            }

            double testRoadHaul = testEndRoadHaul - testStartRoadHaul;
            mapParam.put("testRoadHaul", testRoadHaul);
            // HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(mapParam);

            try {
                sendTda(mapParam, driveSheetMap);
            } catch (Exception e) {
                logger.info("发送tda失败{}", e);
            }
            SactdaZtService.sendZtMQ(mapParam, driveSheetMap);
            // 试驾结束时记录接口表，获取BI车机数据
            insertIfVehicleData(driveSheetMap);
        }
        if (TestStatusEnum.IN_PROGRESS.getCode().equals(mapParam.get("testStatus"))) {
            // Map<String, Object> receiverTime = sacTestDriveSheetMapper.findReceiverTime(mapParam);
            driveSheetMap.put("plateNumber", mapParam.get("plateNumber"));
            OptResult validateResult = validateDriveSheet(driveSheetMap);
            if (validateResult.getResult().equals(CommonConstant.FAIL_CODE)) {
                return validateResult;
            }
            Map<String, Object> infoDlrMap = sacTestDriveSheetMapper.selectClueInfoDlr(mapParam);
            Object smallCarTypeCode = mapParam.get("smallCarTypeCode");
            Object carTypeCode = null;
            if (!StringHelper.IsEmptyOrNull(infoDlrMap) && !StringUtils.isEmpty(smallCarTypeCode)) {
                String intenCarTypeCode = infoDlrMap.get("intenCarTypeCode").toString();
                boolean typeCode = intenCarTypeCode.contains(smallCarTypeCode.toString());
                if (!typeCode) {
                    carTypeCode = intenCarTypeCode + "," + smallCarTypeCode;
                }
            } else {
                carTypeCode = (String) smallCarTypeCode;
            }
            LocalDateTime localDateTime = LocalDateTime.now();
            mapParam.put("lastTestdriverTime", localDateTime);
            try {
                if(Objects.nonNull(mapParam.get("IDNumber"))) {
                    mapParam.put("IDNumberEncryption", aesUtil.encrypt(mapParam.get("IDNumber").toString()));
                }
            }
            catch (Exception e) {
                log.error("身份证号加密失败", e);
            }
            Integer updateCount = sacTestDriveSheetMapper.updateClueInfoDlrActivation(carTypeCode, mapParam);
            if(updateCount == 0) {
                sacTestDriveSheetMapper.updateClueInfoDlr(carTypeCode, mapParam);
            }
            sacTestDriveSheetMapper.updateReview(carTypeCode, mapParam);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("bk", mapParam.get("customerPhone")); // 主键
            paramMap.put("mobile", mapParam.get("customerPhone")); // 手机号码
            paramMap.put("c_lastupdate_system", "ADP"); // 最后更新系统来源
            paramMap.put("remark", "客户信息修改");
            paramMap.put("c_interested_car_model", carTypeCode);
            sacTestDriveSheetMapper.insertCdpLeads(paramMap);
        }
        BusicenUtils.invokeUserInfo(mapParam, SOU.Update, mapParam.get("token").toString());
        sacTestDriveSheetMapper.updateSacTestDriveSheet(mapParam);
        return OptResultBuilder.createOk().build();
    }

    @Override
    public OptResult sacTestDriveSheetStatusRedisLock(Map<String, Object> param) {
        TestStatusEnum testStatus = TestStatusEnum.getTestStatusEnumByCode(String.valueOf(param.get("testStatus")));
        // 是否开始试驾操作
        boolean isDriveStart = testStatus.getCode().equals(TestStatusEnum.IN_PROGRESS.getCode());
        String actionDesc = isDriveStart ? "开始试驾" : "结束试驾";
        String testDriveSheetId = String.valueOf(param.get("testDriveSheetId"));
        String lockPrefix = isDriveStart ? RedisLockPrefixConstants.DRIVE_START_LOCK_PREFIX : RedisLockPrefixConstants.DRIVE_END_LOCK_PREFIX + ":" + testDriveSheetId;
        // 获取分布式锁
        boolean lock = redisLockUtil.lock(lockPrefix, testDriveSheetId);
        if (!lock) {
            // 当前已被锁
            return OptResultBuilder
                    .createFail(String.format("当前试驾单在%s操作中,请稍后重试", actionDesc))
                    .build();
        }
        try {
            return sacTestDriveSheetStatus(param);
        } catch (BusicenException e) {
            // 业务异常返回对应系统错误原因
            return OptResultBuilder
                    .createFail(actionDesc + "失败，失败原因：" + e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error(actionDesc + "失败，失败原因：", e);
            return OptResultBuilder
                    .createFail()
                    .setMsg(actionDesc + "失败")
                    .build();
        } finally {
            redisLockUtil.unlock(lockPrefix, testDriveSheetId);
        }
    }

    /**
     * 验证试驾单
     */
    public OptResult validateDriveSheet(HashMap<String, Object> driveSheetMap) {
        // 1. 验证签到时间
        if (!hasSignedIn(driveSheetMap)) {
            return OptResultBuilder
                    .createFail("请先试驾签到")
                    .build();
        }

        // 2. 检查是否有正在进行的试驾
        Optional<SacTestDriveSheet> ongoingDrive = findDrivingSheet(driveSheetMap);
        if (ongoingDrive.isPresent()) {
            return createDrivingErrorResult(ongoingDrive.get());
        }

        return OptResultBuilder.createOk().build();
    }

    /**
     * 检查是否已签到
     */
    private boolean hasSignedIn(HashMap<String, Object> driveSheetMap) {
        return Objects.nonNull(driveSheetMap.get("receiverTime"));
    }

    /**
     * 查找正在进行的试驾单
     */
    private Optional<SacTestDriveSheet> findDrivingSheet(HashMap<String, Object> driveSheetMap) {
        String plateNumber = String.valueOf(driveSheetMap.get("plateNumber"));

        return getCurrentDriveSheets(plateNumber).stream()
                .filter(this::isDriving)
                .findFirst();
    }

    /**
     * 获取当前车辆的所有试驾单
     */
    private List<SacTestDriveSheet> getCurrentDriveSheets(String plateNumber) {
        SacTestDriveSheet query = new SacTestDriveSheet();
        query.setPlateNumber(plateNumber);

        return sacTestDriveSheetMapper.selectList(
                query.buildQueryWrapper(
                        SacTestDriveSheet::getTestStatus,
                        SacTestDriveSheet::getSalesConsultantName,
                        SacTestDriveSheet::getCustomerName
                )
        );
    }

    /**
     * 判断是否正在试驾中
     */
    private boolean isDriving(SacTestDriveSheet driveSheet) {
        return TestStatusEnum.IN_PROGRESS.getCode()
                .equals(driveSheet.getTestStatus());
    }

    /**
     * 创建试驾中的错误结果
     */
    private OptResult createDrivingErrorResult(SacTestDriveSheet driveSheet) {
        String message = String.format(
                "当前车辆正在试驾中，试驾产品专家%s，客户为%s",
                driveSheet.getSalesConsultantName(),
                driveSheet.getCustomerName()
        );

        return OptResultBuilder
                .createFail(message)
                .build();
    }

    /**
     * 试驾结束时记录接口表，获取BI车机数据
     * @param driveEndMap
     */
    private void insertIfVehicleData(HashMap<String, Object> driveEndMap) {
        // 实际插入车机数据接口表的参数
        HashMap<String, Object> paramMap = new HashMap<>(3);
        paramMap.put("logsId", UUIDUtils.getUUIDString());
        paramMap.put("testDriveOrderNo", driveEndMap.get("testDriveOrderNo"));
        paramMap.put("insertDate", LocalDateTime.now());
        // 调用xapiInsert的公共入参
        Map<String, Object> param = new HashMap<>(1);
        param.put("mapParam", paramMap);
        xapiPushFeignService.sendApiData("drive_insert_vehicle_data", param);
    }

    /**
     * 若试驾单结束且存在原试驾单则修改原试驾单状态、原预约单状态
     */
    public void editHis(Map<String, Object> mapParam) {
        try {
            // 先查询试乘试驾单是否存在
            SacTestDriveSheet sacTestDriveSheet = sacTestDriveSheetMapper
                    .selectById(String.valueOf(mapParam.get("testDriveSheetId")));
            if (sacTestDriveSheet != null && "2".equals(mapParam.get("testStatus"))
                    && sacTestDriveSheet.getOldTestDriveSheetId() != null) {
                // 当试乘试驾单结束后更改原试驾单状态、原预约单状态
                String[] oldIdList = sacTestDriveSheet.getOldTestDriveSheetId().split(",");
                for (String oldId : oldIdList) {
                    // 根据id查询试乘试驾单
                    SacTestDriveSheet testDriveSheet = sacTestDriveSheetMapper.selectById(oldId);
                    if (testDriveSheet != null) {
                        // 若存在原试驾单，修改原试乘试驾单状态
                        testDriveSheet.setTestStatus(String.valueOf(mapParam.get("testStatus")));
                        sacTestDriveSheetMapper.updateById(testDriveSheet);
                        // 修改预约单状态
                        if (!StringHelper.IsEmptyOrNull(testDriveSheet.getAppointmentId())) {
                            SacAppointmentSheet sacAppointmentSheet = new SacAppointmentSheet();
                            sacAppointmentSheet.setAppointmentId(testDriveSheet.getAppointmentId());
                            sacAppointmentSheet.setIsTestDrive("1");
                            sacAppointmentSheetMapper.updateById(sacAppointmentSheet);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("csc_clue_test_driver_status_old异常", e);
            throw e;
        }

    }

    /**
     * 试乘试驾详情查询
     */
    @Override
    public EntityResult<Map<String, Object>> sacTestDriveSheetQueryDetail(Map<String, Object> mapParam) {
        // 判断id是否为空
        if (StringHelper.IsEmptyOrNull(mapParam.get("testDriveSheetId"))) {
            throw new BusicenException(message.get("TEST-DRIVER-SHEET-10"));
        }
        // 查找详情
        List<Map<String, Object>> list = sacTestDriveSheetMapper.selectSacTestDriveSheetDetail(mapParam);
        // 校验试乘试驾单id是否存在
        if (list.size() == 0 || list.get(0).isEmpty()) {
            throw new BusicenException(message.get("TEST-DRIVER-SHEET-11"));
        }
        return ResultHandler.updateOk(list.get(0));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EntityResult<Map<String, Object>> sacTestDriveSheetAllSave(Map<String, Object> mapParam) {
        try {
            SpringContextHolder.getBean(SacAppointmentSheetService.class).appointmentSheetSave(mapParam);
            mapParam.put("custId", mapParam.get("customerId"));
            SpringContextHolder.getBean(SacTestDriveSheetService.class).sacTestDriveSheetSave(mapParam);
        } catch (Exception e) {
            log.error("sacTestDriveSheetAllSave异常", e);
            throw e;
        }
        return ResultHandler.updateOk(mapParam);
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public EntityResult<Map<String, Object>> sacTestDriveSheetAllSave_performance(Map<String, Object> mapParam) {
        try {
//            GtsTemplate.doTrans("saveTestDriveSheetPerformance", () -> {
                //不管新增还是修改，性别都能修改
                if (!StringHelper.IsEmptyOrNull(mapParam.get("customerSexCode"))) {
                    mapParam.put("customerSex", mapParam.get("customerSexCode"));
                }
                mapParam.put("skipClue", Boolean.TRUE);
                sacAppointmentSheetService.appointmentSheetSave_performance(mapParam);
                //SpringContextHolder.getBean(SacAppointmentSheetService.class).appointmentSheetSave_performance(mapParam);

                mapParam.put("custId", mapParam.get("customerId"));
                sacTestDriveSheetSave_performance(mapParam);
//                SpringContextHolder.getBean(SacTestDriveSheetService.class).sacTestDriveSheetSave_performance(mapParam);
//                return ResultHandler.updateOk(mapParam);
//            });
            // 异步调用方法ABC，ABC中请求线索，判断线索是否存在，不存在的就创建
            // 然后反向修改试驾预约单和试驾单的信息线索等信息
            // 如果不行，就在试驾的时候生成好customerId和dlrClueOrderNo，放到map中，让csc去判断，如果有了就不要创建了
            supplementTestData(mapParam);
        } catch (Exception e) {
            log.error("sacTestDriveSheetAllSave_performance异常", e);
            throw e;
        }
        return ResultHandler.updateOk(mapParam);
    }

    private void supplementTestData(Map<String, Object> mapParam) {
        CompletableFuture.runAsync(() -> {
            logger.info("异步获取线索更新试驾单");
            // 异步查询线索或者创建线索
            sacAppointmentSheetService.checkCustomerClueNew(mapParam);

            // 下面开始更新试驾预约单，更新试乘试驾单
            // 1.通过appointmentId去更新预约试驾单的线索单号和客户ID
            Map<String, Object> updateAppointmentParam = new HashMap<>();
            updateAppointmentParam.put("appointmentId", mapParam.get("appointmentId").toString());
            updateAppointmentParam.put("dlrClueOrderNo", mapParam.get("dlrClueOrderNo"));
            updateAppointmentParam.put("customerId", mapParam.get("customerId"));
            updateAppointmentParam.put("testDriveSheetId", mapParam.get("testDriveSheetId"));
            updateAppointmentParam.put("dlrId", mapParam.get("dlrId"));
            updateAppointmentParam.put("token", mapParam.get("token"));
//            updateAppointmentParam.put("updateControlId", mapParam.get("updateControlId"));
            sacAppointmentSheetService.setOrderCode(mapParam);
            updateAppointmentParam.put("appointmentOrderNo", mapParam.get("appointmentOrderNo"));
            int i = sacAppointmentSheetMapper.updateSacAppointmentSheetSupplement(updateAppointmentParam);
            logger.info("更新预约单行数{}", i);
            // 2.通过appointmentId去更新试乘试驾单的DLR_CLUE_ORDER_NO和CUSTOMER_ID
            setOrderCode(updateAppointmentParam);
            int i1 = sacTestDriveSheetMapper.updateSacTestDriveSheet(updateAppointmentParam);
            logger.info("更新试驾单行数{}", i1);
        }, asyncTaskExecutor);
    }

    @Override
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<Map<String, Object>>();
        logger.info("sacTestDriveSheetSingleListApp原入参：{}", JSON.toJSONString(mapParam));
        // 去掉空字符串
        MapUtil.removeNullValue(mapParam.getParam());
        logger.info("sacTestDriveSheetSingleListApp去除空值入参：{}", JSON.toJSONString(mapParam));
        //validateParams(mapParam);
        try {
            Page<Map<String, Object>> page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize());
            Map<String, Object> maps = new HashMap<>();
            List<Map<String, Object>> objects = new ArrayList<>();
            List<Map<String, Object>> list = sacTestDriveSheetMapper.selectSacTestDriveSheetSingle(mapParam.getParam(), page);
            if ("external".equals(mapParam.getParam().get("systemSource")) && !StringHelper.IsEmptyOrNull(mapParam.getParam().get("customerPhone"))) {
                maps.put("singleList", list);

                mapParam.getParam().put("isLabels", '0');
                mapParam.getParam().put("phone", mapParam.getParam().get("customerPhone"));
                mapParam.getParam().put("pageIndex", mapParam.getPageIndex());
                mapParam.getParam().put("pageSize", mapParam.getPageSize());

                List<Map<String, Object>> list1 = sacTestDriveTaskMapper.querySacTestDriveTask(page, mapParam.getParam());
                maps.put("taskList", list1);
                objects.add(maps);
                page.setRecords(objects);
                return BusicenUtils.page2ListResult(page);
                /* list.addAll(list1);*/
            }
            /*maps.put("taskList",list1);*/
            /*list = sacTestDriveTaskMapper.querySacTestDriveTask(page, mapParam.getParam());*/
			/*maps.add(list);
			maps.add(list1);*/

            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("sacTestDriveSheetSingleListApp异常", e);
            throw e;
        }
        return result;
    }

    @Override
    public ListResult<Map<String, Object>> sacTestDriveSheetSingleListApp_p(ParamPage<Map<String, Object>> mapParam) {
        ListResult<Map<String, Object>> result = new ListResult<>();
        // 去掉空字符串
        MapUtil.removeNullValue(mapParam.getParam());
        validateParams(mapParam);

        try {
            Page<Map<String, Object>> page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize());
            Map<String, Object> maps = new HashMap<>();
            List<Map<String, Object>> objects = new ArrayList<>();

            // 判断是否需要特殊处理
            boolean isExternal = "external".equals(mapParam.getParam().get("systemSource"))
                    && !StringHelper.IsEmptyOrNull(mapParam.getParam().get("customerPhone"));
            if (isExternal) {
                page = new Page<>(mapParam.getPageIndex(), mapParam.getPageSize(), false);
            }

            // 1 部分：异步查询 sacTestDriveSheetSingle 和 sacTestDriveSheetSingleCountP
            Page<Map<String, Object>> finalPage = page;
            CompletableFuture<List<Map<String, Object>>> futureList = CompletableFuture.supplyAsync(() ->
                    sacTestDriveSheetMapper.selectSacTestDriveSheetSingle(mapParam.getParam(), finalPage)
            ,asyncTaskExecutor);

//            CompletableFuture<Long> futureCount = CompletableFuture.supplyAsync(() ->
//                    sacTestDriveSheetMapper.selectSacTestDriveSheetSingleCountP(mapParam.getParam())
//                    ,asyncTaskExecutor);

            // 3 部分：异步查询 sacTestDriveTaskMapper.querySacTestDriveTask（如果需要）
            CompletableFuture<List<Map<String, Object>>> futureTaskList = isExternal ?
                    CompletableFuture.supplyAsync(() -> {
                        mapParam.getParam().put("isLabels", '0');
                        mapParam.getParam().put("phone", mapParam.getParam().get("customerPhone"));
                        mapParam.getParam().put("pageIndex", mapParam.getPageIndex());
                        mapParam.getParam().put("pageSize", mapParam.getPageSize());
                        return sacTestDriveTaskMapper.querySacTestDriveTask(finalPage, mapParam.getParam());
                    },asyncTaskExecutor) : CompletableFuture.completedFuture(null);

            // 等待所有异步任务完成
//            CompletableFuture.allOf(futureList, futureCount, futureTaskList).join();
            CompletableFuture.allOf(futureList, futureTaskList).join();

            // 获取异步任务的结果
            List<Map<String, Object>> list = futureList.get();
//            Long counts = futureCount.get();
            List<Map<String, Object>> taskList = futureTaskList.get();

            // 2 部分：处理 list
//            list = handleTestDriveSheetSingleList(list);

            // 如果是外部系统，组装结果
            if (isExternal) {
                maps.put("singleList", list);
                maps.put("taskList", taskList);
                objects.add(maps);
                page.setRecords(objects);
//                page.setTotal(counts);
                return BusicenUtils.page2ListResult(page);
            }

            // 如果不是外部系统，直接返回 list
            page.setRecords(list);
            result = BusicenUtils.page2ListResult(page);
        } catch (Exception e) {
            log.error("sacTestDriveSheetSingleListApp异常", e);
//            throw e;
        }
        return result;
    }

    /**
     * 校验入参是否为空
     * @param mapParam
     */
    private void validateParams(ParamPage<Map<String, Object>> mapParam) {
        Map<String, Object> params = mapParam.getParam();
        boolean isEmptyRequest = params.isEmpty()
                || (params.size() == 1 && params.containsKey("token"));

        if (isEmptyRequest) {
            throw BusicenException.create("请求参数不能为空");
        }
    }

    /**
     * 参数为空 或 参数只有token时预填充数据
     * @param mapParam
     */
    private void ifNullInitParam(ParamPage<Map<String, Object>> mapParam) {
        if (mapParam.getParam().isEmpty()
                || (mapParam.getParam().size() == 1 && mapParam.getParam().containsKey("token"))) {
            LocalDate today = LocalDate.now();  // 假设今天是 2024-03-21

            // 获取7天前的零点
            LocalDateTime sevenDaysAgo = today
                    .minusDays(6)    // 减6天（因为包含今天）
                    .atStartOfDay(); // 设置为零点
            // 结果：2024-03-15 00:00:00

            // 获取明天的零点
            LocalDateTime tomorrowStart = today
                    .plusDays(1)     // 加1天
                    .atStartOfDay(); // 设置为零点
            // 结果：2024-03-22 00:00:00

            mapParam.getParam().put("createdDateBeg", sevenDaysAgo);
            mapParam.getParam().put("createdDateEnd", tomorrowStart);
        }
    }

    /**
     * 清空指定字段值但保留key
     * @param list 需要处理的数据列表
     * @return 处理后的列表
     */
    public List<Map<String, Object>> clearSensitiveInfo(List<Map<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        // 需要清空的字段
        Set<String> sensitiveFields = new HashSet<>();
        sensitiveFields.add("customerPhone");
        sensitiveFields.add("customerIdNumberAgreement");
        sensitiveFields.add("customerSignatureAgreement");
        sensitiveFields.add("otherAgreement");
        sensitiveFields.add("testDriveAgreement");
        sensitiveFields.add("customerName");
        sensitiveFields.add("custName");
        sensitiveFields.add("phone");

        list.forEach(map -> {
            // 只处理已存在的key
            sensitiveFields.forEach(field -> {
                if (map.containsKey(field)) {
                    map.put(field, "");
                }
            });
        });

        return list;
    }

    public void sendMessage(Map<String, Object> mapParam) {
        logger.info("sendMessage002" + JSON.toJSONString(mapParam));
        try {
            if (mapParam.containsKey("isSendMessage") && "1".equals(mapParam.get("isSendMessage"))
                    && "2".equals(mapParam.get("testDriveMethod"))) {
                Map<String, Object> paramM = new HashMap<>();
                Map<String, Object> param = new HashMap<>();
                param.put("time", mapParam.get("appointmentTestDate") + " " + mapParam.get("appointmentTestTime"));
                // param.put("time", mapParam.get("expectStartDate"));
                param.put("dlrShortName", mapParam.get("dlrName"));
                paramM.put("recNum", new String[]{mapParam.get("customerPhone").toString()});
                paramM.put("smsParam", param);
                paramM.put("smsTemplateId", "adp002");//模板id
                accPushFeignService.smsSendMessage(paramM);
            }
        } catch (Exception e) {
            log.error(String.format("appointmentSheetSave试驾预约发送短信异常%s", mapParam.get("customerPhone")), e);
        }
    }

    @Override
    public OptResult sacTestDriveReplenishExport(Map<String, Object> dataInfo, String token, HttpServletResponse response) {
        try {
            String title = "补录试乘试驾单列表查询导出";
            String[][] columns = new String[][]{
                    {"customerName", "客户姓名"},
                    {"customerPhoneTm", "客户电话"},
                    {"customerSexName", "性别"},
                    {"clueDlrName", "线索所属门店"},
                    {"cityName", "城市"},
                    {"dlrName", "试乘试驾门店"},
                    {"salesConsultantName", "试驾执行产品专家"},
                    {"testTypeName", "类型"},
                    {"smallCarTypeName", "试乘试驾车型"},
                    {"plateNumber", "试乘试驾车牌"},
                    {"testDriveTimePJ", "试乘试驾预约时间"},
                    {"testStatusName", "试驾状态"},
                    {"startTime", "试驾开始试驾"},
                    {"endTime", "试驾结束试驾"},
                    {"carVin", "VIN"},
                    {"testRoadHaul", "试驾里程"},
            };
            ParamPage<Map<String, Object>> paramPage = new ParamPage<>();
            paramPage.setPageIndex(1);
            paramPage.setPageSize(-1);
            paramPage.setParam(dataInfo);
            paramPage.getParam().put("token", token);
            List<Map<String, Object>> transferRecordsList = this.sacTestDriveSheetSingleList(paramPage).getRows();
            ExcelExportUtil.exportExcel(title, columns, transferRecordsList, response);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("sacTestDriveSheetSingleListExport异常；错误信息{" + e.toString() + "}");
            throw e;
        }
        return null;
    }


    public static String postData(String url, HttpEntity<Object> param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, param, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }

    public static class Rsp {
        String code;

        String msg;

        JSON data;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public JSON getData() {
            return data;
        }

        public void setData(JSON data) {
            this.data = data;
        }
    }

    //  @Async
    public void sendTda(Map<String, Object> param, HashMap<String, Object> driveEndMap) {

        // HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(param);
        HashMap<String, Object> driveMap = Maps.newHashMap();
        driveMap.put("drive_id", driveEndMap.get("testDriveOrderNo"));
        driveMap.put("user_id", driveEndMap.get("empCode"));

        long receptionEd = LocalDateTime.now().plusMinutes(30).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driveMap.put("reception_ed", receptionEd);
        Object receiverTime = driveEndMap.get("receiverTime");

        if (!StringHelper.IsEmptyOrNull(receiverTime)) {
            LocalDateTime parse = LocalDateTime.parse(receiverTime.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).minusMinutes(30);
            long receptionBg = parse.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            driveMap.put("reception_bg", receptionBg);
        }

        HashMap<String, Object> driver = Maps.newHashMap();
        long driveEd = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driver.put("drive_ed", driveEd);
        long driveBg = LocalDateTime.parse(driveEndMap.get("startTime").toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .toInstant(ZoneOffset.of("+8")).toEpochMilli();
        driver.put("drive_bg", driveBg);
        driver.put("drive_car", driveEndMap.get("smallCarTypeCode"));
        List<Object> list = Lists.newArrayList();
        driver.put("drive_route", list);
        driveMap.put("drive_info", driver);
        HashMap<String, Object> clientInfo = Maps.newHashMap();
        clientInfo.put("client_name", driveEndMap.get("customerName"));
        clientInfo.put("client_phone", driveEndMap.get("customerPhone"));
        clientInfo.put("client_id", driveEndMap.get("customerId"));
        driveMap.put("client_info", clientInfo);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json; charset=UTF-8");
        HttpEntity<Object> entity = new HttpEntity<>(JSONObject.toJSONString(driveMap), httpHeaders);
        Map<String, Object> urlMap = sacTestDriveSheetMapper.findUrl();
        if ("1".equals(urlMap.get("isEnable"))) {
            logger.info("发送tdatdata入参{}", entity);
            try {
                String postData = postData(urlMap.get("url").toString(), entity);
                logger.info("发送tdatda返回{}", postData);
                Rsp rspData = JSONObject.parseObject(postData, Rsp.class);
                if (!"0".equals(rspData.getCode())) {
                    logger.info("发送tda失败{}", postData);
                    throw new BusicenException(rspData.getMsg());

                }
            } catch (Exception e) {
                logger.info("发送tda异常{}", e);
            }
        }
    }


    // @Async
    public void sendZtMQ(Map<String, Object> param, HashMap<String, Object> driveEndMap) {

        // HashMap<String, Object> driveEndMap = sacTestDriveSheetMapper.findSheet(param);
        // HashMap<String, Object> driveMap = Maps.newHashMap();
        driveEndMap.put("endTime", param.get("endTime"));
        logger.info("试驾结束发送ZTMQ{}", driveEndMap);
        AdpMsUtil.sendMq("adp_ms_base_driveEnd", driveEndMap);
    }

    /**
     * 试驾转移产品专家查询
     *
     * @param productSpecia
     * @return
     */
    @Override
    public ListResult<ProductSpecialistVO> findProductSpecialist(ProductSpecialistDTO productSpecia) {

        List<ProductSpecialistVO> productSpecialist = sacTestDriveSheetMapper.findProductSpecialist(productSpecia);
        ListResult<ProductSpecialistVO> result = new ListResult<>();
        result.setMsg("查询成功");
        result.setRows(productSpecialist);
        result.setResult("1");
        return result;
    }

    /**
     * 试驾转移
     *
     * @param testDriveTransferDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OptResult testDriveTransfer(TestDriveTransferDTO testDriveTransferDTO) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String token = request.getHeader(HEADER_TOKEN);
        UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
        String empName = userBusiEntity.getEmpName();

        int update = sacTestDriveSheetMapper.testDriveTransfer(testDriveTransferDTO, empName);
        if (update <= 0) {
            throw new BusicenException("试驾转移失败");

        }
        OptResult optResult = new OptResult();
        optResult.setResult("1");
        optResult.setMsg("试驾转移成功");
        return optResult;
    }

    @Override
    public EntityResult<VehicleDataVO> queryVehicleData(String testDriveOrderNo) {
        return vehicleDataService.queryVehicleData(testDriveOrderNo);
    }


    @Override
    public ListResult<GetBiVehicleDataResultVO> getBiVehicleData(GetBiVehicleDataJobReq req, String token) {
        String testDriveOrderNo = req.getTestDriveOrderNo();
        if (org.apache.commons.lang3.StringUtils.isBlank(testDriveOrderNo)) {
            return ListResultBuilder.create().result("0").msg("单号为空").build();
        }
        return vehicleDataService.getBiVehicleData(req, token);
    }

    /**
     * 根据条件和字段查询试驾表
     *
     * @param entity
     * @param selectField
     * @return
     */
    @Override
    public List<SacTestDriveSheet> queryTestDrivSheet(SacTestDriveSheet entity,
                                                   SFunction<SacTestDriveSheet, Object>... selectField) {
        LambdaQueryWrapper<SacTestDriveSheet> queryWrapper = entity.buildQueryWrapper(selectField);
        if (ObjectUtil.isNotEmpty(selectField)) {
            queryWrapper.select(selectField);
        }
        return list(queryWrapper);
    }
}
