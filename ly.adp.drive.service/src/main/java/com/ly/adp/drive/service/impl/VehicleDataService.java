package com.ly.adp.drive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.adp.common.entity.enums.YesOrNoEnum;
import com.ly.adp.drive.entities.BiDataResponse;
import com.ly.adp.drive.entities.SacTestDriveSheet;
import com.ly.adp.drive.entities.TestDriveVehicleData;
import com.ly.adp.drive.entities.constants.CommonConstant;
import com.ly.adp.drive.entities.constants.IsEnable;
import com.ly.adp.drive.entities.enums.CustEventEnum;
import com.ly.adp.drive.entities.enums.IsValidTestDriveEnum;
import com.ly.adp.drive.entities.out.CustEventFlowOut;
import com.ly.adp.drive.entities.req.GetBiVehicleDataJobReq;
import com.ly.adp.drive.entities.vo.GetBiVehicleDataResultVO;
import com.ly.adp.drive.entities.vo.VehicleDataVO;
import com.ly.adp.drive.idal.mapper.TestDriveVehicleDataMapper;
import com.ly.adp.drive.otherservice.AdpMsUtil;
import com.ly.adp.drive.service.ISacTestDriveSheetService;
import com.ly.adp.drive.service.IVehicleDataService;
import com.ly.adp.drive.util.EntityNullConverter;
import com.ly.mp.busi.base.handler.EntityResultBuilder;
import com.ly.mp.busi.base.handler.ListResultBuilder;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Version 1.0.0
 **/
@Service
public class VehicleDataService implements IVehicleDataService {

    private Logger logger = LoggerFactory.getLogger(VehicleDataService.class);

    /**
     * 查询三次未获取到数据提示
     */
    private final String QUERY_ERR_NOTICE = "车辆数据查询失败\n请联系smart总部";

    /**
     * 查询未获取到数据，但在三次内 提示
     */
    private final String QUERY_EMPTY_NOTICE = "车辆数据处理中\n请晚点再来";

    /**
     * 查询未获取到数据，但在三次内 编码
     */
    private final String QUERY_EMPTY_CODE = "0";

    /**
     * 查询三次未获取到数据 编码
     */
    private final String QUERY_ERR_CODE = "3";

    /**
     * 计算中限制次数
     */
    public static final int CALCULATE_LIMIT = 5;

    @Value("${refer.bi.url}")
    private String biUrl;

    @Value("${refer.bi.retryTime}")
    private int biRetryTime;

    @Value("${refere.bi.timeOut}")
    private int biTimeOut;

    @Autowired
    private ISacTestDriveSheetService testDriveSheetService;

    @Resource
    private TestDriveVehicleDataMapper testDriveVehicleDataMapper;

    @Autowired
    Executor asyncTaskExecutor;

    @Override
    @Transactional
    public ListResult<GetBiVehicleDataResultVO> getBiVehicleData(GetBiVehicleDataJobReq req, String token) {
        String uid = UUID.fastUUID().toString();
        try {
            // 获取并处理试驾单号
            List<String> processedOrders = processTestDriveOrders(req.getTestDriveOrderNo());

            logger.info("[{}] getBiVehicleData开始 - 试驾单数量: {}, 是否手动: {}",
                    uid, processedOrders.size(), req.isManual());
            logger.info("[{}] 请求参数详情: {}", uid, processedOrders);

            List<BiDataResponse.TestDriveBiData> vehicleDataList = getBiData(String.join(",", processedOrders), uid);

            // 车机数据处理
            List<GetBiVehicleDataResultVO> resultList = handleDriveBiDataList(vehicleDataList, req.isManual(), processedOrders, token);

            return ListResultBuilder.creatOk().records(Long.valueOf(resultList.size())).rows(resultList).build();
        } catch (Exception e) {
            logger.error("[{}] getBiVehicleData异常: {}", uid, e.getMessage(), e);
            return ListResultBuilder.create().result("0").msg(e.getMessage()).build();
        }
    }

    /**
     * 处理试驾单号：去除空格/换行，过滤空值，返回有效单号列表
     *
     * @param orderStr 原始试驾单号字符串（逗号分隔）
     * @return 处理后的有效单号列表
     */
    private List<String> processTestDriveOrders(String orderStr) {
        return Arrays.stream(orderStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 处理获取到的车机数据
     * @param testDriveBiDataList bi数据
     * @param manual 是否手动
     * @param reqList 入参试驾单号
     * @param token
     * @return
     */
    public List<GetBiVehicleDataResultVO> handleDriveBiDataList(List<BiDataResponse.TestDriveBiData> testDriveBiDataList, boolean manual, List<String> reqList, String token) {
        // 获取可用的 BiData
        List<BiDataResponse.TestDriveBiData> canUseBiList = getCanUseBiList(testDriveBiDataList, reqList);

        // 获取数据库中已有的数据
        List<TestDriveVehicleData> doList = getVehicleDataList(reqList);

        // 处理结果
        List<GetBiVehicleDataResultVO> resultVOList = handleResultVO(reqList, canUseBiList, doList);

        // 处理数据库操作
        handleDbOperation(canUseBiList, doList, resultVOList, token, manual);

        return resultVOList;
    }

    /**
     * 从 testDriveBiDataList 中获取可用的 BiData
     *
     * @param testDriveBiDataList 原始的 BiData 列表
     * @param reqList            请求中的试驾单号列表
     * @return 可用的 BiData 列表
     */
    private List<BiDataResponse.TestDriveBiData> getCanUseBiList(List<BiDataResponse.TestDriveBiData> testDriveBiDataList, List<String> reqList) {
        return testDriveBiDataList.stream()
                .filter(data -> reqList.contains(data.getTestDriveOrderNo()))
                .collect(Collectors.toList());
    }

    /**
     * 处理结果 VO
     *
     * @param reqList      请求中的试驾单号列表
     * @param canUseBiList 可用的 BiData 列表
     * @param doList       数据库中已有的数据
     * @return 结果 VO 列表
     */
    private List<GetBiVehicleDataResultVO> handleResultVO(List<String> reqList, List<BiDataResponse.TestDriveBiData> canUseBiList, List<TestDriveVehicleData> doList) {
        return reqList.stream()
                .map(orderNo -> {
                    GetBiVehicleDataResultVO resultVO = new GetBiVehicleDataResultVO();
                    resultVO.setTestDriveOrderNo(orderNo);

                    Optional<BiDataResponse.TestDriveBiData> driveBiData = canUseBiList.stream()
                            .filter(data -> data.getTestDriveOrderNo().equals(orderNo))
                            .findFirst();
                    GetBiVehicleDataResultVO.Msg msg = driveBiData.isPresent()
                            ? GetBiVehicleDataResultVO.Msg.SUCCESS
                            : GetBiVehicleDataResultVO.Msg.FAIL;
                    resultVO.setCode(msg.getCode());
                    resultVO.setMsg(msg.getDescription());
                    return resultVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新 TestDriveVehicleData 对象
     *
     * @param canUseBiList          可用的 BiData 列表
     * @param doDriveVehicleData    数据库中已有的 TestDriveVehicleData 对象
     * @param vo                    结果 VO
     * @param token                 操作人 token
     * @param manual                是否手动操作
     */
    private void updateDriveVehicleData(List<BiDataResponse.TestDriveBiData> canUseBiList, TestDriveVehicleData doDriveVehicleData, GetBiVehicleDataResultVO vo, String token, boolean manual) {
        Optional<BiDataResponse.TestDriveBiData> testDriveBiDataOpt = canUseBiList.stream()
                .filter(d -> d.getTestDriveOrderNo().equals(doDriveVehicleData.getTestDriveOrderNo()))
                .findFirst();

        doDriveVehicleData.setCommonFields(false, manual, token);

        if (testDriveBiDataOpt.isPresent()) {
            BiDataResponse.TestDriveBiData testDriveBiData = testDriveBiDataOpt.get();
            updateDriveVehicleDataFields(doDriveVehicleData, testDriveBiData);
        } else if (doDriveVehicleData.getQueryBiTimes() >= CALCULATE_LIMIT) {
            // 最后一轮job未获取到bi车机数据时，设为无效试驾
            doDriveVehicleData.setIsValidTestDrive(IsValidTestDriveEnum.INVALID.getCode());
        }
    }

    /**
     * 更新 TestDriveVehicleData 对象的字段
     *
     * @param doDriveVehicleData 数据库中已有的 TestDriveVehicleData 对象
     * @param testDriveBiData    可用的 BiData 对象
     */
    private void updateDriveVehicleDataFields(TestDriveVehicleData doDriveVehicleData, BiDataResponse.TestDriveBiData testDriveBiData) {
        doDriveVehicleData.setIsValidTestDrive(transIsValidTestDrive(testDriveBiData.getIsValidTestDriveCode()));
        doDriveVehicleData.setVehStartVoc(testDriveBiData.getVehStartVoc());
        doDriveVehicleData.setVehStartMileage(testDriveBiData.getVehStartMileage());
        doDriveVehicleData.setVehEndMileage(testDriveBiData.getVehEndMileage());
        doDriveVehicleData.setVehTotalMileage(testDriveBiData.getVehTotalMileage());
        doDriveVehicleData.setVehTotalDur(testDriveBiData.getVehTotalDur());
        doDriveVehicleData.setVehMaxSpeed(testDriveBiData.getVehMaxSpeed());
        doDriveVehicleData.setTestDriveAvgSpeed(testDriveBiData.getTestDriveAvgSpeed());
        doDriveVehicleData.setIsGoodTestDrive(testDriveBiData.getIsGoodTestDrive());
        doDriveVehicleData.setIsMatchRecord(testDriveBiData.getIsMatchRecord());
        doDriveVehicleData.setRecordTime(testDriveBiData.getRecordTime());
        doDriveVehicleData.setRecordDuration(testDriveBiData.getRecordDuration());
        doDriveVehicleData.setRecordScore(testDriveBiData.getRecordScore());
    }

    /**
     * bi 有效试驾定义转换为adp有效试驾定义编码
     *
     * BI 定义：1:有效试驾 2:无效试驾
     * adp 定义 是否有效试驾 1-是，0-否
     * @param isValidTestDriveCode
     * @return
     */
    public int transIsValidTestDrive(int isValidTestDriveCode) {
        return  isValidTestDriveCode == 1 ? 1 : 0;
    }

    /**
     * 创建一个禁用状态的 TestDriveVehicleData 对象
     *
     * @param testDriveOrderNo 试驾单号
     * @param token            操作人 token
     * @param manual           是否手动操作
     * @return 禁用状态的 TestDriveVehicleData 对象
     */
    private TestDriveVehicleData createDisabledDriveVehicleData(String testDriveOrderNo, String token, boolean manual) {
        TestDriveVehicleData driveVehicleData = new TestDriveVehicleData();
        driveVehicleData.saveInsertDO(token, manual, IsEnable.DISABLE);
        driveVehicleData.setTestDriveOrderNo(testDriveOrderNo);
        return driveVehicleData;
    }

    /**
     * 创建一个启用状态的 TestDriveVehicleData 对象
     *
     * @param testDriveOrderNo 试驾单号
     * @param token            操作人 token
     * @param manual           是否手动操作
     * @return 启用状态的 TestDriveVehicleData 对象
     */
    private TestDriveVehicleData createEnabledDriveVehicleData(String testDriveOrderNo, String token, boolean manual) {
        TestDriveVehicleData driveVehicleData = new TestDriveVehicleData();
        driveVehicleData.saveInsertDO(token, manual, IsEnable.ENABLE);
        driveVehicleData.setTestDriveOrderNo(testDriveOrderNo);
        return driveVehicleData;
    }

    /**
     * 处理数据库操作
     *
     * @param canUseBiList   可用的 BiData 列表
     * @param doList         数据库中已有的数据
     * @param resultVOList   结果 VO 列表
     * @param token          操作人 token
     * @param manual         是否手动操作
     */
    private void handleDbOperation(List<BiDataResponse.TestDriveBiData> canUseBiList, List<TestDriveVehicleData> doList, List<GetBiVehicleDataResultVO> resultVOList, String token, boolean manual) {
        List<TestDriveVehicleData> insertList = new ArrayList<>();
        List<TestDriveVehicleData> updateList = new ArrayList<>();
        List<TestDriveVehicleData> allList = new ArrayList<>();

        for (GetBiVehicleDataResultVO vo : resultVOList) {
            Optional<TestDriveVehicleData> doDriveVehicleDataOpt = doList.stream()
                    .filter(d -> d.getTestDriveOrderNo().equals(vo.getTestDriveOrderNo()))
                    .findFirst();

            boolean isFail = vo.getCode().equals(GetBiVehicleDataResultVO.Msg.FAIL.getCode());
            int currentQueryTimes = 0;

            if (doDriveVehicleDataOpt.isPresent()) {
                // 数据库中已存在该试驾单的数据
                TestDriveVehicleData doDriveVehicleData = doDriveVehicleDataOpt.get();
                updateDriveVehicleData(canUseBiList, doDriveVehicleData, vo, token, manual);
                updateList.add(doDriveVehicleData);
                allList.add(doDriveVehicleData);
                currentQueryTimes = doDriveVehicleData.getQueryBiTimes();
            } else if (isFail) {
                // 从 BI 未获取到数据时，记录试驾单查询记录
                TestDriveVehicleData driveVehicleData = createEnabledDriveVehicleData(vo.getTestDriveOrderNo(), token, manual);
                insertList.add(driveVehicleData);
                currentQueryTimes = driveVehicleData.getQueryBiTimes();
            } else {
                // 数据库中不存在该试驾单的数据，且查到了 BI 数据，说明第一次查到 BI 数据，直接插入车机数据成功
                TestDriveVehicleData driveVehicleData = createEnabledDriveVehicleData(vo.getTestDriveOrderNo(), token, manual);
                Optional<BiDataResponse.TestDriveBiData> testDriveBiDataOpt = canUseBiList.stream()
                        .filter(d -> d.getTestDriveOrderNo().equals(vo.getTestDriveOrderNo()))
                        .findFirst();
                updateDriveVehicleDataFields(driveVehicleData, testDriveBiDataOpt.get());
                insertList.add(driveVehicleData);
                allList.add(driveVehicleData);
                currentQueryTimes = driveVehicleData.getQueryBiTimes();
            }

            vo.setQueryBiTimes(currentQueryTimes);
        }
        handleUpdate(updateList);
        handleInset(insertList);
        CompletableFuture.runAsync(() -> {
            handlePushTestDriveEventFlow(allList);
        }, asyncTaskExecutor);
    }
    
    /**
     * 车机数据更新
     *
     * @param updateList
     */
    private void handleUpdate(List<TestDriveVehicleData> updateList) {
        if (CollectionUtil.isEmpty(updateList)) {
            return;
        }
        // 更新数据库
        testDriveVehicleDataMapper.batchUpdate(updateList);
    }

    private void handlePushTestDriveEventFlow(List<TestDriveVehicleData> dlList) {
        if (CollectionUtil.isEmpty(dlList)) {
            return;
        }
        // 获取属于有效试驾的对象集
        List<TestDriveVehicleData> validList = dlList.stream()
                .filter(data -> data.getIsValidTestDrive() == 1)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(validList)) {
            return;
        }
        List<String> listTestDrivOrderNo = validList.stream()
                .filter(person -> StringUtils.isNotEmpty(person.getTestDriveOrderNo()))
                .map(TestDriveVehicleData::getTestDriveOrderNo).collect(Collectors.toList());
        SacTestDriveSheet testDriveSheet = new SacTestDriveSheet();
        testDriveSheet.setTestDriveOrderNoList(listTestDrivOrderNo);
        List<SacTestDriveSheet> sacTestDriveSheets = testDriveSheetService.queryTestDrivSheet(testDriveSheet, SacTestDriveSheet::getCustomerId,
                SacTestDriveSheet::getTestDriveOrderNo, SacTestDriveSheet::getStartTime);
        if (CollectionUtil.isEmpty(sacTestDriveSheets)) {
            return;
        }
        Map<String, SacTestDriveSheet> testDriveSheetMap = sacTestDriveSheets.stream().collect(Collectors.toMap(
                SacTestDriveSheet::getTestDriveOrderNo,
                Function.identity()));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        validList.forEach(item -> {
            CustEventFlowOut eventFlowOut = new CustEventFlowOut();
            eventFlowOut.setBusinessId(item.getTestDriveOrderNo());
            eventFlowOut.setCustId(testDriveSheetMap.get(item.getTestDriveOrderNo()).getCustomerId());
            String startTime = Optional.ofNullable(item.getTestDriveOrderNo())
                                       .map(testDriveSheetMap::get)
                                       .map(SacTestDriveSheet::getStartTime)
                                       .orElse(null);
            if (Objects.nonNull(startTime)) {
                eventFlowOut.setEventTime(startTime);
            } else {
                eventFlowOut.setEventTime(LocalDateTime.now());
            }
            eventFlowOut.setType(CustEventEnum.DRIVE.getCode());
            logger.info("用户旅程有效试驾: {}", com.alibaba.fastjson.JSONObject.toJSONString(eventFlowOut));
            AdpMsUtil.sendMq("adp_ms_eventFlow", eventFlowOut);
        });
    }

    /**
     * 获取单号列表，如果获取不到bi数据，就使用入参单号
     * @param canUseBiList
     * @param resultVOList
     * @return
     */
    private List<String> getDriveOrderNoList(List<BiDataResponse.TestDriveBiData> canUseBiList, List<GetBiVehicleDataResultVO> resultVOList) {
        return CollectionUtil.isEmpty(canUseBiList) ?
                resultVOList.stream().map(d -> d.getTestDriveOrderNo()).collect(Collectors.toList())
                : canUseBiList.stream().map(d -> d.getTestDriveOrderNo()).collect(Collectors.toList());
    }

    /**
     * 车机数据保存
     * @param insertList
     */
    private void handleInset(List<TestDriveVehicleData> insertList) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }
        testDriveVehicleDataMapper.batchInsert(insertList);
    }

    private List<TestDriveVehicleData> getVehicleDataList(List<String> driveOrderNoList) {
        LambdaQueryWrapper<TestDriveVehicleData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TestDriveVehicleData::getTestDriveOrderNo, driveOrderNoList);
        return testDriveVehicleDataMapper.selectList(queryWrapper);
    }

    /**
     * 获取bi车机数据
     *
     * @param testDriveOrderNo 试驾单号
     * @param uid 请求标识
     * @return 返回结果
     */
    private List<BiDataResponse.TestDriveBiData> getBiData(String testDriveOrderNo, String uid) {
        List<BiDataResponse.TestDriveBiData> emptyList = Lists.newArrayList();

        logger.info("[{}] 开始请求BI接口获取车机数据", uid);

        try {

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("test_drive_order_no", testDriveOrderNo);

            HttpResponse response = HttpRequest.post(biUrl)
                    .body(JSONUtil.toJsonStr(requestBody))
                    .contentType("application/json")
                    .timeout(biTimeOut)
                    .execute();

            logger.info("{}-请求bi接口获取车机数据, 结果:{}", uid, response);

            if (HttpStatus.HTTP_OK == response.getStatus()) {
                BiDataResponse biDataResponse = JSONUtil.toBean(response.body(), BiDataResponse.class);
                if (biDataResponse == null) {
                    logger.error("{}-反序列化BiDataResponse失败，body:{}", uid, response.body());
                    return emptyList;
                }
                List<BiDataResponse.TestDriveBiData> vehicleDataList = biDataResponse.getData();
                logger.info("{}-成功获取bi车机数据，数据条数:{}", uid, vehicleDataList != null ? vehicleDataList.size() : 0);
                return CollectionUtil.isEmpty(vehicleDataList) ? emptyList : vehicleDataList;
            } else {
                logger.error("{}-获取bi车机数据失败，httpStatus:{}, body:{}", uid, response.getStatus(), response.body());
                return emptyList;
            }
        } catch (Exception e) {
            logger.error("{}-获取bi车机数据异常: {}", uid, e.getMessage(), e);
            return emptyList;
        }
    }

    @Override
    public EntityResult<VehicleDataVO> queryVehicleData(String testDriveOrderNo) {
        LambdaQueryWrapper<TestDriveVehicleData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TestDriveVehicleData::getTestDriveOrderNo, testDriveOrderNo);
        TestDriveVehicleData driveVehicleData = testDriveVehicleDataMapper.selectOne(queryWrapper);
        return handleQueryResult(driveVehicleData);
    }

    private EntityResult<VehicleDataVO> handleQueryResult(TestDriveVehicleData driveVehicleData) {
        if (Objects.isNull(driveVehicleData)) {
            return buildQueryEmptyResult();
        }
        // 是否可用
        boolean isEnable = driveVehicleData.getIsEnable() == IsEnable.ENABLE;
        // 是否已查询三次
        boolean isQueryLimit = driveVehicleData.getQueryBiTimes() >= 3;
        if (isEnable) {
            return buildQuerySuccessResult(driveVehicleData);
        } else if (isQueryLimit) {
            // 若三次查询未查询到数据 默认无效试驾 2024-11-06
            // return buildQueryErresult();
            EntityNullConverter.convertNullToEmpty(driveVehicleData);
            return buildQuerySuccessResult(driveVehicleData);
        } else {
            return buildQueryEmptyResult();
        }
    }

    public EntityResult<VehicleDataVO> buildQuerySuccessResult(TestDriveVehicleData driveVehicleData) {
        VehicleDataVO vehicleDataVO = new VehicleDataVO();
        BeanUtils.copyProperties(driveVehicleData, vehicleDataVO);
        vehicleDataVO.setIsValidTestDriveCn(IsValidTestDriveEnum.fromCode(vehicleDataVO.getIsValidTestDrive()).getDescription());
        return EntityResultBuilder.<VehicleDataVO>creatOk().rows(vehicleDataVO).build();
    }

    public EntityResult<VehicleDataVO> buildQueryEmptyResult() {
        EntityResult<VehicleDataVO> entityResult = new EntityResult();
        entityResult.setResult(CommonConstant.FAIL_CODE);
        entityResult.setMsg(QUERY_EMPTY_NOTICE);
        entityResult.setExtInfo(QUERY_EMPTY_CODE);
        entityResult.setRows(null);
        return entityResult;
    }

    public EntityResult<VehicleDataVO> buildQueryErresult() {
        EntityResult<VehicleDataVO> entityResult = new EntityResult();
        entityResult.setResult(CommonConstant.FAIL_CODE);
        entityResult.setMsg(QUERY_ERR_NOTICE);
        entityResult.setExtInfo(QUERY_ERR_CODE);
        entityResult.setRows(null);
        return entityResult;
    }

    public List<VehicleDataVO> queryVehicleDataList(List<String> testDriveOrderNoList) {
        LambdaQueryWrapper<TestDriveVehicleData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TestDriveVehicleData::getTestDriveOrderNo, testDriveOrderNoList);
        List<TestDriveVehicleData> testDriveVehicleData = testDriveVehicleDataMapper.selectList(queryWrapper);
        return doListToVoList(testDriveVehicleData);
    }

    private List<VehicleDataVO> doListToVoList(List<TestDriveVehicleData> testDriveVehicleData) {
        return testDriveVehicleData.stream()
                .map(this::convertToVehicleDataVO)
                .collect(Collectors.toList());
    }

    @Override
    public VehicleDataVO convertToVehicleDataVO(TestDriveVehicleData data) {
        if (IsValidTestDriveEnum.CALCULATING.getCode() == data.getIsValidTestDrive()) {
            return VehicleDataVO.getDefaultInstance(data.getTestDriveOrderNo());
        }

        // 复制基础属性
        VehicleDataVO vehicleDataVO = data.entityToVO();

        // 查询次数超限时转换空值
        if (data.getQueryBiTimes() >= CALCULATE_LIMIT) {
            vehicleDataVO = EntityNullConverter.convertNullToEmpty(vehicleDataVO);
        }

        // 设置试驾有效性描述
        vehicleDataVO.setIsValidTestDriveCn(
                IsValidTestDriveEnum.fromCode(data.getIsValidTestDrive()).getDescription()
        );
        vehicleDataVO.setIsGoodTestDriveCn(YesOrNoEnum.getYesOrNoDescByType(data.getIsGoodTestDrive()));
        vehicleDataVO.setIsMatchRecordCn(YesOrNoEnum.getYesOrNoDescByType(data.getIsMatchRecord()));

        return vehicleDataVO;
    }

    @Override
    public Map<String, VehicleDataVO> queryVehicleDataMap(List<String> testDriveOrderNoList) {
        List<VehicleDataVO> vehicleDataVOList = queryVehicleDataList(testDriveOrderNoList);
        return vehicleDataVOList.stream()
                .collect(Collectors.toMap(
                        VehicleDataVO::getTestDriveOrderNo,
                        Function.identity(),
                        (existing, replacement) -> existing  // 保留第一个遇到的值
                ));
    }
}
