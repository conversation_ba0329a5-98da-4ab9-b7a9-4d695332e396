package com.ly.adp.drive.idal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.drive.entities.SacTestDriveSheet;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ly.adp.drive.entities.in.ProductSpecialistDTO;
import com.ly.adp.drive.entities.in.TestDriveTransferDTO;
import com.ly.adp.drive.entities.out.ProductSpecialistVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 试乘试驾单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface SacTestDriveSheetMapper extends BaseMapper<SacTestDriveSheet> {

    /**
     * 试乘试驾单新增
     *
     * @param param
     * @return
     */
    int insertSacTestDriveSheet(@Param("param") Map<String, Object> param);

    /**
     * 试乘试驾单批量新增
     *
     * @param list
     * @return
     */
    int insertSacTestDriveSheetList(@Param("list") List<Map<String, Object>> list);

    /**
     * 试乘试驾单修改
     *
     * @param param
     * @return
     */
    int updateSacTestDriveSheet(@Param("param") Map<String, Object> param);

    /**
     * 补录试乘试驾单删除
     *
     * @param param
     * @return
     */
    int deleteSacTestDriveSheet(@Param("param") Map<String, Object> param);

    /**
     * 试乘试驾单查询
     *
     * @param param
     * @return
     */
//    List<Map<String, Object>> selectSacTestDriveSheet(@Param("param") Map<String, Object> param, Page<Map<String, Object>> page);
    List<Map<String, Object>> selectSacTestDriveSheet(@Param("param") Map<String, Object> param);

    Long selectSacTestDriveSheetCount(@Param("param") Map<String, Object> param);

    /**
     * 个人试乘试驾单查询
     *
     * @param param
     * @return
     */
    List<Map<String, Object>> selectSacTestDriveSheetSingle(@Param("param") Map<String, Object> param, Page<Map<String, Object>> page);

    List<Map<String, Object>> selectSacTestDriveSheetSingleNoPage(@Param("param") Map<String, Object> param);

    Long selectSacTestDriveSheetSingleCount(@Param("param") Map<String, Object> param);
    Long selectSacTestDriveSheetSingleCountP(@Param("param") Map<String, Object> param);

    /**
     * 试乘试驾单详情查询
     *
     * @param param
     * @return
     */

    List<Map<String, Object>> selectSacTestDriveSheetDetail(@Param("param") Map<String, Object> param);




    Map<String, Object> selectClueInfoDlr(Map<String, Object> mapParam);

    Integer updateClueInfoDlrActivation(@Param("carTypeCode") Object carTypeCode, @Param("param") Map<String, Object> mapParam);

    Integer updateClueInfoDlr(@Param("carTypeCode") Object carTypeCode, @Param("param") Map<String, Object> mapParam);

    void updateReview(@Param("carTypeCode") Object carTypeCode, @Param("param") Map<String, Object> mapParam);

    int insertCdpLeads(Map<String, Object> paramMap);



    HashMap<String, Object> findSheet(Map<String, Object> mapParam);

    Map<String, Object> findUrl();


    List<ProductSpecialistVO> findProductSpecialist(ProductSpecialistDTO productSpecia);

    int testDriveTransfer(@Param("param") TestDriveTransferDTO testDriveTransferDTO, @Param("empName") String empName);

    Map<String, Object> findReceiverTime(Map<String, Object> mapParam);
}
