package com.ly.adp.drive.entities;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.FieldFill;

/**
 * <p>
 * 试驾任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@TableName("t_sac_test_drive_task")
public class SacTestDriveTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId("ID")
    private String id;

    /**
     * 任务标题
     */
    @TableField("TASK_TITLE")
    private String taskTitle;

    /**
     * 客户名称
     */
    @TableField("CUST_NAME")
    private String custName;

    /**
     * 手机号
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 性别编码
     */
    @TableField("GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     */
    @TableField("GENDER_NAME")
    private String genderName;

    /**
     * 试乘试驾类型
     */
    @TableField("TEST_TYPE")
    private String testType;

    /**
     * 完成人员ID
     */
    @TableField("TASK_PERSON_ID")
    private String taskPersonId;

    /**
     * 完成人员编码
     */
    @TableField("TASK_PERSON_CODE")
    private String taskPersonCode;

    /**
     * 完成人员名称
     */
    @TableField("TASK_PERSON_NAME")
    private String taskPersonName;

    /**
     * 完成人员专营店编码
     */
    @TableField("TASK_PERSON_DLR_CODE")
    private String taskPersonDlrCode;

    /**
     * 完成人员专营店名称
     */
    @TableField("TASK_PERSON_DLR_NAME")
    private String taskPersonDlrName;

    /**
     * 完成状态编码
     */
    @TableField("TASK_STATE_CODE")
    private String taskStateCode;

    /**
     * 完成状态名称
     */
    @TableField("TASK_STATE_NAME")
    private String taskStateName;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 完成时间
     */
    @TableField("BUSS_TIME")
    private LocalDateTime bussTime;

    /**
     * 试乘试驾车型
     */
    @TableField("SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 预约试驾日期
     */
    @TableField("APPOINTMENT_TEST_DATE")
    private String appointmentTestDate;

    /**
     * 预约试驾时间段
     */
    @TableField("APPOINTMENT_TEST_TIME")
    private String appointmentTestTime;

    /**
     * 预约试驾开始时间
     */
    @TableField("APPOINTMENT_START_TIME")
    private String appointmentStartTime;

    /**
     * 预约试驾结束时间
     */
    @TableField("APPOINTMENT_END_TIME")
    private String appointmentEndTime;

    /**
     * 发起门店
     */
    @TableField("SEND_DLR_NAME")
    private String sendDlrName;

    /**
     * 销售顾问名称
     */
    @TableField("SALES_CONSULTANT_NAME")
    private String salesConsultantName;

    /**
     * 换店类型
     */
    @TableField("MSG_TEST_TYPE")
    private String msgTestType;

    /**
     * 试驾单ID
     */
    @TableField("OLD_TEST_DRIVE_SHEET_ID")
    private String oldTestDriveSheetId;

    /**
     * 意向级别编码
     */
    @TableField("INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @TableField("INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 意向车型
     */
    @TableField("INTEN_CAR_TYPE_NAME")
    private String intenCarTypeName;

    /**
     * 预计购车时间
     */
    @TableField("PLAN_BUY_DATE_NAME")
    private String planBuyDateName;

    /**
     * 渠道来源
     */
    @TableField("CHANNEL_NAME")
    private String channelName;

    /**
     * 新试驾单ID
     */
    @TableField("NEW_TEST_DRIVE_SHEET_ID")
    private String newTestDriveSheetId;

    /**
     * 扩展信息
     */
    @TableField("EXTEND_JSON")
    private String extendJson;

    /**
     * 邀请码
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_NAME", fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建日期
     */
    @TableField(value = "CREATED_DATE", fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 修改人
     */
    @TableField(value = "MODIFY_NAME", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    /**
     * 最后更新日期
     */
    @TableField(value = "LAST_UPDATED_DATE", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @TableField(value = "UPDATE_CONTROL_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateControlId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getTaskTitle() {
        return taskTitle;
    }

    public void setTaskTitle(String taskTitle) {
        this.taskTitle = taskTitle;
    }
    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }
    public String getGenderName() {
        return genderName;
    }

    public void setGenderName(String genderName) {
        this.genderName = genderName;
    }
    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }
    public String getTaskPersonId() {
        return taskPersonId;
    }

    public void setTaskPersonId(String taskPersonId) {
        this.taskPersonId = taskPersonId;
    }
    public String getTaskPersonCode() {
        return taskPersonCode;
    }

    public void setTaskPersonCode(String taskPersonCode) {
        this.taskPersonCode = taskPersonCode;
    }
    public String getTaskPersonName() {
        return taskPersonName;
    }

    public void setTaskPersonName(String taskPersonName) {
        this.taskPersonName = taskPersonName;
    }
    public String getTaskPersonDlrCode() {
        return taskPersonDlrCode;
    }

    public void setTaskPersonDlrCode(String taskPersonDlrCode) {
        this.taskPersonDlrCode = taskPersonDlrCode;
    }
    public String getTaskPersonDlrName() {
        return taskPersonDlrName;
    }

    public void setTaskPersonDlrName(String taskPersonDlrName) {
        this.taskPersonDlrName = taskPersonDlrName;
    }
    public String getTaskStateCode() {
        return taskStateCode;
    }

    public void setTaskStateCode(String taskStateCode) {
        this.taskStateCode = taskStateCode;
    }
    public String getTaskStateName() {
        return taskStateName;
    }

    public void setTaskStateName(String taskStateName) {
        this.taskStateName = taskStateName;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public LocalDateTime getBussTime() {
        return bussTime;
    }

    public void setBussTime(LocalDateTime bussTime) {
        this.bussTime = bussTime;
    }
    public String getSmallCarTypeName() {
        return smallCarTypeName;
    }

    public void setSmallCarTypeName(String smallCarTypeName) {
        this.smallCarTypeName = smallCarTypeName;
    }
    public String getAppointmentTestDate() {
        return appointmentTestDate;
    }

    public void setAppointmentTestDate(String appointmentTestDate) {
        this.appointmentTestDate = appointmentTestDate;
    }
    public String getAppointmentTestTime() {
        return appointmentTestTime;
    }

    public void setAppointmentTestTime(String appointmentTestTime) {
        this.appointmentTestTime = appointmentTestTime;
    }
    public String getAppointmentStartTime() {
        return appointmentStartTime;
    }

    public void setAppointmentStartTime(String appointmentStartTime) {
        this.appointmentStartTime = appointmentStartTime;
    }
    public String getAppointmentEndTime() {
        return appointmentEndTime;
    }

    public void setAppointmentEndTime(String appointmentEndTime) {
        this.appointmentEndTime = appointmentEndTime;
    }
    public String getSendDlrName() {
        return sendDlrName;
    }

    public void setSendDlrName(String sendDlrName) {
        this.sendDlrName = sendDlrName;
    }
    public String getSalesConsultantName() {
        return salesConsultantName;
    }

    public void setSalesConsultantName(String salesConsultantName) {
        this.salesConsultantName = salesConsultantName;
    }
    public String getMsgTestType() {
        return msgTestType;
    }

    public void setMsgTestType(String msgTestType) {
        this.msgTestType = msgTestType;
    }
    public String getOldTestDriveSheetId() {
        return oldTestDriveSheetId;
    }

    public void setOldTestDriveSheetId(String oldTestDriveSheetId) {
        this.oldTestDriveSheetId = oldTestDriveSheetId;
    }
    public String getIntenLevelCode() {
        return intenLevelCode;
    }

    public void setIntenLevelCode(String intenLevelCode) {
        this.intenLevelCode = intenLevelCode;
    }
    public String getIntenLevelName() {
        return intenLevelName;
    }

    public void setIntenLevelName(String intenLevelName) {
        this.intenLevelName = intenLevelName;
    }
    public String getIntenCarTypeName() {
        return intenCarTypeName;
    }

    public void setIntenCarTypeName(String intenCarTypeName) {
        this.intenCarTypeName = intenCarTypeName;
    }
    public String getPlanBuyDateName() {
        return planBuyDateName;
    }

    public void setPlanBuyDateName(String planBuyDateName) {
        this.planBuyDateName = planBuyDateName;
    }
    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    public String getNewTestDriveSheetId() {
        return newTestDriveSheetId;
    }

    public void setNewTestDriveSheetId(String newTestDriveSheetId) {
        this.newTestDriveSheetId = newTestDriveSheetId;
    }
    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "SacTestDriveTask{" +
                "id=" + id +
                ", taskTitle=" + taskTitle +
                ", custName=" + custName +
                ", phone=" + phone +
                ", genderCode=" + genderCode +
                ", genderName=" + genderName +
                ", testType=" + testType +
                ", taskPersonId=" + taskPersonId +
                ", taskPersonCode=" + taskPersonCode +
                ", taskPersonName=" + taskPersonName +
                ", taskPersonDlrCode=" + taskPersonDlrCode +
                ", taskPersonDlrName=" + taskPersonDlrName +
                ", taskStateCode=" + taskStateCode +
                ", taskStateName=" + taskStateName +
                ", remark=" + remark +
                ", bussTime=" + bussTime +
                ", smallCarTypeName=" + smallCarTypeName +
                ", appointmentTestDate=" + appointmentTestDate +
                ", appointmentTestTime=" + appointmentTestTime +
                ", appointmentStartTime=" + appointmentStartTime +
                ", appointmentEndTime=" + appointmentEndTime +
                ", sendDlrName=" + sendDlrName +
                ", salesConsultantName=" + salesConsultantName +
                ", msgTestType=" + msgTestType +
                ", oldTestDriveSheetId=" + oldTestDriveSheetId +
                ", intenLevelCode=" + intenLevelCode +
                ", intenLevelName=" + intenLevelName +
                ", intenCarTypeName=" + intenCarTypeName +
                ", planBuyDateName=" + planBuyDateName +
                ", channelName=" + channelName +
                ", newTestDriveSheetId=" + newTestDriveSheetId +
                ", extendJson=" + extendJson +
                ", column1=" + column1 +
                ", column2=" + column2 +
                ", column3=" + column3 +
                ", column4=" + column4 +
                ", column5=" + column5 +
                ", mycatOpTime=" + mycatOpTime +
                ", oemId=" + oemId +
                ", groupId=" + groupId +
                ", oemCode=" + oemCode +
                ", groupCode=" + groupCode +
                ", creator=" + creator +
                ", createdName=" + createdName +
                ", createdDate=" + createdDate +
                ", modifier=" + modifier +
                ", modifyName=" + modifyName +
                ", lastUpdatedDate=" + lastUpdatedDate +
                ", isEnable=" + isEnable +
                ", sdpUserId=" + sdpUserId +
                ", sdpOrgId=" + sdpOrgId +
                ", updateControlId=" + updateControlId +
                "}";
    }
}
