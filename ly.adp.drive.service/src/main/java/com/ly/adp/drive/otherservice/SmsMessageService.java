package com.ly.adp.drive.otherservice;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ibm.icu.text.SimpleDateFormat;
import com.ly.adp.drive.entities.MessageResponseEntity;
import com.ly.bucn.component.interceptor.annotation.Interceptor;
import com.ly.mp.component.entities.OptResult;

@Service
public class SmsMessageService {
	private static final Logger logger = LoggerFactory.getLogger(SmsMessageService.class);

	@Autowired
	ISmsMessageFeignClient smsMessageService;

	@Interceptor("cust_send_agent_message")
	public OptResult sendAgentMessage(Map<String, Object> mapParam, String token) {
		OptResult result = new OptResult();
		HashMap<String, Object> logInfoMap = new HashMap<>();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		logInfoMap.put("begTime", formatter.format(new Date()));
		logInfoMap.put("sendTimeStart", formatter.format(new Date()));
		MessageResponseEntity messageResponse = null;
		try {
			// 值列表存放域名和短信模板
			// SELECT LOOKUP_VALUE_NAME,ATTRIBUTE1 AS WEB_SITE FROM T_PRC_MDS_LOOKUP_VALUE
			// WHERE LOOKUP_TYPE_CODE = 'DB1032' AND LOOKUP_VALUE_CODE='1';
			// Map<String, Object> templateMap = agentInfoMapper.getLookupValue();
			Map<String, Object> templateMap = new HashMap<>();
			templateMap.put("lookupValueName", mapParam.get("smsTemplateId"));// 模板ID
			// 短信模板变量
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("code", UUID.randomUUID().toString());
			param.put("time", "30分钟");
			// 请求体
			Map<String, Object> requestBody = new HashMap<String, Object>();
			requestBody.put("recNum", new String[] { (String) mapParam.get("agentContactPhone") }); // 1.接收短信的号码
			requestBody.put("smsParam", param); // 2.短信模板变量
			requestBody.put("smsTemplateId", templateMap.get("lookupValueName")); // 3.模板id
			logInfoMap.put("requestBody", requestBody);
			// Feign调用发送短信接口
			messageResponse = smsMessageService.rest(requestBody);
			// 20000 success
			// 40000 send error
			// 40006 phone is invalid
			// 40007 template parse error
			// 40008 phone daily msg overlimit
			// 40009 appid daily msg overlimit
			// 50000 server error
		} catch (Exception e) {
			logInfoMap.put("sendStatus", "-1"); // Feign调用短信接口失败
			logInfoMap.put("errors", e.getMessage());
			logger.error("SmsMessageService::sendAgentMessage", e.getMessage());
		} finally {
			logInfoMap.put("endTime", formatter.format(new Date()));
			logInfoMap.put("sendTimeEnd", formatter.format(new Date()));
			logInfoMap.put("response", messageResponse);
			if (messageResponse == null) {
				result.setMsg("Feign调用短信发送接口失败");
				logInfoMap.put("errors", "Feign调用短信发送接口失败");
				result.setResult("0");
			} else if ("200".equals(messageResponse.getCode())) {
				logInfoMap.put("sendStatus", "1");
				logInfoMap.put("message", messageResponse.getMessage());
				result.setMsg(messageResponse.getMessage());
			} else {
				logInfoMap.put("sendStatus", "0");
				logInfoMap.put("message", messageResponse.getMessage());
				result.setResult("0");
				result.setMsg(messageResponse.getMessage());
				logInfoMap.put("errors", messageResponse.getMessage());
			}
			mapParam.putAll(logInfoMap);
		}
		return result;
	}
}
