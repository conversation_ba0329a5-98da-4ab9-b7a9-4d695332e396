package com.ly.adp.drive.otherservice;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.drive.entities.MdmCarColor;
import com.ly.adp.drive.entities.MdmCarIncolor;
import com.ly.adp.drive.otherservice.entities.BuTestcarPrepare;
import com.ly.adp.drive.otherservice.entities.MdmAgentInfo;
import com.ly.adp.drive.otherservice.entities.MdmCarBrand;
import com.ly.adp.drive.otherservice.entities.MdmOrgCityOut;
import com.ly.adp.drive.otherservice.entities.MdmOrgCommunityOut;
import com.ly.adp.drive.otherservice.entities.MdmOrgEmployee;
import com.ly.adp.drive.otherservice.entities.MdmOrgEmployeeBrOut;
import com.ly.adp.drive.otherservice.entities.MdmOrgProvince;
import com.ly.adp.drive.otherservice.entities.MdsLookupValue;
import com.ly.adp.drive.otherservice.entities.in.MdmAgentCompanyIn;
import com.ly.adp.drive.otherservice.entities.in.MdmCarColorIn;
import com.ly.adp.drive.otherservice.entities.in.MdmCarIncolorIn;
import com.ly.adp.drive.otherservice.entities.in.MdmOrgCityIn;
import com.ly.adp.drive.otherservice.entities.in.MdmOrgCommunityExtIn;
import com.ly.adp.drive.otherservice.entities.in.MdmOrgEmployeeBrIn;
import com.ly.adp.drive.otherservice.entities.in.MdmOrgEmployeeIn;
import com.ly.adp.drive.otherservice.entities.in.MdmOrgProvinceIn;
import com.ly.adp.drive.otherservice.entities.in.MdsLookupValueIn;
import com.ly.adp.drive.otherservice.entities.in.PrcProvinceLinkageIn;
import com.ly.mp.bucn.pack.entity.ParamPage;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;

@FeignClient(name = "${refer.name.adp.csc:ly.adp.base}", url = "${refer.url.adp.base}")
public interface IBasedataFeignClient {

    // 根据岗位获取员工信息
    @PostMapping("/ly/adp/base/clue/locationempinfo.do")
    ListResult<Map<String, Object>> locationEmpInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    @PostMapping("/ly/adp/base/clue/locationempinfo.todo")
    ListResult<Map<String, Object>> locationEmpInfo2(@RequestBody(required = false) Map<String, Object> dateInfo);

    //createmptoken.do
    @PostMapping("/ly/adp/base/clue/createmptoken.do")
    OptResult createMpToken(@RequestBody(required = false) Map<String, Object> dateInfo);

    //createmptoken.do
    @PostMapping("/ly/adp/base/clue/createmptokenwithoutsuffix")
    OptResult createMpTokenWithoutSuffix(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 获取用户信息
    @PostMapping("ly/busicen/base/eap/org/sysuser/getPartUserInfo.do")
    Map<String, Object> getPartUserInfo(@RequestBody(required = false) String empId) throws Exception;

    // 根据多个ID获取员工信息
    @PostMapping("/ly/adp/base/clue/getempbyidlist.do")
    ListResult<Map<String, Object>> sacMsgRecordFindInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                                         @RequestBody(required = false) Map<String, Object> dateInfo);

    /**
     * 调用BASE-一期值列表查询
     * @param authentication
     * @param lookupTypeCode
     * @param lookupValueCode
     * @return
     */
    @PostMapping("/ly/adp/base/lookuptype/getlookupvaluename.do")
    String getLookupValueName(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                              @RequestParam(value = "lookupTypeCode") String lookupTypeCode,
                              @RequestParam(value = "lookupValueCode") String lookupValueCode);

    // 调用BASE-一期值查询服务
    @PostMapping("/ly/adp/base/lookuptype/mdslookupvaluefindbypage.do")
    ListResult<MdsLookupValue> mdsLookupValueFindByPage(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                        @RequestBody(required = false) MdsLookupValueIn mdsLookupValue);

    // 调用BASE-一期值查询服务
    @PostMapping("/ly/adp/base/lookuptype/mdslookupvaluefindbypageNotDo")
    ListResult<MdsLookupValue> mdslookupvaluefindbypageNotDo(@RequestBody(required = false) MdsLookupValueIn mdsLookupValue);

    // 调用BASE-试驾车信息管理
    @PostMapping("/ly/adp/base/buTestcarPrepare/buTestcarPrepareManage.do")
    public EntityResult<BuTestcarPrepare> inset(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                @RequestBody ParamPage<BuTestcarPrepare> param);

    // 调用BASE-门店员工信息管理
    @PostMapping("/ly/busicen/base/usc/org/employee/mdmOrgEmployeeQueryFindDlr.do")
    public ListResult<Map<String, Object>> mdmOrgEmployeeQueryFindDlr(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgEmployeeIn info) throws Exception;

    // 保证金管理服务-车型配置信息
    @PostMapping("/ly/adp/base/tUscBuCashDeposit/mdmVeCarConfigQueryList.do")
    ListResult<Map<String, Object>> mdmVeCarConfigQueryList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> param) throws Exception;

    // 区县服务-区县查询服务
    @PostMapping("/ly/busicen/base/usc/org/mdmorgcommunity/mdmorgcommunityqueryfindall.do")
    ListResult<MdmOrgCommunityOut> mdmOrgCommunityQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgCommunityExtIn info) throws Exception;

    // 车身颜色服务-车身颜色查询
    @PostMapping("/ly/busicen/base/prc/basedata/MdmVeCarColorController/mdmCarColorQueryList.do")
    ListResult<MdmCarColor> mdmCarColorQueryList(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                 @RequestBody(required = false) MdmCarColorIn mdmCarColor) throws Exception;

    // 内饰颜色服务-查询内饰色-订单
    @PostMapping("/ly/busicen/base/prc/basedata/MdmVeCarIncolorController/mdmCarIncolorQueryListForPage.do")
    ListResult<MdmCarIncolor> mdmCarIncolorQueryListForPage(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmCarIncolorIn dataInfo) throws Exception;

    // 代理商服务-代理商基本查询服务
    @PostMapping("/ly/adp/base/mdmAgentInfo/agentBasicInfoList.do")
    ListResult<Map<String, Object>> agentBasicInfoList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmAgentInfo mdmAgentInfo) throws Exception;

    // 城市公司信息服务-城市公司下门店查询
    @PostMapping("/ly/adp/base/mdmAgentCompany/getMdmCompanyDlrInfoList.do")
    ListResult<Map<String, Object>> getMdmCompanyDlrInfoList(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmAgentCompanyIn mdmAgentCompanyIn);

    // 门店管理服务-经销商信息查询
    @PostMapping("/ly/adp/base/mdmDlrInfo/mdmDlrInfoQuery.do")
    ListResult<Map<String, Object>> mdmDlrInfoQuery(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> info) throws Exception;

    // 员工信息服务-xapi员工查询
    @PostMapping("/ly/busicen/base/usc/org/employee/selectById")
    MdmOrgEmployee selectById(@RequestBody(required = false) String empId);

    // 根据empId获取员工信息
    @PostMapping("/ly/busicen/base/usc/org/employee/mdmOrgEmployeeQueryFindById.do")
    List<Map<String, Object>> mdmOrgEmployeeQueryFindById(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) Map<String, Object> info) throws Exception;

    // 查询员工品牌信息
    @RequestMapping(value = "/employeebr/mdmOrgEmployeeBrQueryFindAll", method = RequestMethod.POST)
    ListResult<MdmOrgEmployeeBrOut> mdmOrgEmployeeBrQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgEmployeeBrIn info) throws Exception;

    // 获取员工岗位信息
    // 员工信息服务-员工岗位查询
    @RequestMapping(value = "/ly/busicen/base/usc/org/employee/selectMdmOrgEmployeeByStation.do", method = RequestMethod.POST)
    ListResult<Map<String, Object>> selectMdmOrgEmployeeByStation(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) ParamBase<Map<String, Object>> info) throws Exception;

    // 调用BASE-查询品牌信息列表
    @PostMapping("/ly/busicen/base/prc/basedata/carbrand/qurymdmcarbrand.do")
    List<MdmCarBrand> quryMdmCarBrand(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody(required = false) MdmCarBrand mdmCarBrand);

    // 调用BASE-APP版本信息查询
    @PostMapping("/appversion/queryAppVersionInfo")
    ListResult<Map<String, Object>> queryAppVersionInfo(@RequestBody(required = false) Map<String, Object> map,
                                                        @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication);

    // 调用BASE-单号生成服务
    @PostMapping("/ordercoderule/generateOrderCode.do")
    OptResult generateOrderCodeTest(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestParam("dlrId") String dlrId, @RequestParam("billTypeId") String billTypeId) throws Exception;

    // 调用BASE-单号生成服务
    @PostMapping("/ordercoderule/generateOrderCode.do")
    OptResult generateOrderCode(
            @RequestParam("dlrId") String dlrId, @RequestParam("billTypeId") String billTypeId) throws Exception;

    // 调用BASE-单号生成服务-feign(不用token)
    @PostMapping("/ordercoderule/generateOrderCodeForFeign.do")
    OptResult generateOrderCodeForFeign(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody Map<String, Object> map);

    // 调用BASE-省市区三级查询
    @PostMapping("/ly/busicen/base/queryPrcProvinceLinkage/queryPrcProvinceLinkages.do")
    ListResult<Map<String, Object>> queryPrcProvinceLinkage(
            @RequestBody(required = false) PrcProvinceLinkageIn dataInfo,
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication);

    // 调用BASE-省份信息查询服务
    @PostMapping("/ly/busicen/base/usc/org/mdmOrgProvince/mdmOrgProvinceQueryFindAll.do")
    ListResult<MdmOrgProvince> mdmOrgProvinceQueryFindAll(
            @RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
            @RequestBody(required = false) MdmOrgProvinceIn info);

    // 调用BASE-城市信息查询服务
    @PostMapping("/ly/busicen/base/usc/org/mdmorgcity/mdmorgcityqueryfindall.do")
    ListResult<MdmOrgCityOut> mdmOrgCityQueryFindAll(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                                     @RequestBody(required = false) MdmOrgCityIn info);

    // 根据多个ID获取员工信息
    @PostMapping(value = "/ly/adp/base/clue/getempbyidlist.do")
    ListResult<Map<String, Object>> getEmpByIdList(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 根据userId获取员工信息
    @PostMapping(value = "/ly/adp/base/clue/getuserinfobyuserid.do")
    ListResult<Map<String, Object>> getUserInfoByUserId(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询用户所属组织类型
    @PostMapping(value = "/ly/adp/base/clue/getbelongnodeinfo.do")
    ListResult<Map<String, Object>> getBelongNodeInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询员工所属经销商信息
    @PostMapping(value = "/ly/adp/base/clue/getempdlrinfo.do")
    ListResult<Map<String, Object>> getEmpDlrInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 获取经销商对应的主机厂信息
    @PostMapping(value = "/ly/adp/base/clue/getdlroeminfo.do")
    ListResult<Map<String, Object>> getDlrOemInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询员工品牌信息
    @PostMapping(value = "/ly/adp/base/clue/getempbrandinfo.do")
    ListResult<Map<String, Object>> getEmpBrandInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 获取员工岗位信息
    @PostMapping(value = "/ly/adp/base/clue/getempstationinfo.do")
    ListResult<Map<String, Object>> getEmpStationInfo(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 初始化代理商
    @PostMapping(value = "/ly/adp/base/clue/applyinitfind.do")
    ListResult<Map<String, Object>> applyInitFind(@RequestBody(required = false) Map<String, Object> dateInfo);

    // 查询区域经理管辖门店
    @PostMapping(value = "/ly/adp/base/clue/querymanagedlr.do")
    ListResult<Map<String, Object>> findManagedDlrList(@RequestBody(required = false) Map<String, Object> dataInfo);

    @PostMapping("/ly/adp/base/clue/createMpTokenInfo")
    OptResult createMpTokenInfo(@RequestBody(required = false) Map<String, Object> dateInfo);
}