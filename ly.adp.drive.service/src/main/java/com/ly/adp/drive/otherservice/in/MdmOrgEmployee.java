package com.ly.adp.drive.otherservice.in;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 职员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@TableName("t_usc_mdm_org_employee")
public class MdmOrgEmployee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 职员ID
     */
    @TableId("EMP_ID")
    private String empId;

    /**
     * 部门ID  T_MDM_ORG_DEPT DEPT_ID
     */
    @TableField("DEPT_ID")
    private String deptId;

    /**
     * 专营店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 职员编码
     */
    @TableField("EMP_CODE")
    private String empCode;

    /**
     * 职员姓名
     */
    @TableField("EMP_NAME")
    private String empName;

    /**
     * 出生日期
     */
    @TableField("BIRTH_DATE")
    private LocalDateTime birthDate;

    /**
     * 工作电话
     */
    @TableField("WORK_TEL")
    private String workTel;

    /**
     * 手机号码
     */
    @TableField("MOBILE")
    private String mobile;

    /**
     * 性别编码
     */
    @TableField("GENDER_CODE")
    private String genderCode;

    /**
     * 学历编码
     */
    @TableField("DEGREE_CODE")
    private String degreeCode;

    /**
     * 个人通信地址
     */
    @TableField("PERSON_ADDR")
    private String personAddr;

    /**
     * 邮编
     */
    @TableField("ZIP")
    private String zip;

    /**
     * 邮箱
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 传真号
     */
    @TableField("FAX")
    private String fax;

    /**
     * 国籍编码
     */
    @TableField("NATIONALITY_CODE")
    private String nationalityCode;

    /**
     * 婚姻状况编码
     */
    @TableField("MARRIAGED_CODE")
    private String marriagedCode;

    /**
     * 籍贯
     */
    @TableField("NATIVE_PLACE")
    private String nativePlace;

    /**
     * 毕业院校
     */
    @TableField("SCHOOL")
    private String school;

    /**
     * 专业
     */
    @TableField("DEGREEPRO")
    private String degreepro;

    /**
     * 技能特长
     */
    @TableField("SKILL_SPECIAL")
    private String skillSpecial;

    /**
     * 家庭电话
     */
    @TableField("FAMILY_PHONE")
    private String familyPhone;

    /**
     * 紧急联络人
     */
    @TableField("SECOND_MAN")
    private String secondMan;

    /**
     * 紧急联络人电话
     */
    @TableField("SECOND_MAN_TEL")
    private String secondManTel;

    /**
     * 领取驾照日期
     */
    @TableField("DRIVER_DATE")
    private LocalDateTime driverDate;

    /**
     * 民族编码
     */
    @TableField("NATION_CODE")
    private String nationCode;

    /**
     * 汽车行业从业时间
     */
    @TableField("BUSINESS_DATE")
    private LocalDateTime businessDate;

    /**
     * 入职日期
     */
    @TableField("EMPLOY_DATE")
    private LocalDateTime employDate;

    /**
     * 是否有驾照
     */
    @TableField("IS_DRIVER")
    private String isDriver;

    /**
     * 招聘方式
     */
    @TableField("EMPLOY_TYPE")
    private String employType;

    /**
     * 政治面貌编码
     */
    @TableField("POLITICS_CODE")
    private String politicsCode;

    /**
     * 证件类型编码
     */
    @TableField("CRED_TYPE_CODE")
    private String credTypeCode;

    /**
     * 证件号
     */
    @TableField("CRED_NO")
    private String credNo;

    /**
     * 职称
     */
    @TableField("CLASS")
    private String classes;

    /**
     * 职员照片
     */
    @TableField("EMP_PIC")
    private String empPic;

    /**
     * 自我评价
     */
    @TableField("SELF_ESTIMATE")
    private String selfEstimate;

    /**
     * 专营店ID
     */
    @TableField("DLR_ID")
    private String dlrId;

    /**
     * 第一岗位
     */
    @TableField("STATION_ID")
    private String stationId;

    /**
     * 二级网点ID 如属于一级店则为空
     */
    @TableField("SEC_DLR_ID")
    private String secDlrId;

    /**
     * 二级网点编码 如属于一级店则为空
     */
    @TableField("SEC_DLR_CODE")
    private String secDlrCode;

    /**
     * 直属上司ID
     */
    @TableField("HEAD_MANAGER")
    private String headManager;

    /**
     * 离职日期
     */
    @TableField("LEAVE_DATE")
    private LocalDateTime leaveDate;

    /**
     * 是否冻结
     */
    @TableField("IS_FROZEN")
    private String isFrozen;

    /**
     * 一网编码  一网DLRCODE
     */
    @TableField("OLD_DLR_CODE")
    private String oldDlrCode;

    /**
     * 一网ID  一网DLR_ID
     */
    @TableField("OLD_DLR_ID")
    private String oldDlrId;

    /**
     * 时间戳
     */
    @TableField("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @TableField("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @TableField("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建人姓名
     */
    @TableField("CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 修改人姓名
     */
    @TableField("MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @TableField("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @TableField("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 用户ID
     */
    @TableField("USER_ID")
    private String userId;

    /**
     * 用户名称
     */
    @TableField("USER_NAME")
    private String userName;

    /**
     * 是否培训合格
     */
    @TableField("COLUMN1")
    private String column1;

    /**
     * 兼职岗位
     */
    @TableField("COLUMN2")
    private String column2;

    /**
     * 省份
     */
    @TableField("COLUMN3")
    private String column3;

    /**
     * 城市
     */
    @TableField("COLUMN4")
    private String column4;

    /**
     * 区县
     */
    @TableField("COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @TableField("COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @TableField("COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @TableField("COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @TableField("COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @TableField("COLUMN10")
    private String column10;

    /**
     * 离职原因编码
     */
    @TableField("RESIGNATION_REASON_CODE")
    private String resignationReasonCode;

    /**
     * 离职原因名称
     */
    @TableField("RESIGNATION_REASON_NAME")
    private String resignationReasonName;

    /**
     * 老部门ID
     */
    @TableField("OLD_DEPT_ID")
    private String oldDeptId;

    /**
     * 状态（1在职，2离职）
     */
    @TableField("USER_STATUS")
    private String userStatus;

    /**
     * 对应厂家员工编码
     */
    @TableField("FACTORY_EMP_CODE")
    private String factoryEmpCode;

    /**
     * 职级
     */
    @TableField("DUTY")
    private String duty;

    /**
     * 职级名称
     */
    @TableField("DUTY_NAME")
    private String dutyName;

    /**
     * 团队
     */
    @TableField("GROUP_TEAM")
    private String groupTeam;

    /**
     * 团队名称
     */
    @TableField("GROUP_TEAM_NAME")
    private String groupTeamName;

    /**
     * 证件类型名称
     */
    @TableField("CRED_TYPE_NAME")
    private String credTypeName;

    /**
     * 生效日期
     */
    @TableField("ACTIVE_DATE")
    private LocalDateTime activeDate;

    /**
     * 失效日期
     */
    @TableField("DISABLE_DATE")
    private LocalDateTime disableDate;


    /**
     * 失效日期
     */
    @TableField("SHOW_PHONE")
    private String showPhone;

    /**
     * ORG_ID
     */
    @TableField("ORG_ID")
    private String orgId;



    /**
     * ORG_NAME
     */
    @TableField("ORG_NAME")
    private String orgName;

    /**
     * 专营店账号id
     */
    @TableField("DLR_USER_ID")
    private String dlrUserId;

    /**
     * 专营店账号Name
     */
    @TableField("DLR_USER_NAME")
    private String dlrUserName;

    /**
     * 灰度
     */
    @TableField("grayScale")
    private String grayScale;

    /**
     * 灰度筛选字段
     */
    @TableField("IS_GRAY_SCALE")
    private String isGrayScale;

    public String getDlrUserId() {
		return dlrUserId;
	}

	public void setDlrUserId(String dlrUserId) {
		this.dlrUserId = dlrUserId;
	}

	public String getDlrUserName() {
		return dlrUserName;
	}

	public void setDlrUserName(String dlrUserName) {
		this.dlrUserName = dlrUserName;
	}

	public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }
    public String getEmpCode() {
        return empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }
    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }
    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }
    public String getWorkTel() {
        return workTel;
    }

    public void setWorkTel(String workTel) {
        this.workTel = workTel;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }
    public String getDegreeCode() {
        return degreeCode;
    }

    public void setDegreeCode(String degreeCode) {
        this.degreeCode = degreeCode;
    }
    public String getPersonAddr() {
        return personAddr;
    }

    public void setPersonAddr(String personAddr) {
        this.personAddr = personAddr;
    }
    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }
    public String getNationalityCode() {
        return nationalityCode;
    }

    public void setNationalityCode(String nationalityCode) {
        this.nationalityCode = nationalityCode;
    }
    public String getMarriagedCode() {
        return marriagedCode;
    }

    public void setMarriagedCode(String marriagedCode) {
        this.marriagedCode = marriagedCode;
    }
    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }
    public String getSchool() {
        return school;
    }

    public void setSchool(String school) {
        this.school = school;
    }
    public String getDegreepro() {
        return degreepro;
    }

    public void setDegreepro(String degreepro) {
        this.degreepro = degreepro;
    }
    public String getSkillSpecial() {
        return skillSpecial;
    }

    public void setSkillSpecial(String skillSpecial) {
        this.skillSpecial = skillSpecial;
    }
    public String getFamilyPhone() {
        return familyPhone;
    }

    public void setFamilyPhone(String familyPhone) {
        this.familyPhone = familyPhone;
    }
    public String getSecondMan() {
        return secondMan;
    }

    public void setSecondMan(String secondMan) {
        this.secondMan = secondMan;
    }
    public String getSecondManTel() {
        return secondManTel;
    }

    public void setSecondManTel(String secondManTel) {
        this.secondManTel = secondManTel;
    }
    public LocalDateTime getDriverDate() {
        return driverDate;
    }

    public void setDriverDate(LocalDateTime driverDate) {
        this.driverDate = driverDate;
    }
    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }
    public LocalDateTime getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(LocalDateTime businessDate) {
        this.businessDate = businessDate;
    }
    public LocalDateTime getEmployDate() {
        return employDate;
    }

    public void setEmployDate(LocalDateTime employDate) {
        this.employDate = employDate;
    }
    public String getIsDriver() {
        return isDriver;
    }

    public void setIsDriver(String isDriver) {
        this.isDriver = isDriver;
    }
    public String getEmployType() {
        return employType;
    }

    public void setEmployType(String employType) {
        this.employType = employType;
    }
    public String getPoliticsCode() {
        return politicsCode;
    }

    public void setPoliticsCode(String politicsCode) {
        this.politicsCode = politicsCode;
    }
    public String getCredTypeCode() {
        return credTypeCode;
    }

    public void setCredTypeCode(String credTypeCode) {
        this.credTypeCode = credTypeCode;
    }
    public String getCredNo() {
        return credNo;
    }

    public void setCredNo(String credNo) {
        this.credNo = credNo;
    }
    public String getClasses() {
        return classes;
    }

    public void setClasses(String classes) {
        this.classes = classes;
    }
    public String getEmpPic() {
        return empPic;
    }

    public void setEmpPic(String empPic) {
        this.empPic = empPic;
    }
    public String getSelfEstimate() {
        return selfEstimate;
    }

    public void setSelfEstimate(String selfEstimate) {
        this.selfEstimate = selfEstimate;
    }
    public String getDlrId() {
        return dlrId;
    }

    public void setDlrId(String dlrId) {
        this.dlrId = dlrId;
    }
    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }
    public String getSecDlrId() {
        return secDlrId;
    }

    public void setSecDlrId(String secDlrId) {
        this.secDlrId = secDlrId;
    }
    public String getSecDlrCode() {
        return secDlrCode;
    }

    public void setSecDlrCode(String secDlrCode) {
        this.secDlrCode = secDlrCode;
    }
    public String getHeadManager() {
        return headManager;
    }

    public void setHeadManager(String headManager) {
        this.headManager = headManager;
    }
    public LocalDateTime getLeaveDate() {
        return leaveDate;
    }

    public void setLeaveDate(LocalDateTime leaveDate) {
        this.leaveDate = leaveDate;
    }
    public String getIsFrozen() {
        return isFrozen;
    }

    public void setIsFrozen(String isFrozen) {
        this.isFrozen = isFrozen;
    }
    public String getOldDlrCode() {
        return oldDlrCode;
    }

    public void setOldDlrCode(String oldDlrCode) {
        this.oldDlrCode = oldDlrCode;
    }
    public String getOldDlrId() {
        return oldDlrId;
    }

    public void setOldDlrId(String oldDlrId) {
        this.oldDlrId = oldDlrId;
    }
    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }
    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }
    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }
    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }
    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }
    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }
    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }
    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }
    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }
    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }
    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }
    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }
    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }
    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }
    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }
    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }
    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }
    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }
    public String getResignationReasonCode() {
        return resignationReasonCode;
    }

    public void setResignationReasonCode(String resignationReasonCode) {
        this.resignationReasonCode = resignationReasonCode;
    }
    public String getResignationReasonName() {
        return resignationReasonName;
    }

    public void setResignationReasonName(String resignationReasonName) {
        this.resignationReasonName = resignationReasonName;
    }
    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
    public String getOldDeptId() {
        return oldDeptId;
    }

    public void setOldDeptId(String oldDeptId) {
        this.oldDeptId = oldDeptId;
    }
    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }
    public String getFactoryEmpCode() {
        return factoryEmpCode;
    }

    public void setFactoryEmpCode(String factoryEmpCode) {
        this.factoryEmpCode = factoryEmpCode;
    }
    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }
    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }
    public String getGroupTeam() {
        return groupTeam;
    }

    public void setGroupTeam(String groupTeam) {
        this.groupTeam = groupTeam;
    }
    public String getGroupTeamName() {
        return groupTeamName;
    }

    public void setGroupTeamName(String groupTeamName) {
        this.groupTeamName = groupTeamName;
    }
    public String getCredTypeName() {
        return credTypeName;
    }

    public void setCredTypeName(String credTypeName) {
        this.credTypeName = credTypeName;
    }
    public LocalDateTime getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(LocalDateTime activeDate) {
        this.activeDate = activeDate;
    }
    public LocalDateTime getDisableDate() {
        return disableDate;
    }

    public void setDisableDate(LocalDateTime disableDate) {
        this.disableDate = disableDate;
    }



    public String getShowPhone() {
		return showPhone;
	}

	public void setShowPhone(String showPhone) {
		this.showPhone = showPhone;
	}

    public String getGrayScale() {
        return grayScale;
    }

    public void setGrayScale(String grayScale) {
        this.grayScale = grayScale;
    }

    public String getIsGrayScale() {
		return isGrayScale;
	}

	public void setIsGrayScale(String isGrayScale) {
		this.isGrayScale = isGrayScale;
	}

	@Override
    public String toString() {
        return "MdmOrgEmployee{" +
                "empId='" + empId + '\'' +
                ", deptId='" + deptId + '\'' +
                ", dlrCode='" + dlrCode + '\'' +
                ", empCode='" + empCode + '\'' +
                ", empName='" + empName + '\'' +
                ", birthDate=" + birthDate +
                ", workTel='" + workTel + '\'' +
                ", mobile='" + mobile + '\'' +
                ", genderCode='" + genderCode + '\'' +
                ", degreeCode='" + degreeCode + '\'' +
                ", personAddr='" + personAddr + '\'' +
                ", zip='" + zip + '\'' +
                ", email='" + email + '\'' +
                ", fax='" + fax + '\'' +
                ", nationalityCode='" + nationalityCode + '\'' +
                ", marriagedCode='" + marriagedCode + '\'' +
                ", nativePlace='" + nativePlace + '\'' +
                ", school='" + school + '\'' +
                ", degreepro='" + degreepro + '\'' +
                ", skillSpecial='" + skillSpecial + '\'' +
                ", familyPhone='" + familyPhone + '\'' +
                ", secondMan='" + secondMan + '\'' +
                ", secondManTel='" + secondManTel + '\'' +
                ", driverDate=" + driverDate +
                ", nationCode='" + nationCode + '\'' +
                ", businessDate=" + businessDate +
                ", employDate=" + employDate +
                ", isDriver='" + isDriver + '\'' +
                ", employType='" + employType + '\'' +
                ", politicsCode='" + politicsCode + '\'' +
                ", credTypeCode='" + credTypeCode + '\'' +
                ", credNo='" + credNo + '\'' +
                ", classes='" + classes + '\'' +
                ", empPic='" + empPic + '\'' +
                ", selfEstimate='" + selfEstimate + '\'' +
                ", dlrId='" + dlrId + '\'' +
                ", stationId='" + stationId + '\'' +
                ", secDlrId='" + secDlrId + '\'' +
                ", secDlrCode='" + secDlrCode + '\'' +
                ", headManager='" + headManager + '\'' +
                ", leaveDate=" + leaveDate +
                ", isFrozen='" + isFrozen + '\'' +
                ", oldDlrCode='" + oldDlrCode + '\'' +
                ", oldDlrId='" + oldDlrId + '\'' +
                ", mycatOpTime=" + mycatOpTime +
                ", oemId='" + oemId + '\'' +
                ", groupId='" + groupId + '\'' +
                ", oemCode='" + oemCode + '\'' +
                ", groupCode='" + groupCode + '\'' +
                ", creator='" + creator + '\'' +
                ", createdName='" + createdName + '\'' +
                ", createdDate=" + createdDate +
                ", modifier='" + modifier + '\'' +
                ", modifyName='" + modifyName + '\'' +
                ", lastUpdatedDate=" + lastUpdatedDate +
                ", isEnable='" + isEnable + '\'' +
                ", sdpUserId='" + sdpUserId + '\'' +
                ", sdpOrgId='" + sdpOrgId + '\'' +
                ", updateControlId='" + updateControlId + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", column1='" + column1 + '\'' +
                ", column2='" + column2 + '\'' +
                ", column3='" + column3 + '\'' +
                ", column4='" + column4 + '\'' +
                ", column5='" + column5 + '\'' +
                ", column6='" + column6 + '\'' +
                ", column7='" + column7 + '\'' +
                ", column8='" + column8 + '\'' +
                ", column9='" + column9 + '\'' +
                ", column10='" + column10 + '\'' +
                ", resignationReasonCode='" + resignationReasonCode + '\'' +
                ", resignationReasonName='" + resignationReasonName + '\'' +
                ", oldDeptId='" + oldDeptId + '\'' +
                ", userStatus='" + userStatus + '\'' +
                ", factoryEmpCode='" + factoryEmpCode + '\'' +
                ", duty='" + duty + '\'' +
                ", dutyName='" + dutyName + '\'' +
                ", groupTeam='" + groupTeam + '\'' +
                ", groupTeamName='" + groupTeamName + '\'' +
                ", credTypeName='" + credTypeName + '\'' +
                ", activeDate=" + activeDate +
                ", disableDate=" + disableDate +
                ", showPhone='" + showPhone + '\'' +
                ", dlrUserId='" + dlrUserId + '\'' +
                ", dlrUserName='" + dlrUserName + '\'' +
                ", grayScale='" + grayScale + '\'' +
                '}';
    }
}
