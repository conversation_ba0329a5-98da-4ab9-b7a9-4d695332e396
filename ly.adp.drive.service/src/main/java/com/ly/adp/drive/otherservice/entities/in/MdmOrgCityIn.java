package com.ly.adp.drive.otherservice.entities.in;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ly.mp.component.entities.PageInfo;

import io.swagger.annotations.ApiModelProperty;
 
public class MdmOrgCityIn extends PageInfo implements Serializable {
	
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "城市ID", required = false, example = "")
    private String cityId;

    @ApiModelProperty(value = "省份ID  T_MDM_ORG_PROVINCE PROVINCE_ID", required = false, example = "")
    private String provinceId;

    @ApiModelProperty(value = "小区ID", required = false, example = "")
    private String smallAreaId;

    @ApiModelProperty(value = "城市编码", required = false, example = "")
    private String cityCode;

    @ApiModelProperty(value = "城市名称", required = false, example = "")
    private String cityName;

    @ApiModelProperty(value = "城市级别", required = false, example = "")
    private String cityLevel;

    @ApiModelProperty(value = "排序ID", required = false, example = "")
    private Long orderNo;

    @ApiModelProperty(value = "老城市信息主键ID  老E3SV城市信息表主键ID字段", required = false, example = "")
    private String oldCityId;

    @ApiModelProperty(value = "是否共保城市  1.是，0否", required = false, example = "")
    private String isShareInsureCity;

    @ApiModelProperty(value = "是否限牌城市  0否，1是", required = false, example = "")
    private String isLimitLicenseCity;

    private String oldDpsCityId;

    @ApiModelProperty(value = "时间戳", required = false, example = "")
    private Long mycatOpTime;

    @ApiModelProperty(value = "厂商标识ID", required = false, example = "")
    private String oemId;

    @ApiModelProperty(value = "集团标识ID", required = false, example = "")
    private String groupId;

    @ApiModelProperty(value = "厂商标识", required = false, example = "")
    private String oemCode;

    @ApiModelProperty(value = "集团标识", required = false, example = "")
    private String groupCode;

    @ApiModelProperty(value = "创建人", required = false, example = "")
    private String creator;

    @ApiModelProperty(value = "创建人姓名", required = false, example = "")
    private String createdName;

    @ApiModelProperty(value = "创建时间", required = false, example = "")
    private LocalDateTime createdDate;

    @ApiModelProperty(value = "最后更新人员", required = false, example = "")
    private String modifier;

    @ApiModelProperty(value = "修改人姓名", required = false, example = "")
    private String modifyName;

    @ApiModelProperty(value = "最后更新时间", required = false, example = "")
    private LocalDateTime lastUpdatedDate;

    @ApiModelProperty(value = "是否可用", required = false, example = "")
    private String isEnable;

    @ApiModelProperty(value = "SDP用户ID", required = false, example = "")
    private String sdpUserId;

    @ApiModelProperty(value = "SDP组织ID", required = false, example = "")
    private String sdpOrgId;

    @ApiModelProperty(value = "并发控制字段", required = false, example = "")
    private String updateControlId;

    @ApiModelProperty(value = "扩展字段1", required = false, example = "")
    private String column1;

    @ApiModelProperty(value = "扩展字段2", required = false, example = "")
    private String column2;

    @ApiModelProperty(value = "扩展字段3", required = false, example = "")
    private String column3;

    @ApiModelProperty(value = "扩展字段4", required = false, example = "")
    private String column4;

    @ApiModelProperty(value = "扩展字段5", required = false, example = "")
    private String column5;

    @ApiModelProperty(value = "扩展字段6", required = false, example = "")
    private String column6;

    @ApiModelProperty(value = "扩展字段7", required = false, example = "")
    private String column7;

    @ApiModelProperty(value = "扩展字段8", required = false, example = "")
    private String column8;

    @ApiModelProperty(value = "扩展字段9", required = false, example = "")
    private String column9;

    @ApiModelProperty(value = "扩展字段10", required = false, example = "")
    private String column10;

    private String provinceName;
    
    
    public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(String provinceId) {
        this.provinceId = provinceId;
    }

    public String getSmallAreaId() {
        return smallAreaId;
    }

    public void setSmallAreaId(String smallAreaId) {
        this.smallAreaId = smallAreaId;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityLevel() {
        return cityLevel;
    }

    public void setCityLevel(String cityLevel) {
        this.cityLevel = cityLevel;
    }

    public Long getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public String getOldCityId() {
        return oldCityId;
    }

    public void setOldCityId(String oldCityId) {
        this.oldCityId = oldCityId;
    }

    public String getIsShareInsureCity() {
        return isShareInsureCity;
    }

    public void setIsShareInsureCity(String isShareInsureCity) {
        this.isShareInsureCity = isShareInsureCity;
    }

    public String getIsLimitLicenseCity() {
        return isLimitLicenseCity;
    }

    public void setIsLimitLicenseCity(String isLimitLicenseCity) {
        this.isLimitLicenseCity = isLimitLicenseCity;
    }

    public String getOldDpsCityId() {
        return oldDpsCityId;
    }

    public void setOldDpsCityId(String oldDpsCityId) {
        this.oldDpsCityId = oldDpsCityId;
    }

    public Long getMycatOpTime() {
        return mycatOpTime;
    }

    public void setMycatOpTime(Long mycatOpTime) {
        this.mycatOpTime = mycatOpTime;
    }

    public String getOemId() {
        return oemId;
    }

    public void setOemId(String oemId) {
        this.oemId = oemId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getSdpUserId() {
        return sdpUserId;
    }

    public void setSdpUserId(String sdpUserId) {
        this.sdpUserId = sdpUserId;
    }

    public String getSdpOrgId() {
        return sdpOrgId;
    }

    public void setSdpOrgId(String sdpOrgId) {
        this.sdpOrgId = sdpOrgId;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

}
