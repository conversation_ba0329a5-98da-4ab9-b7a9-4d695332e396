package com.ly.adp.drive.entities.in;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/16
 */
public class TestDriveTransferDTO implements Serializable {

    private static final long serialVersionUID = -686384365486589451L;

    @ApiModelProperty("产品专家ID")
    private String userId;

    @ApiModelProperty("产品专家名称")
    private String empName;

    @ApiModelProperty("试驾单号")
    private String testDriveOrderNo;

    @ApiModelProperty("并发控制单号")
    private String updateControlSheetId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public String getUpdateControlSheetId() {
        return updateControlSheetId;
    }

    public void setUpdateControlSheetId(String updateControlSheetId) {
        this.updateControlSheetId = updateControlSheetId;
    }

    @Override
    public String toString() {
        return "TestDriveTransferDTO{" +
                "userId='" + userId + '\'' +
                ", empName='" + empName + '\'' +
                ", testDriveOrderNo='" + testDriveOrderNo + '\'' +
                ", updateControlSheetId='" + updateControlSheetId + '\'' +
                '}';
    }
}
