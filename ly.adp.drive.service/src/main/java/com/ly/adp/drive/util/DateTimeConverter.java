package com.ly.adp.drive.util;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;

public class DateTimeConverter {

    private static final Map<Class<?>, Function<Object, LocalDateTime>> CONVERTERS = new HashMap<>();

    static {
        CONVERTERS.put(LocalDateTime.class, obj -> (LocalDateTime) obj);
        CONVERTERS.put(String.class, obj -> parseString((String) obj));
        CONVERTERS.put(Long.class, obj -> fromEpochMillis((Long) obj));
        CONVERTERS.put(Integer.class, obj -> fromEpochMillis(((Integer) obj).longValue()));
        CONVERTERS.put(Date.class, obj -> fromDate((Date) obj));
        CONVERTERS.put(LocalDate.class, obj -> ((LocalDate) obj).atStartOfDay());
        CONVERTERS.put(Timestamp.class, obj -> fromTimestamp((Timestamp) obj));
    }

    public static LocalDateTime convert(Object obj) {
        if (obj == null) {
            return null;
        }
        Function<Object, LocalDateTime> converter = CONVERTERS.get(obj.getClass());
        if (converter != null) {
            return converter.apply(obj);
        }
        if (obj instanceof Timestamp) {
            return CONVERTERS.get(Timestamp.class).apply(obj);
        }
        if (obj instanceof Date) {
            return CONVERTERS.get(Date.class).apply(obj);
        }
        throw new IllegalArgumentException("Unsupported type: " + obj.getClass());
    }

    private static LocalDateTime parseString(String str) {
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ISO_LOCAL_DATE_TIME,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        );
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDateTime.parse(str, formatter);
            } catch (DateTimeParseException ignored) {}
        }
        throw new IllegalArgumentException("Unsupported datetime format: " + str);
    }

    private static LocalDateTime fromEpochMillis(long millis) {
        return Instant.ofEpochMilli(millis)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    private static LocalDateTime fromDate(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    private static LocalDateTime fromTimestamp(Timestamp timestamp) {
        return timestamp.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

}