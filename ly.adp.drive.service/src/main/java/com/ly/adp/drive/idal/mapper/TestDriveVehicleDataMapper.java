package com.ly.adp.drive.idal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.adp.drive.entities.TestDriveVehicleData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface TestDriveVehicleDataMapper extends BaseMapper<TestDriveVehicleData> {
    int batchUpdate(@Param("list") List<TestDriveVehicleData> updateList);

    int batchInsert(@Param("list") List<TestDriveVehicleData> insertList);
}
