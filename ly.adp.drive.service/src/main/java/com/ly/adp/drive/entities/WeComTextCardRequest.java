package com.ly.adp.drive.entities;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;

@ApiModel(description = "企业微信消息发送文本卡片请求参数")
public class WeComTextCardRequest {
    
    @ApiModelProperty(value = "指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为\"@all\"，则向该企业应用的全部成员发送")
    private String toUser;

    @ApiModelProperty(value = "消息类型，此时固定为：text")
    private String msgType;

    @ApiModelProperty(value = "企业应用的id，整型。企业内部开发，可在应用的设置页面查看；第三方服务商，可通过接口 获取企业授权信息 获取该参数值")
    private Integer agentId;

    @ApiModelProperty(value = "文本卡片消息")
    private TextCard textCard;

    @ApiModelProperty(value = "文本消息")
    private Text text;

    public String getToUser() {
        return toUser;
    }

    public void setToUser(String toUser) {
        this.toUser = toUser;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public TextCard getTextCard() {
        return textCard;
    }

    public void setTextCard(TextCard textCard) {
        this.textCard = textCard;
    }

    public Text getText() {
        return text;
    }

    public void setText(Text text) {
        this.text = text;
    }

    public Map<String, Object> sendTextCardMsgToMap() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("msgtype", msgType);
        map.put("touser", toUser);
        map.put("textcard", textCard.toMap());
        return map;
    }

    public Map<String, Object> sendTextMsgToMap() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("msgtype", msgType);
        map.put("touser", toUser);
        map.put("text", text.toMap());
        return map;
    }

    public static class Text {
        @ApiModelProperty(value = "消息内容，最长不超过2048个字节，超过将截断（支持id转译）")
        private String content;

        public Text(String content) {
            this.content = content;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("content", content);
            return map;
        }
    }

    public static class TextCard {

        @ApiModelProperty(value = "标题，不超过128个字节，超过会自动截断（支持id转译）")
        private String title;

        @ApiModelProperty(value = "描述，不超过512个字节，超过会自动截断（支持id转译）")
        private String description;

        @ApiModelProperty(value = "点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)")
        private String url;

        @ApiModelProperty(value = "按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。")
        private String btntxt;

        public TextCard() {
        }

        public TextCard(String description, String url) {
            this.title = "今日待处理事项提醒";
            this.description = description;
            this.url = url;
            this.btntxt = "详情";
        }

        public TextCard(String title, String description, String url, String btntxt) {
            this.title = title;
            this.description = description;
            this.url = url;
            this.btntxt = btntxt;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getBtntxt() {
            return btntxt;
        }

        public void setBtntxt(String btntxt) {
            this.btntxt = btntxt;
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("title", title);
            map.put("description", description);
            map.put("url", url);
            map.put("btntxt", btntxt);
            return map;
        }
    }
}