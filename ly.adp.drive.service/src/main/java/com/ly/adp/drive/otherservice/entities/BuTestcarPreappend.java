package com.ly.adp.drive.otherservice.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 试驾车and附件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@TableName("t_usc_bu_testcar_preappend")
public class BuTestcarPreappend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试驾整备附件ID
     */
    @TableId("TEST_CAR_APPEND_ID")
    private String testCarAppendId;

    /**
     * 附件类型
     */
    @TableField("APPEND_TYPE_CODE")
    private String appendTypeCode;

    /**
     * 附件地址
     */
    @TableField("APPEND_URL")
    private String appendUrl;

    /**
     * 试驾车整备ID
     */
    @TableField("RESPONSE_ORDER_ID")
    private String responseOrderId;

    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("CREATED_DATE")
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @TableField("MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("LAST_UPDATED_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GTM+8")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @TableField("IS_ENABLE")
    private String isEnable;

    /**
     * 并发控制字段
     */
    @TableField("UPDATE_CONTROL_ID")
    private String updateControlId;


    public String getTestCarAppendId() {
        return testCarAppendId;
    }

    public void setTestCarAppendId(String testCarAppendId) {
        this.testCarAppendId = testCarAppendId;
    }

    public String getAppendTypeCode() {
        return appendTypeCode;
    }

    public void setAppendTypeCode(String appendTypeCode) {
        this.appendTypeCode = appendTypeCode;
    }

    public String getAppendUrl() {
        return appendUrl;
    }

    public void setAppendUrl(String appendUrl) {
        this.appendUrl = appendUrl;
    }

    public String getResponseOrderId() {
        return responseOrderId;
    }

    public void setResponseOrderId(String responseOrderId) {
        this.responseOrderId = responseOrderId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }

    @Override
    public String toString() {
        return "BuTestcarPreappend{" +
        "testCarAppendId=" + testCarAppendId +
        ", appendTypeCode=" + appendTypeCode +
        ", appendUrl=" + appendUrl +
        ", responseOrderId=" + responseOrderId +
        ", creator=" + creator +
        ", createdDate=" + createdDate +
        ", modifier=" + modifier +
        ", lastUpdatedDate=" + lastUpdatedDate +
        ", isEnable=" + isEnable +
        ", updateControlId=" + updateControlId +
        "}";
    }
}
