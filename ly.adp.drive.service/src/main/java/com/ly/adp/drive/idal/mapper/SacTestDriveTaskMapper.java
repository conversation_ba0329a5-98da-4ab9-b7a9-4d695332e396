package com.ly.adp.drive.idal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

import com.ly.adp.drive.entities.LookupValue;
import com.ly.adp.drive.entities.SacTestDriveTask;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * <p>
 * 试驾任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
public interface SacTestDriveTaskMapper extends BaseMapper<SacTestDriveTask> {
    //试驾任务 信息查询
    List<Map<String, Object>> querySacTestDriveTask(IPage<Map<String, Object>> page, @Param("param") Map<String, Object> paramMap);

    List<Map<String, Object>> sacTestDriveTaskFindListInfo(IPage<Map<String, Object>> page, @Param("param") Map<String, Object> paramMap);

    //试驾任务 信息新增
    int sacTestDriveTaskFindCount(@Param("param") Map<String, Object> paramMap);

    //试驾任务 信息新增
    int querySacTestDriveCt(Map<String, Object> paramMap);

    int createSacTestDriveTask(@Param("param") Map<String, Object> paramMap);

    //试驾任务 信息更新
    int updateSacTestDriveTask(@Param("param") Map<String, Object> paramMap);

    //试驾任务 信息删除
    int deleteSacTestDriveTask(@Param("param") Map<String, Object> paramMap);

    List<Map<String, Object>> locationEmpInfo(IPage<Map<String, Object>> page, @Param("param") Map<String, Object> empInfoMap);

    String findStation(@Param("empId") String empID);

    List<String> findSmallCarType(Map<String, Object> dataInfo);

    List<LookupValue> findLookUpValue(@Param("code") String Code);

    String findSmsUrl();

    String findSmartUrl();

    int insertCspLeadsEvent(@Param("param") Map<String, Object> dataInfo, @Param("dateTz") String dateTz);

    Map<String, Object> searchClueByPhoneAndDlr(@Param("param") Map<String, Object> dataInfo);

    void insertCspLeads(@Param("param") Map<String, Object> dataInfo, String dateTz);

    List<String> findAgentCompany(@Param("orgId") String orgID);

    List<String> findCompany(@Param("orgCode") String orgCode);
}
